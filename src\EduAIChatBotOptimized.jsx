import React, { useState, useEffect, useRef, useCallback, useMemo, Suspense } from "react";
import { getDoc, doc } from 'firebase/firestore';
import { auth, db } from './firebaseConfig';
import axios from "axios";
import { sidebarItems } from './sidebarItems';
import { onAuthStateChanged } from 'firebase/auth';
import { Navbar, Sidebar, Dashboard, Notification } from './components';
import { useResponsive, useSidebar, useOptimizedAnimation } from './hooks/useResponsive';
import { getTheme } from './theme';
import {
  FiFileText, FiCode, FiHelpCircle, FiAward, FiBook, FiShield,
  FiSearch, FiUpload, FiBriefcase, FiBarChart2, FiLayers, FiCheckCircle,
  FiExternalLink, FiX
} from "react-icons/fi";
import { createClient } from '@supabase/supabase-js';
import './App.css';
import './components.css';

// Lazy load heavy components for better performance
const LazyFaq = React.lazy(() => import('./Faq'));
const LazyExams = React.lazy(() => import('./Exams'));
const LazyCoding = React.lazy(() => import('./Coding'));

// Enhanced sidebar items with icons
const updatedSidebarItems = sidebarItems.map(item => {
  const iconMap = {
    "resume": <FiFileText />,
    "dsa": <FiCode />,
    "coding": <FiLayers />,
    "resources": <FiBriefcase />,
    "quizzes": <FiCheckCircle />,
    "aptitude": <FiBarChart2 />,
    "academics": <FiBook />,
    "faq": <FiHelpCircle />,
    "admin": <FiShield />
  };

  return {
    ...item,
    icon: iconMap[item.tab] || iconMap[item.title.toLowerCase()] || <FiAward />
  };
});

const EduAIChatBot = () => {
  // Performance monitoring
  const { isMobile } = useResponsive();
  const { isOpen: sidebarOpen, toggle: toggleSidebar, close: closeSidebar } = useSidebar();
  const { shouldAnimate } = useOptimizedAnimation();

  // Optimized state declarations with proper initial values
  const [input, setInput] = useState("");
  const [messages, setMessages] = useState([]);
  const [userId, setUserId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [knowledge, setKnowledge] = useState("");
  const [activeTab, setActiveTab] = useState("dashboard");
  const [searchTerm, setSearchTerm] = useState("");
  const [expandedMenus, setExpandedMenus] = useState({});
  const [user, setUser] = useState(null);
  const [isDarkMode, setIsDarkMode] = useState(false);

  const [resumeUploadLoading, setResumeUploadLoading] = useState(false);
  const [resumeUrl, setResumeUrl] = useState(null);
  const [resourceUploadLoading, setResourceUploadLoading] = useState(false);
  const [userResources] = useState([]);
  const [allUsers] = useState([]);
  const [adminTab, setAdminTab] = useState('users');
  const [notification, setNotification] = useState(null);
  const [activityLog, setActivityLog] = useState([]);
  const chatEndRef = useRef(null);

  // Enhanced DSA section states
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [favoriteCompanies, setFavoriteCompanies] = useState([]);
  const [recentCompanies, setRecentCompanies] = useState([]);

  // Memoized configurations for better performance
  const supabase = useMemo(() => {
    const SUPABASE_URL = 'https://gziaptswfepiveyylven.supabase.co';
    const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imd6aWFwdHN3ZmVwaXZleXlsdmVuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU2NzczNTYsImV4cCI6MjA2MTI1MzM1Nn0.wmqXZGffrox8E_PuCwbzh4xJEffsvFmZCVcF6WFAX6Q';
    return createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
  }, []);

  const API_KEY = "AIzaSyC6kHWto78QdqHz7Uu9RzEXb443ZO7tG5M";
  const ADMIN_EMAIL = '<EMAIL>';

  // Memoized theme
  const theme = useMemo(() => getTheme(isDarkMode), [isDarkMode]);

  // Memoized company categories
  const companyCategories = useMemo(() => ({
    'FAANG': ['Facebook', 'Apple', 'Amazon', 'Netflix', 'Google'],
    'Big Tech': ['Microsoft', 'Adobe', 'Salesforce', 'Oracle', 'IBM', 'Intel', 'Nvidia'],
    'Startups': ['Stripe', 'Airbnb', 'Uber', 'Lyft', 'DoorDash', 'Instacart', 'Coinbase'],
    'Finance': ['Goldman Sachs', 'JPMorgan', 'Morgan Stanley', 'BlackRock', 'Citadel', 'Two Sigma'],
    'Consulting': ['McKinsey', 'BCG', 'Bain', 'Deloitte', 'Accenture', 'PwC'],
    'E-commerce': ['Amazon', 'eBay', 'Shopify', 'Etsy', 'Wayfair', 'Booking.com'],
    'Gaming': ['Blizzard', 'Epic Games', 'Riot Games', 'Unity', 'Roblox'],
    'Indian': ['TCS', 'Infosys', 'Wipro', 'HCL', 'Flipkart', 'Paytm', 'Zomato', 'Swiggy']
  }), []);

  // Memoized companies list
  const companies = useMemo(() => [
    "Accenture", "Adobe", "Amazon", "Apple", "Google", "Microsoft", "Meta", "Netflix",
    "Tesla", "Uber", "Airbnb", "Stripe", "Spotify", "TCS", "Infosys", "Wipro",
    "Goldman Sachs", "JPMorgan", "Morgan Stanley", "Salesforce", "Oracle", "IBM"
  ], []);

  // Quiz buttons data
  const quizButtons = useMemo(() => [
    {
      title: "OP and CN Quiz",
      description: "Test your knowledge of Operating System and Computer Networks",
      link: "https://opcn.netlify.app",
    },
    {
      title: "OOPs and DBMS Quiz",
      description: "Challenge yourself with oops and dbms",
      link: "https://oopsanddbms.netlify.app/",
    },
    {
      title: "System Design Quiz",
      description: "Test your system design knowledge",
      link: "https://system-design041.netlify.app",
    },
    {
      title: "DSA Quiz",
      description: "Data Structures and Algorithms quiz",
      link: "https://dsa041.netlify.app",
    },
    {
      title: "Web Development Quiz",
      description: "Quiz on Web Development topics",
      link: "https://web-dev041.netlify.app",
    },
  ], []);

  // Optimized callback functions with useCallback
  const showNotification = useCallback((msg, type = 'info') => {
    setNotification({ msg, type });
  }, []);

  const logActivity = useCallback((activity) => {
    setActivityLog(prev => [{
      activity,
      timestamp: new Date().toISOString(),
      user: user?.email || 'Anonymous'
    }, ...prev.slice(0, 99)]); // Keep only last 100 activities
  }, [user?.email]);

  const handleTabChange = useCallback((tab) => {
    setActiveTab(tab);
    logActivity(`Navigated to ${tab}`);
  }, [logActivity]);

  const handleMenuToggle = useCallback((menuTitle) => {
    setExpandedMenus(prev => ({
      ...prev,
      [menuTitle]: !prev[menuTitle]
    }));
  }, []);

  const handleLogout = useCallback(async () => {
    try {
      await supabase.auth.signOut();
      setUser(null);
      showNotification('Logged out successfully!', 'success');
      logActivity('User logged out');
    } catch (error) {
      showNotification('Logout failed', 'error');
    }
  }, [supabase.auth, showNotification, logActivity]);

  // Optimized company handlers with useCallback
  const handleCompanyClick = useCallback((company) => {
    setRecentCompanies(prev => {
      const filtered = prev.filter(c => c !== company);
      return [company, ...filtered].slice(0, 5);
    });

    logActivity(`Viewed ${company} DSA questions`);
    const formattedCompany = company.replace(/\s+/g, '');
    window.open(`/company-dsa/${formattedCompany}.html`, '_blank');
  }, [logActivity]);

  const toggleFavorite = useCallback((company, e) => {
    e.stopPropagation();
    setFavoriteCompanies(prev => {
      if (prev.includes(company)) {
        return prev.filter(c => c !== company);
      } else {
        return [...prev, company];
      }
    });
  }, []);

  // Memoized filtered companies for better performance
  const filteredCompanies = useMemo(() => {
    let filtered = companies;

    if (selectedCategory !== 'all') {
      const categoryCompanies = companyCategories[selectedCategory] || [];
      filtered = filtered.filter(company =>
        categoryCompanies.some(catCompany =>
          company.toLowerCase().includes(catCompany.toLowerCase()) ||
          catCompany.toLowerCase().includes(company.toLowerCase())
        )
      );
    }

    filtered = filtered.filter(company =>
      company.toLowerCase().includes(searchTerm.toLowerCase())
    );

    if (sortBy === 'name') {
      filtered.sort();
    } else if (sortBy === 'favorites') {
      filtered.sort((a, b) => {
        const aFav = favoriteCompanies.includes(a);
        const bFav = favoriteCompanies.includes(b);
        if (aFav && !bFav) return -1;
        if (!aFav && bFav) return 1;
        return a.localeCompare(b);
      });
    }

    return filtered;
  }, [companies, selectedCategory, searchTerm, sortBy, favoriteCompanies, companyCategories]);

  const openQuizLink = useCallback((url) => {
    window.open(url, "_blank");
    logActivity(`Opened quiz: ${url}`);
  }, [logActivity]);

  // Effects
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (user) {
        setUserId(user.uid);
      } else {
        setLoading(false);
      }
    });
    return () => unsubscribe();
  }, []);

  useEffect(() => {
    fetch("/training-data.txt")
      .then((res) => res.text())
      .then((data) => setKnowledge(data))
      .catch((err) => console.error("Failed to load training data:", err));
  }, []);

  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user || null);
    });
    const { data: listener } = supabase.auth.onAuthStateChange((_event, session) => {
      setUser(session?.user || null);
    });
    return () => {
      listener?.subscription.unsubscribe();
    };
  }, [supabase.auth]);

  useEffect(() => {
    if (chatEndRef.current) chatEndRef.current.scrollIntoView({ behavior: 'smooth' });
  }, [messages, loading]);

  // Render optimized component structure
  return (
    <div className={`app-container ${shouldAnimate ? 'smooth-transition' : ''}`} style={{ backgroundColor: theme.background, color: theme.text }}>
      {/* Performance-optimized Navbar */}
      <Navbar
        sidebarOpen={sidebarOpen}
        onToggleSidebar={toggleSidebar}
        user={user}
        onLogout={handleLogout}
        adminEmail={ADMIN_EMAIL}
      />

      {/* Performance-optimized Sidebar */}
      <Sidebar
        isOpen={sidebarOpen}
        onClose={closeSidebar}
        activeTab={activeTab}
        onTabChange={handleTabChange}
        sidebarItems={updatedSidebarItems}
        expandedMenus={expandedMenus}
        onToggleMenu={handleMenuToggle}
        theme={theme}
      />

      {/* Main Content with optimized layout */}
      <main className={`main-content ${sidebarOpen && !isMobile ? 'main-content-with-sidebar' : ''}`}>
        {/* Dashboard Content */}
        {activeTab === "dashboard" && (
          <Suspense fallback={<div className="loading-spinner" style={{ margin: '50px auto' }} />}>
            <Dashboard user={user} onTabChange={handleTabChange} />
          </Suspense>
        )}

        {/* Other tabs with lazy loading */}
        {activeTab === "coding" && (
          <Suspense fallback={<div className="loading-spinner" style={{ margin: '50px auto' }} />}>
            <LazyCoding />
          </Suspense>
        )}
        
        {activeTab === "academics" && (
          <Suspense fallback={<div className="loading-spinner" style={{ margin: '50px auto' }} />}>
            <LazyExams />
          </Suspense>
        )}
        
        {activeTab === "faq" && (
          <Suspense fallback={<div className="loading-spinner" style={{ margin: '50px auto' }} />}>
            <LazyFaq />
          </Suspense>
        )}

        {/* Simple placeholder for other tabs */}
        {(activeTab === "quizzes" || activeTab === "dsa" || activeTab === "resources" || activeTab === "resume") && (
          <div style={{ padding: '24px', textAlign: 'center' }}>
            <h2>Coming Soon</h2>
            <p>This section is being optimized for better performance.</p>
          </div>
        )}
      </main>

      {/* Performance-optimized Notification */}
      <Notification 
        notification={notification} 
        onClose={() => setNotification(null)} 
      />
    </div>
  );
};

export default EduAIChatBot;
