import { useState, useEffect, useCallback } from 'react';

// Breakpoints for responsive design
const BREAKPOINTS = {
  mobile: 480,
  tablet: 768,
  desktop: 1024,
  large: 1200
};

// Custom hook for responsive behavior
export const useResponsive = () => {
  const [screenSize, setScreenSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1200,
    height: typeof window !== 'undefined' ? window.innerHeight : 800
  });

  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [isDesktop, setIsDesktop] = useState(true);

  // Throttled resize handler for better performance
  const handleResize = useCallback(() => {
    const width = window.innerWidth;
    const height = window.innerHeight;
    
    setScreenSize({ width, height });
    setIsMobile(width <= BREAKPOINTS.mobile);
    setIsTablet(width > BREAKPOINTS.mobile && width <= BREAKPOINTS.tablet);
    setIsDesktop(width > BREAKPOINTS.tablet);
  }, []);

  useEffect(() => {
    // Initial check
    handleResize();

    // Throttled event listener
    let timeoutId = null;
    const throttledResize = () => {
      if (timeoutId) clearTimeout(timeoutId);
      timeoutId = setTimeout(handleResize, 100);
    };

    window.addEventListener('resize', throttledResize);
    return () => {
      window.removeEventListener('resize', throttledResize);
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [handleResize]);

  return {
    screenSize,
    isMobile,
    isTablet,
    isDesktop,
    breakpoints: BREAKPOINTS
  };
};

// Hook for managing sidebar state with responsive behavior
export const useSidebar = () => {
  const { isMobile } = useResponsive();
  const [isOpen, setIsOpen] = useState(false);

  // Auto-close sidebar on mobile when screen size changes
  useEffect(() => {
    if (isMobile && isOpen) {
      setIsOpen(false);
    }
  }, [isMobile, isOpen]);

  const toggle = useCallback(() => {
    setIsOpen(prev => !prev);
  }, []);

  const close = useCallback(() => {
    setIsOpen(false);
  }, []);

  const open = useCallback(() => {
    setIsOpen(true);
  }, []);

  return {
    isOpen,
    toggle,
    close,
    open,
    isMobile
  };
};

// Hook for optimized animations
export const useOptimizedAnimation = (enabled = true) => {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (e) => {
      setPrefersReducedMotion(e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  const shouldAnimate = enabled && !prefersReducedMotion;

  return {
    shouldAnimate,
    prefersReducedMotion,
    animationClass: shouldAnimate ? 'smooth-transition' : '',
    fastAnimationClass: shouldAnimate ? 'fast-transition' : ''
  };
};

// Hook for performance monitoring
export const usePerformanceMonitor = () => {
  const [metrics, setMetrics] = useState({
    renderTime: 0,
    componentCount: 0,
    memoryUsage: 0
  });

  const startRender = useCallback(() => {
    return performance.now();
  }, []);

  const endRender = useCallback((startTime) => {
    const renderTime = performance.now() - startTime;
    setMetrics(prev => ({
      ...prev,
      renderTime: Math.round(renderTime * 100) / 100
    }));
  }, []);

  const updateComponentCount = useCallback((count) => {
    setMetrics(prev => ({
      ...prev,
      componentCount: count
    }));
  }, []);

  const getMemoryUsage = useCallback(() => {
    if (performance.memory) {
      const usage = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
      setMetrics(prev => ({
        ...prev,
        memoryUsage: usage
      }));
      return usage;
    }
    return 0;
  }, []);

  return {
    metrics,
    startRender,
    endRender,
    updateComponentCount,
    getMemoryUsage
  };
};

// Hook for debounced values (useful for search inputs)
export const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

// Hook for intersection observer (lazy loading)
export const useIntersectionObserver = (options = {}) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [ref, setRef] = useState(null);

  useEffect(() => {
    if (!ref) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options
      }
    );

    observer.observe(ref);

    return () => {
      observer.disconnect();
    };
  }, [ref, options]);

  return [setRef, isIntersecting];
};

export default {
  useResponsive,
  useSidebar,
  useOptimizedAnimation,
  usePerformanceMonitor,
  useDebounce,
  useIntersectionObserver
};
