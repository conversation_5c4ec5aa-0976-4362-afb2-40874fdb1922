{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\quiz\\\\aich (4)\\\\aich (3)\\\\aich(6)\\\\aich(5)\\\\src\\\\components\\\\Navbar.jsx\";\nimport React, { memo } from 'react';\nimport { FiMenu, FiX, FiLogIn, FiLogOut, FiShield } from 'react-icons/fi';\nimport '../components.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Navbar = /*#__PURE__*/memo(_c = ({\n  sidebarOpen,\n  onToggleSidebar,\n  user,\n  onLogout,\n  adminEmail\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"navbar-fixed\",\n    style: {\n      background: 'linear-gradient(135deg, #7C3AED 0%, #8B5CF6 100%)',\n      boxShadow: '0 2px 10px rgba(124, 58, 237, 0.1)'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"fast-transition\",\n      style: {\n        background: 'none',\n        border: 'none',\n        color: 'white',\n        marginRight: '20px',\n        cursor: 'pointer',\n        padding: '8px',\n        borderRadius: '4px'\n      },\n      onClick: onToggleSidebar,\n      onMouseEnter: e => e.target.style.background = 'rgba(255, 255, 255, 0.1)',\n      onMouseLeave: e => e.target.style.background = 'none',\n      \"aria-label\": sidebarOpen ? 'Close sidebar' : 'Open sidebar',\n      children: sidebarOpen ? /*#__PURE__*/_jsxDEV(FiX, {\n        size: 24\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 24\n      }, this) : /*#__PURE__*/_jsxDEV(FiMenu, {\n        size: 24\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 44\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: 1,\n        display: 'flex',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: require('../eduai-logo.jpg'),\n        alt: \"EduAI Logo\",\n        style: {\n          height: '36px',\n          marginRight: '12px',\n          borderRadius: '50%',\n          border: '2px solid rgba(255, 255, 255, 0.3)'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          \"data-header-title\": true,\n          style: {\n            fontWeight: 600,\n            fontSize: '18px',\n            color: 'white',\n            textShadow: '0 1px 2px rgba(0, 0, 0, 0.1)'\n          },\n          children: \"EDU NOVA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          \"data-header-subtitle\": true,\n          style: {\n            fontSize: '12px',\n            opacity: 0.8,\n            color: 'white',\n            fontWeight: 400\n          },\n          children: \"AI POWERED LEARNING SYSTEM\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '16px'\n      },\n      children: user ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hover-scale fast-transition\",\n          style: {\n            width: '40px',\n            height: '40px',\n            borderRadius: '50%',\n            background: 'rgba(255, 255, 255, 0.2)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            fontWeight: 600,\n            cursor: 'pointer',\n            color: 'white',\n            backdropFilter: 'blur(10px)',\n            border: '1px solid rgba(255, 255, 255, 0.3)'\n          },\n          title: user.email,\n          children: user.email === adminEmail ? /*#__PURE__*/_jsxDEV(FiShield, {\n            size: 20,\n            color: \"#4caf50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 17\n          }, this) : user.email[0].toUpperCase()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn-primary hover-lift\",\n          style: {\n            background: 'rgba(255, 255, 255, 0.2)',\n            color: 'white',\n            border: '1px solid rgba(255, 255, 255, 0.3)',\n            backdropFilter: 'blur(10px)',\n            fontSize: '14px',\n            padding: '8px 16px'\n          },\n          onClick: onLogout,\n          children: [/*#__PURE__*/_jsxDEV(FiLogOut, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 15\n          }, this), \"Logout\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn-primary hover-lift\",\n        style: {\n          background: 'rgba(255, 255, 255, 0.2)',\n          color: 'white',\n          border: '1px solid rgba(255, 255, 255, 0.3)',\n          backdropFilter: 'blur(10px)',\n          fontSize: '14px',\n          padding: '8px 16px'\n        },\n        onClick: () => console.log('Login functionality to be implemented'),\n        children: [/*#__PURE__*/_jsxDEV(FiLogIn, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 13\n        }, this), \"Login\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n});\n_c2 = Navbar;\nNavbar.displayName = 'Navbar';\nexport default Navbar;\nvar _c, _c2;\n$RefreshReg$(_c, \"Navbar$memo\");\n$RefreshReg$(_c2, \"Navbar\");", "map": {"version": 3, "names": ["React", "memo", "FiMenu", "FiX", "FiLogIn", "FiLogOut", "FiShield", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON>", "_c", "sidebarOpen", "onToggleSidebar", "user", "onLogout", "adminEmail", "className", "style", "background", "boxShadow", "children", "border", "color", "marginRight", "cursor", "padding", "borderRadius", "onClick", "onMouseEnter", "e", "target", "onMouseLeave", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flex", "display", "alignItems", "src", "require", "alt", "height", "fontWeight", "fontSize", "textShadow", "opacity", "gap", "width", "justifyContent", "<PERSON><PERSON>ilter", "title", "email", "toUpperCase", "console", "log", "_c2", "displayName", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/src/components/Navbar.jsx"], "sourcesContent": ["import React, { memo } from 'react';\nimport { FiMenu, FiX, FiLogIn, FiLogOut, FiShield } from 'react-icons/fi';\nimport '../components.css';\n\nconst Navbar = memo(({ \n  sidebarOpen, \n  onToggleSidebar, \n  user, \n  onLogout, \n  adminEmail \n}) => {\n  return (\n    <nav className=\"navbar-fixed\" style={{\n      background: 'linear-gradient(135deg, #7C3AED 0%, #8B5CF6 100%)',\n      boxShadow: '0 2px 10px rgba(124, 58, 237, 0.1)'\n    }}>\n      <button\n        className=\"fast-transition\"\n        style={{\n          background: 'none',\n          border: 'none',\n          color: 'white',\n          marginRight: '20px',\n          cursor: 'pointer',\n          padding: '8px',\n          borderRadius: '4px'\n        }}\n        onClick={onToggleSidebar}\n        onMouseEnter={(e) => e.target.style.background = 'rgba(255, 255, 255, 0.1)'}\n        onMouseLeave={(e) => e.target.style.background = 'none'}\n        aria-label={sidebarOpen ? 'Close sidebar' : 'Open sidebar'}\n      >\n        {sidebarOpen ? <FiX size={24} /> : <FiMenu size={24} />}\n      </button>\n\n      <div style={{ flex: 1, display: 'flex', alignItems: 'center' }}>\n        <img\n          src={require('../eduai-logo.jpg')}\n          alt=\"EduAI Logo\"\n          style={{ \n            height: '36px', \n            marginRight: '12px',\n            borderRadius: '50%',\n            border: '2px solid rgba(255, 255, 255, 0.3)'\n          }}\n        />\n        <div>\n          <div\n            data-header-title\n            style={{ \n              fontWeight: 600, \n              fontSize: '18px', \n              color: 'white',\n              textShadow: '0 1px 2px rgba(0, 0, 0, 0.1)'\n            }}\n          >\n            EDU NOVA\n          </div>\n          <div\n            data-header-subtitle\n            style={{ \n              fontSize: '12px', \n              opacity: 0.8, \n              color: 'white',\n              fontWeight: 400\n            }}\n          >\n            AI POWERED LEARNING SYSTEM\n          </div>\n        </div>\n      </div>\n\n      <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>\n        {user ? (\n          <>\n            <div \n              className=\"hover-scale fast-transition\"\n              style={{\n                width: '40px',\n                height: '40px',\n                borderRadius: '50%',\n                background: 'rgba(255, 255, 255, 0.2)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontWeight: 600,\n                cursor: 'pointer',\n                color: 'white',\n                backdropFilter: 'blur(10px)',\n                border: '1px solid rgba(255, 255, 255, 0.3)'\n              }}\n              title={user.email}\n            >\n              {user.email === adminEmail ? (\n                <FiShield size={20} color=\"#4caf50\" />\n              ) : (\n                user.email[0].toUpperCase()\n              )}\n            </div>\n            <button\n              className=\"btn-primary hover-lift\"\n              style={{\n                background: 'rgba(255, 255, 255, 0.2)',\n                color: 'white',\n                border: '1px solid rgba(255, 255, 255, 0.3)',\n                backdropFilter: 'blur(10px)',\n                fontSize: '14px',\n                padding: '8px 16px'\n              }}\n              onClick={onLogout}\n            >\n              <FiLogOut size={16} />\n              Logout\n            </button>\n          </>\n        ) : (\n          <button\n            className=\"btn-primary hover-lift\"\n            style={{\n              background: 'rgba(255, 255, 255, 0.2)',\n              color: 'white',\n              border: '1px solid rgba(255, 255, 255, 0.3)',\n              backdropFilter: 'blur(10px)',\n              fontSize: '14px',\n              padding: '8px 16px'\n            }}\n            onClick={() => console.log('Login functionality to be implemented')}\n          >\n            <FiLogIn size={16} />\n            Login\n          </button>\n        )}\n      </div>\n    </nav>\n  );\n});\n\nNavbar.displayName = 'Navbar';\n\nexport default Navbar;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,IAAI,QAAQ,OAAO;AACnC,SAASC,MAAM,EAAEC,GAAG,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,gBAAgB;AACzE,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3B,MAAMC,MAAM,gBAAGV,IAAI,CAAAW,EAAA,GAACA,CAAC;EACnBC,WAAW;EACXC,eAAe;EACfC,IAAI;EACJC,QAAQ;EACRC;AACF,CAAC,KAAK;EACJ,oBACET,OAAA;IAAKU,SAAS,EAAC,cAAc;IAACC,KAAK,EAAE;MACnCC,UAAU,EAAE,mDAAmD;MAC/DC,SAAS,EAAE;IACb,CAAE;IAAAC,QAAA,gBACAd,OAAA;MACEU,SAAS,EAAC,iBAAiB;MAC3BC,KAAK,EAAE;QACLC,UAAU,EAAE,MAAM;QAClBG,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,OAAO;QACdC,WAAW,EAAE,MAAM;QACnBC,MAAM,EAAE,SAAS;QACjBC,OAAO,EAAE,KAAK;QACdC,YAAY,EAAE;MAChB,CAAE;MACFC,OAAO,EAAEf,eAAgB;MACzBgB,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACb,KAAK,CAACC,UAAU,GAAG,0BAA2B;MAC5Ea,YAAY,EAAGF,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACb,KAAK,CAACC,UAAU,GAAG,MAAO;MACxD,cAAYP,WAAW,GAAG,eAAe,GAAG,cAAe;MAAAS,QAAA,EAE1DT,WAAW,gBAAGL,OAAA,CAACL,GAAG;QAAC+B,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAAG9B,OAAA,CAACN,MAAM;QAACgC,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC,eAET9B,OAAA;MAAKW,KAAK,EAAE;QAAEoB,IAAI,EAAE,CAAC;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAnB,QAAA,gBAC7Dd,OAAA;QACEkC,GAAG,EAAEC,OAAO,CAAC,mBAAmB,CAAE;QAClCC,GAAG,EAAC,YAAY;QAChBzB,KAAK,EAAE;UACL0B,MAAM,EAAE,MAAM;UACdpB,WAAW,EAAE,MAAM;UACnBG,YAAY,EAAE,KAAK;UACnBL,MAAM,EAAE;QACV;MAAE;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACF9B,OAAA;QAAAc,QAAA,gBACEd,OAAA;UACE,yBAAiB;UACjBW,KAAK,EAAE;YACL2B,UAAU,EAAE,GAAG;YACfC,QAAQ,EAAE,MAAM;YAChBvB,KAAK,EAAE,OAAO;YACdwB,UAAU,EAAE;UACd,CAAE;UAAA1B,QAAA,EACH;QAED;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN9B,OAAA;UACE,4BAAoB;UACpBW,KAAK,EAAE;YACL4B,QAAQ,EAAE,MAAM;YAChBE,OAAO,EAAE,GAAG;YACZzB,KAAK,EAAE,OAAO;YACdsB,UAAU,EAAE;UACd,CAAE;UAAAxB,QAAA,EACH;QAED;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN9B,OAAA;MAAKW,KAAK,EAAE;QAAEqB,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAES,GAAG,EAAE;MAAO,CAAE;MAAA5B,QAAA,EAChEP,IAAI,gBACHP,OAAA,CAAAE,SAAA;QAAAY,QAAA,gBACEd,OAAA;UACEU,SAAS,EAAC,6BAA6B;UACvCC,KAAK,EAAE;YACLgC,KAAK,EAAE,MAAM;YACbN,MAAM,EAAE,MAAM;YACdjB,YAAY,EAAE,KAAK;YACnBR,UAAU,EAAE,0BAA0B;YACtCoB,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBW,cAAc,EAAE,QAAQ;YACxBN,UAAU,EAAE,GAAG;YACfpB,MAAM,EAAE,SAAS;YACjBF,KAAK,EAAE,OAAO;YACd6B,cAAc,EAAE,YAAY;YAC5B9B,MAAM,EAAE;UACV,CAAE;UACF+B,KAAK,EAAEvC,IAAI,CAACwC,KAAM;UAAAjC,QAAA,EAEjBP,IAAI,CAACwC,KAAK,KAAKtC,UAAU,gBACxBT,OAAA,CAACF,QAAQ;YAAC4B,IAAI,EAAE,EAAG;YAACV,KAAK,EAAC;UAAS;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAEtCvB,IAAI,CAACwC,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;QAC3B;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACN9B,OAAA;UACEU,SAAS,EAAC,wBAAwB;UAClCC,KAAK,EAAE;YACLC,UAAU,EAAE,0BAA0B;YACtCI,KAAK,EAAE,OAAO;YACdD,MAAM,EAAE,oCAAoC;YAC5C8B,cAAc,EAAE,YAAY;YAC5BN,QAAQ,EAAE,MAAM;YAChBpB,OAAO,EAAE;UACX,CAAE;UACFE,OAAO,EAAEb,QAAS;UAAAM,QAAA,gBAElBd,OAAA,CAACH,QAAQ;YAAC6B,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UAExB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA,eACT,CAAC,gBAEH9B,OAAA;QACEU,SAAS,EAAC,wBAAwB;QAClCC,KAAK,EAAE;UACLC,UAAU,EAAE,0BAA0B;UACtCI,KAAK,EAAE,OAAO;UACdD,MAAM,EAAE,oCAAoC;UAC5C8B,cAAc,EAAE,YAAY;UAC5BN,QAAQ,EAAE,MAAM;UAChBpB,OAAO,EAAE;QACX,CAAE;QACFE,OAAO,EAAEA,CAAA,KAAM4B,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAE;QAAApC,QAAA,gBAEpEd,OAAA,CAACJ,OAAO;UAAC8B,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,SAEvB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IACT;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC,CAAC;AAACqB,GAAA,GAnIGhD,MAAM;AAqIZA,MAAM,CAACiD,WAAW,GAAG,QAAQ;AAE7B,eAAejD,MAAM;AAAC,IAAAC,EAAA,EAAA+C,GAAA;AAAAE,YAAA,CAAAjD,EAAA;AAAAiD,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}