import React, { memo } from 'react';
import { FiMenu, FiX, FiLogIn, FiLogOut, FiShield } from 'react-icons/fi';
import '../components.css';

const Navbar = memo(({ 
  sidebarOpen, 
  onToggleSidebar, 
  user, 
  onLogout, 
  adminEmail 
}) => {
  return (
    <nav className="navbar-fixed" style={{
      background: 'linear-gradient(135deg, #7C3AED 0%, #8B5CF6 100%)',
      boxShadow: '0 2px 10px rgba(124, 58, 237, 0.1)'
    }}>
      <button
        className="fast-transition"
        style={{
          background: 'none',
          border: 'none',
          color: 'white',
          marginRight: '20px',
          cursor: 'pointer',
          padding: '8px',
          borderRadius: '4px'
        }}
        onClick={onToggleSidebar}
        onMouseEnter={(e) => e.target.style.background = 'rgba(255, 255, 255, 0.1)'}
        onMouseLeave={(e) => e.target.style.background = 'none'}
        aria-label={sidebarOpen ? 'Close sidebar' : 'Open sidebar'}
      >
        {sidebarOpen ? <FiX size={24} /> : <FiMenu size={24} />}
      </button>

      <div style={{ flex: 1, display: 'flex', alignItems: 'center' }}>
        <img
          src={require('../eduai-logo.jpg')}
          alt="EduAI Logo"
          style={{ 
            height: '36px', 
            marginRight: '12px',
            borderRadius: '50%',
            border: '2px solid rgba(255, 255, 255, 0.3)'
          }}
        />
        <div>
          <div
            data-header-title
            style={{ 
              fontWeight: 600, 
              fontSize: '18px', 
              color: 'white',
              textShadow: '0 1px 2px rgba(0, 0, 0, 0.1)'
            }}
          >
            EDU NOVA
          </div>
          <div
            data-header-subtitle
            style={{ 
              fontSize: '12px', 
              opacity: 0.8, 
              color: 'white',
              fontWeight: 400
            }}
          >
            AI POWERED LEARNING SYSTEM
          </div>
        </div>
      </div>

      <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
        {user ? (
          <>
            <div 
              className="hover-scale fast-transition"
              style={{
                width: '40px',
                height: '40px',
                borderRadius: '50%',
                background: 'rgba(255, 255, 255, 0.2)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontWeight: 600,
                cursor: 'pointer',
                color: 'white',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255, 255, 255, 0.3)'
              }}
              title={user.email}
            >
              {user.email === adminEmail ? (
                <FiShield size={20} color="#4caf50" />
              ) : (
                user.email[0].toUpperCase()
              )}
            </div>
            <button
              className="btn-primary hover-lift"
              style={{
                background: 'rgba(255, 255, 255, 0.2)',
                color: 'white',
                border: '1px solid rgba(255, 255, 255, 0.3)',
                backdropFilter: 'blur(10px)',
                fontSize: '14px',
                padding: '8px 16px'
              }}
              onClick={onLogout}
            >
              <FiLogOut size={16} />
              Logout
            </button>
          </>
        ) : (
          <button
            className="btn-primary hover-lift"
            style={{
              background: 'rgba(255, 255, 255, 0.2)',
              color: 'white',
              border: '1px solid rgba(255, 255, 255, 0.3)',
              backdropFilter: 'blur(10px)',
              fontSize: '14px',
              padding: '8px 16px'
            }}
            onClick={() => console.log('Login functionality to be implemented')}
          >
            <FiLogIn size={16} />
            Login
          </button>
        )}
      </div>
    </nav>
  );
});

Navbar.displayName = 'Navbar';

export default Navbar;
