{"ast": null, "code": "// Optimized theme configuration\nexport const lightTheme = {\n  primary: '#7C3AED',\n  primaryLight: '#8B5CF6',\n  primaryDark: '#6D28D9',\n  secondary: '#EC4899',\n  accent: '#F59E0B',\n  // Surfaces\n  background: '#FFFFFF',\n  surface: '#F8FAFC',\n  surfaceLight: '#FFFFFF',\n  surfaceDark: '#F1F5F9',\n  // Text colors\n  text: '#1E293B',\n  textLight: '#64748B',\n  textMuted: '#94A3B8',\n  textInverse: '#FFFFFF',\n  // Borders and dividers\n  border: '#E2E8F0',\n  borderLight: '#F1F5F9',\n  borderDark: '#CBD5E1',\n  // Status colors\n  success: '#10B981',\n  warning: '#F59E0B',\n  error: '#EF4444',\n  info: '#3B82F6',\n  // Shadows\n  shadow: 'rgba(0, 0, 0, 0.1)',\n  shadowLight: 'rgba(0, 0, 0, 0.05)',\n  shadowDark: 'rgba(0, 0, 0, 0.15)',\n  // Gradients\n  gradients: {\n    primary: 'linear-gradient(135deg, #7C3AED 0%, #8B5CF6 100%)',\n    secondary: 'linear-gradient(135deg, #EC4899 0%, #F472B6 100%)',\n    accent: 'linear-gradient(135deg, #F59E0B 0%, #FBBF24 100%)',\n    success: 'linear-gradient(135deg, #10B981 0%, #34D399 100%)',\n    error: 'linear-gradient(135deg, #EF4444 0%, #F87171 100%)',\n    info: 'linear-gradient(135deg, #3B82F6 0%, #60A5FA 100%)',\n    glass: 'linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.1))'\n  }\n};\nexport const darkTheme = {\n  primary: '#8B5CF6',\n  primaryLight: '#A78BFA',\n  primaryDark: '#7C3AED',\n  secondary: '#F472B6',\n  accent: '#FBBF24',\n  // Surfaces\n  background: '#0F172A',\n  surface: '#1E293B',\n  surfaceLight: '#334155',\n  surfaceDark: '#0F172A',\n  // Text colors\n  text: '#F8FAFC',\n  textLight: '#CBD5E1',\n  textMuted: '#94A3B8',\n  textInverse: '#1E293B',\n  // Borders and dividers\n  border: '#334155',\n  borderLight: '#475569',\n  borderDark: '#1E293B',\n  // Status colors\n  success: '#34D399',\n  warning: '#FBBF24',\n  error: '#F87171',\n  info: '#60A5FA',\n  // Shadows\n  shadow: 'rgba(0, 0, 0, 0.3)',\n  shadowLight: 'rgba(0, 0, 0, 0.2)',\n  shadowDark: 'rgba(0, 0, 0, 0.4)',\n  // Gradients\n  gradients: {\n    primary: 'linear-gradient(135deg, #8B5CF6 0%, #A78BFA 100%)',\n    secondary: 'linear-gradient(135deg, #F472B6 0%, #FBBF24 100%)',\n    accent: 'linear-gradient(135deg, #FBBF24 0%, #FCD34D 100%)',\n    success: 'linear-gradient(135deg, #34D399 0%, #6EE7B7 100%)',\n    error: 'linear-gradient(135deg, #F87171 0%, #FCA5A5 100%)',\n    info: 'linear-gradient(135deg, #60A5FA 0%, #93C5FD 100%)',\n    glass: 'linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))'\n  }\n};\n\n// Responsive breakpoints\nexport const breakpoints = {\n  mobile: '480px',\n  tablet: '768px',\n  desktop: '1024px',\n  large: '1200px',\n  xlarge: '1400px'\n};\n\n// Animation configurations\nexport const animations = {\n  fast: '0.15s',\n  normal: '0.3s',\n  slow: '0.5s',\n  easing: {\n    ease: 'ease',\n    easeIn: 'ease-in',\n    easeOut: 'ease-out',\n    easeInOut: 'ease-in-out',\n    smooth: 'cubic-bezier(0.4, 0, 0.2, 1)',\n    bounce: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)'\n  }\n};\n\n// Z-index scale\nexport const zIndex = {\n  dropdown: 1000,\n  sticky: 1020,\n  fixed: 1030,\n  modalBackdrop: 1040,\n  modal: 1050,\n  popover: 1060,\n  tooltip: 1070,\n  notification: 2000\n};\n\n// Component sizes\nexport const sizes = {\n  navbar: {\n    height: '64px'\n  },\n  sidebar: {\n    width: '280px',\n    collapsedWidth: '64px'\n  },\n  button: {\n    small: {\n      padding: '8px 16px',\n      fontSize: '14px'\n    },\n    medium: {\n      padding: '12px 24px',\n      fontSize: '16px'\n    },\n    large: {\n      padding: '16px 32px',\n      fontSize: '18px'\n    }\n  },\n  input: {\n    small: {\n      padding: '8px 12px',\n      fontSize: '14px'\n    },\n    medium: {\n      padding: '12px 16px',\n      fontSize: '16px'\n    },\n    large: {\n      padding: '16px 20px',\n      fontSize: '18px'\n    }\n  }\n};\n\n// Utility functions\nexport const getTheme = (isDark = false) => isDark ? darkTheme : lightTheme;\nexport const createGlassEffect = (theme, opacity = 0.1) => ({\n  background: `rgba(255, 255, 255, ${opacity})`,\n  backdropFilter: 'blur(20px)',\n  border: `1px solid rgba(255, 255, 255, ${opacity * 2})`,\n  boxShadow: `0 8px 32px ${theme.shadow}`\n});\nexport const createHoverEffect = (theme, scale = 1.02) => ({\n  transform: `translateY(-2px) scale(${scale})`,\n  boxShadow: `0 8px 25px ${theme.shadow}`\n});\nexport const createFocusEffect = theme => ({\n  outline: 'none',\n  boxShadow: `0 0 0 3px ${theme.primary}40`\n});\nexport default {\n  lightTheme,\n  darkTheme,\n  breakpoints,\n  animations,\n  zIndex,\n  sizes,\n  getTheme,\n  createGlassEffect,\n  createHoverEffect,\n  createFocusEffect\n};", "map": {"version": 3, "names": ["lightTheme", "primary", "primaryLight", "primaryDark", "secondary", "accent", "background", "surface", "surfaceLight", "surfaceDark", "text", "textLight", "textMuted", "textInverse", "border", "borderLight", "borderDark", "success", "warning", "error", "info", "shadow", "shadowLight", "shadowDark", "gradients", "glass", "darkTheme", "breakpoints", "mobile", "tablet", "desktop", "large", "xlarge", "animations", "fast", "normal", "slow", "easing", "ease", "easeIn", "easeOut", "easeInOut", "smooth", "bounce", "zIndex", "dropdown", "sticky", "fixed", "modalBackdrop", "modal", "popover", "tooltip", "notification", "sizes", "navbar", "height", "sidebar", "width", "collapsedWidth", "button", "small", "padding", "fontSize", "medium", "input", "getTheme", "isDark", "createGlassEffect", "theme", "opacity", "<PERSON><PERSON>ilter", "boxShadow", "createHoverEffect", "scale", "transform", "createFocusEffect", "outline"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/src/theme/index.js"], "sourcesContent": ["// Optimized theme configuration\nexport const lightTheme = {\n  primary: '#7C3AED',\n  primaryLight: '#8B5CF6',\n  primaryDark: '#6D28D9',\n  secondary: '#EC4899',\n  accent: '#F59E0B',\n  \n  // Surfaces\n  background: '#FFFFFF',\n  surface: '#F8FAFC',\n  surfaceLight: '#FFFFFF',\n  surfaceDark: '#F1F5F9',\n  \n  // Text colors\n  text: '#1E293B',\n  textLight: '#64748B',\n  textMuted: '#94A3B8',\n  textInverse: '#FFFFFF',\n  \n  // Borders and dividers\n  border: '#E2E8F0',\n  borderLight: '#F1F5F9',\n  borderDark: '#CBD5E1',\n  \n  // Status colors\n  success: '#10B981',\n  warning: '#F59E0B',\n  error: '#EF4444',\n  info: '#3B82F6',\n  \n  // Shadows\n  shadow: 'rgba(0, 0, 0, 0.1)',\n  shadowLight: 'rgba(0, 0, 0, 0.05)',\n  shadowDark: 'rgba(0, 0, 0, 0.15)',\n  \n  // Gradients\n  gradients: {\n    primary: 'linear-gradient(135deg, #7C3AED 0%, #8B5CF6 100%)',\n    secondary: 'linear-gradient(135deg, #EC4899 0%, #F472B6 100%)',\n    accent: 'linear-gradient(135deg, #F59E0B 0%, #FBBF24 100%)',\n    success: 'linear-gradient(135deg, #10B981 0%, #34D399 100%)',\n    error: 'linear-gradient(135deg, #EF4444 0%, #F87171 100%)',\n    info: 'linear-gradient(135deg, #3B82F6 0%, #60A5FA 100%)',\n    glass: 'linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.1))'\n  }\n};\n\nexport const darkTheme = {\n  primary: '#8B5CF6',\n  primaryLight: '#A78BFA',\n  primaryDark: '#7C3AED',\n  secondary: '#F472B6',\n  accent: '#FBBF24',\n  \n  // Surfaces\n  background: '#0F172A',\n  surface: '#1E293B',\n  surfaceLight: '#334155',\n  surfaceDark: '#0F172A',\n  \n  // Text colors\n  text: '#F8FAFC',\n  textLight: '#CBD5E1',\n  textMuted: '#94A3B8',\n  textInverse: '#1E293B',\n  \n  // Borders and dividers\n  border: '#334155',\n  borderLight: '#475569',\n  borderDark: '#1E293B',\n  \n  // Status colors\n  success: '#34D399',\n  warning: '#FBBF24',\n  error: '#F87171',\n  info: '#60A5FA',\n  \n  // Shadows\n  shadow: 'rgba(0, 0, 0, 0.3)',\n  shadowLight: 'rgba(0, 0, 0, 0.2)',\n  shadowDark: 'rgba(0, 0, 0, 0.4)',\n  \n  // Gradients\n  gradients: {\n    primary: 'linear-gradient(135deg, #8B5CF6 0%, #A78BFA 100%)',\n    secondary: 'linear-gradient(135deg, #F472B6 0%, #FBBF24 100%)',\n    accent: 'linear-gradient(135deg, #FBBF24 0%, #FCD34D 100%)',\n    success: 'linear-gradient(135deg, #34D399 0%, #6EE7B7 100%)',\n    error: 'linear-gradient(135deg, #F87171 0%, #FCA5A5 100%)',\n    info: 'linear-gradient(135deg, #60A5FA 0%, #93C5FD 100%)',\n    glass: 'linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))'\n  }\n};\n\n// Responsive breakpoints\nexport const breakpoints = {\n  mobile: '480px',\n  tablet: '768px',\n  desktop: '1024px',\n  large: '1200px',\n  xlarge: '1400px'\n};\n\n// Animation configurations\nexport const animations = {\n  fast: '0.15s',\n  normal: '0.3s',\n  slow: '0.5s',\n  \n  easing: {\n    ease: 'ease',\n    easeIn: 'ease-in',\n    easeOut: 'ease-out',\n    easeInOut: 'ease-in-out',\n    smooth: 'cubic-bezier(0.4, 0, 0.2, 1)',\n    bounce: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)'\n  }\n};\n\n// Z-index scale\nexport const zIndex = {\n  dropdown: 1000,\n  sticky: 1020,\n  fixed: 1030,\n  modalBackdrop: 1040,\n  modal: 1050,\n  popover: 1060,\n  tooltip: 1070,\n  notification: 2000\n};\n\n// Component sizes\nexport const sizes = {\n  navbar: {\n    height: '64px'\n  },\n  sidebar: {\n    width: '280px',\n    collapsedWidth: '64px'\n  },\n  button: {\n    small: {\n      padding: '8px 16px',\n      fontSize: '14px'\n    },\n    medium: {\n      padding: '12px 24px',\n      fontSize: '16px'\n    },\n    large: {\n      padding: '16px 32px',\n      fontSize: '18px'\n    }\n  },\n  input: {\n    small: {\n      padding: '8px 12px',\n      fontSize: '14px'\n    },\n    medium: {\n      padding: '12px 16px',\n      fontSize: '16px'\n    },\n    large: {\n      padding: '16px 20px',\n      fontSize: '18px'\n    }\n  }\n};\n\n// Utility functions\nexport const getTheme = (isDark = false) => isDark ? darkTheme : lightTheme;\n\nexport const createGlassEffect = (theme, opacity = 0.1) => ({\n  background: `rgba(255, 255, 255, ${opacity})`,\n  backdropFilter: 'blur(20px)',\n  border: `1px solid rgba(255, 255, 255, ${opacity * 2})`,\n  boxShadow: `0 8px 32px ${theme.shadow}`\n});\n\nexport const createHoverEffect = (theme, scale = 1.02) => ({\n  transform: `translateY(-2px) scale(${scale})`,\n  boxShadow: `0 8px 25px ${theme.shadow}`\n});\n\nexport const createFocusEffect = (theme) => ({\n  outline: 'none',\n  boxShadow: `0 0 0 3px ${theme.primary}40`\n});\n\nexport default {\n  lightTheme,\n  darkTheme,\n  breakpoints,\n  animations,\n  zIndex,\n  sizes,\n  getTheme,\n  createGlassEffect,\n  createHoverEffect,\n  createFocusEffect\n};\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,UAAU,GAAG;EACxBC,OAAO,EAAE,SAAS;EAClBC,YAAY,EAAE,SAAS;EACvBC,WAAW,EAAE,SAAS;EACtBC,SAAS,EAAE,SAAS;EACpBC,MAAM,EAAE,SAAS;EAEjB;EACAC,UAAU,EAAE,SAAS;EACrBC,OAAO,EAAE,SAAS;EAClBC,YAAY,EAAE,SAAS;EACvBC,WAAW,EAAE,SAAS;EAEtB;EACAC,IAAI,EAAE,SAAS;EACfC,SAAS,EAAE,SAAS;EACpBC,SAAS,EAAE,SAAS;EACpBC,WAAW,EAAE,SAAS;EAEtB;EACAC,MAAM,EAAE,SAAS;EACjBC,WAAW,EAAE,SAAS;EACtBC,UAAU,EAAE,SAAS;EAErB;EACAC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,SAAS;EAClBC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAE,SAAS;EAEf;EACAC,MAAM,EAAE,oBAAoB;EAC5BC,WAAW,EAAE,qBAAqB;EAClCC,UAAU,EAAE,qBAAqB;EAEjC;EACAC,SAAS,EAAE;IACTvB,OAAO,EAAE,mDAAmD;IAC5DG,SAAS,EAAE,mDAAmD;IAC9DC,MAAM,EAAE,mDAAmD;IAC3DY,OAAO,EAAE,mDAAmD;IAC5DE,KAAK,EAAE,mDAAmD;IAC1DC,IAAI,EAAE,mDAAmD;IACzDK,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMC,SAAS,GAAG;EACvBzB,OAAO,EAAE,SAAS;EAClBC,YAAY,EAAE,SAAS;EACvBC,WAAW,EAAE,SAAS;EACtBC,SAAS,EAAE,SAAS;EACpBC,MAAM,EAAE,SAAS;EAEjB;EACAC,UAAU,EAAE,SAAS;EACrBC,OAAO,EAAE,SAAS;EAClBC,YAAY,EAAE,SAAS;EACvBC,WAAW,EAAE,SAAS;EAEtB;EACAC,IAAI,EAAE,SAAS;EACfC,SAAS,EAAE,SAAS;EACpBC,SAAS,EAAE,SAAS;EACpBC,WAAW,EAAE,SAAS;EAEtB;EACAC,MAAM,EAAE,SAAS;EACjBC,WAAW,EAAE,SAAS;EACtBC,UAAU,EAAE,SAAS;EAErB;EACAC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,SAAS;EAClBC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAE,SAAS;EAEf;EACAC,MAAM,EAAE,oBAAoB;EAC5BC,WAAW,EAAE,oBAAoB;EACjCC,UAAU,EAAE,oBAAoB;EAEhC;EACAC,SAAS,EAAE;IACTvB,OAAO,EAAE,mDAAmD;IAC5DG,SAAS,EAAE,mDAAmD;IAC9DC,MAAM,EAAE,mDAAmD;IAC3DY,OAAO,EAAE,mDAAmD;IAC5DE,KAAK,EAAE,mDAAmD;IAC1DC,IAAI,EAAE,mDAAmD;IACzDK,KAAK,EAAE;EACT;AACF,CAAC;;AAED;AACA,OAAO,MAAME,WAAW,GAAG;EACzBC,MAAM,EAAE,OAAO;EACfC,MAAM,EAAE,OAAO;EACfC,OAAO,EAAE,QAAQ;EACjBC,KAAK,EAAE,QAAQ;EACfC,MAAM,EAAE;AACV,CAAC;;AAED;AACA,OAAO,MAAMC,UAAU,GAAG;EACxBC,IAAI,EAAE,OAAO;EACbC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE,MAAM;EAEZC,MAAM,EAAE;IACNC,IAAI,EAAE,MAAM;IACZC,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,aAAa;IACxBC,MAAM,EAAE,8BAA8B;IACtCC,MAAM,EAAE;EACV;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,MAAM,GAAG;EACpBC,QAAQ,EAAE,IAAI;EACdC,MAAM,EAAE,IAAI;EACZC,KAAK,EAAE,IAAI;EACXC,aAAa,EAAE,IAAI;EACnBC,KAAK,EAAE,IAAI;EACXC,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE,IAAI;EACbC,YAAY,EAAE;AAChB,CAAC;;AAED;AACA,OAAO,MAAMC,KAAK,GAAG;EACnBC,MAAM,EAAE;IACNC,MAAM,EAAE;EACV,CAAC;EACDC,OAAO,EAAE;IACPC,KAAK,EAAE,OAAO;IACdC,cAAc,EAAE;EAClB,CAAC;EACDC,MAAM,EAAE;IACNC,KAAK,EAAE;MACLC,OAAO,EAAE,UAAU;MACnBC,QAAQ,EAAE;IACZ,CAAC;IACDC,MAAM,EAAE;MACNF,OAAO,EAAE,WAAW;MACpBC,QAAQ,EAAE;IACZ,CAAC;IACD/B,KAAK,EAAE;MACL8B,OAAO,EAAE,WAAW;MACpBC,QAAQ,EAAE;IACZ;EACF,CAAC;EACDE,KAAK,EAAE;IACLJ,KAAK,EAAE;MACLC,OAAO,EAAE,UAAU;MACnBC,QAAQ,EAAE;IACZ,CAAC;IACDC,MAAM,EAAE;MACNF,OAAO,EAAE,WAAW;MACpBC,QAAQ,EAAE;IACZ,CAAC;IACD/B,KAAK,EAAE;MACL8B,OAAO,EAAE,WAAW;MACpBC,QAAQ,EAAE;IACZ;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMG,QAAQ,GAAGA,CAACC,MAAM,GAAG,KAAK,KAAKA,MAAM,GAAGxC,SAAS,GAAG1B,UAAU;AAE3E,OAAO,MAAMmE,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,OAAO,GAAG,GAAG,MAAM;EAC1D/D,UAAU,EAAE,uBAAuB+D,OAAO,GAAG;EAC7CC,cAAc,EAAE,YAAY;EAC5BxD,MAAM,EAAE,iCAAiCuD,OAAO,GAAG,CAAC,GAAG;EACvDE,SAAS,EAAE,cAAcH,KAAK,CAAC/C,MAAM;AACvC,CAAC,CAAC;AAEF,OAAO,MAAMmD,iBAAiB,GAAGA,CAACJ,KAAK,EAAEK,KAAK,GAAG,IAAI,MAAM;EACzDC,SAAS,EAAE,0BAA0BD,KAAK,GAAG;EAC7CF,SAAS,EAAE,cAAcH,KAAK,CAAC/C,MAAM;AACvC,CAAC,CAAC;AAEF,OAAO,MAAMsD,iBAAiB,GAAIP,KAAK,KAAM;EAC3CQ,OAAO,EAAE,MAAM;EACfL,SAAS,EAAE,aAAaH,KAAK,CAACnE,OAAO;AACvC,CAAC,CAAC;AAEF,eAAe;EACbD,UAAU;EACV0B,SAAS;EACTC,WAAW;EACXM,UAAU;EACVW,MAAM;EACNS,KAAK;EACLY,QAAQ;EACRE,iBAAiB;EACjBK,iBAAiB;EACjBG;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}