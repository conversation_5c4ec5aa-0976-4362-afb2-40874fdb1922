{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\quiz\\\\aich (4)\\\\aich (3)\\\\aich(6)\\\\aich(5)\\\\src\\\\EduAIChatBot.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef, useCallback, useMemo, Suspense } from \"react\";\nimport { getDoc, doc } from 'firebase/firestore';\nimport { auth, db } from './firebaseConfig';\nimport axios from \"axios\";\nimport { sidebarItems } from './sidebarItems';\nimport { onAuthStateChanged } from 'firebase/auth';\nimport { Navbar, Sidebar, Dashboard, Notification } from './components';\nimport { useResponsive, useSidebar, useOptimizedAnimation, usePerformanceMonitor } from './hooks/useResponsive';\nimport { getTheme } from './theme';\nimport { FiFileText, FiCode, FiHelpCircle, FiAward, FiBook, <PERSON><PERSON>ser, FiShield, FiSearch, FiUpload, FiBriefcase, FiBarChart2, FiLayers, FiCheckCircle, FiExternalLink, FiHeart, FiClock, FiRefreshCw } from \"react-icons/fi\";\nimport { createClient } from '@supabase/supabase-js';\nimport './App.css';\nimport './components.css';\n\n// Lazy load heavy components for better performance\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LazyFaq = /*#__PURE__*/React.lazy(_c = () => import('./Faq'));\n_c2 = LazyFaq;\nconst LazyExams = /*#__PURE__*/React.lazy(_c3 = () => import('./Exams'));\n_c4 = LazyExams;\nconst LazyCoding = /*#__PURE__*/React.lazy(_c5 = () => import('./Coding'));\n\n// Chart.js lazy loading with proper registration\n_c6 = LazyCoding;\nconst LazyChart = /*#__PURE__*/React.lazy(_c7 = async () => {\n  const {\n    Bar\n  } = await import('react-chartjs-2');\n  const {\n    Chart,\n    BarElement,\n    CategoryScale,\n    LinearScale,\n    Tooltip,\n    Legend\n  } = await import('chart.js');\n  Chart.register(BarElement, CategoryScale, LinearScale, Tooltip, Legend);\n  return {\n    default: Bar\n  };\n});\n_c8 = LazyChart;\nconst LazyMarkdown = /*#__PURE__*/React.lazy(_c9 = () => import('react-markdown'));\n\n// Enhanced sidebar items with icons\n_c0 = LazyMarkdown;\nconst updatedSidebarItems = sidebarItems.map(item => {\n  const iconMap = {\n    \"resume\": /*#__PURE__*/_jsxDEV(FiFileText, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 15\n    }, this),\n    \"dsa\": /*#__PURE__*/_jsxDEV(FiCode, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 12\n    }, this),\n    \"coding\": /*#__PURE__*/_jsxDEV(FiLayers, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 15\n    }, this),\n    \"resources\": /*#__PURE__*/_jsxDEV(FiBriefcase, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 18\n    }, this),\n    \"quizzes\": /*#__PURE__*/_jsxDEV(FiCheckCircle, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 16\n    }, this),\n    \"aptitude\": /*#__PURE__*/_jsxDEV(FiBarChart2, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 17\n    }, this),\n    \"academics\": /*#__PURE__*/_jsxDEV(FiBook, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 18\n    }, this),\n    \"faq\": /*#__PURE__*/_jsxDEV(FiHelpCircle, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 12\n    }, this),\n    \"admin\": /*#__PURE__*/_jsxDEV(FiShield, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 14\n    }, this)\n  };\n  return {\n    ...item,\n    icon: iconMap[item.tab] || iconMap[item.title.toLowerCase()] || /*#__PURE__*/_jsxDEV(FiAward, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 69\n    }, this)\n  };\n});\nconst EduAIChatBot = () => {\n  _s();\n  // Performance monitoring\n  const {\n    metrics,\n    startRender,\n    endRender\n  } = usePerformanceMonitor();\n  const {\n    isMobile\n  } = useResponsive();\n  const {\n    isOpen: sidebarOpen,\n    toggle: toggleSidebar,\n    close: closeSidebar\n  } = useSidebar();\n  const {\n    shouldAnimate\n  } = useOptimizedAnimation();\n\n  // Optimized state declarations with proper initial values\n  const [input, setInput] = useState(\"\");\n  const [messages, setMessages] = useState([]);\n  const [userId, setUserId] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [knowledge, setKnowledge] = useState(\"\");\n  const [activeTab, setActiveTab] = useState(\"dashboard\");\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [expandedMenus, setExpandedMenus] = useState({});\n  const [user, setUser] = useState(null);\n  const [isDarkMode, setIsDarkMode] = useState(false);\n  const [resumeUploadLoading, setResumeUploadLoading] = useState(false);\n  const [resumeUrl, setResumeUrl] = useState(null);\n  const [resourceUploadLoading, setResourceUploadLoading] = useState(false);\n  const [userResources] = useState([]);\n  const [allUsers] = useState([]);\n  const [adminTab, setAdminTab] = useState('users');\n  const [notification, setNotification] = useState(null);\n  const [activityLog, setActivityLog] = useState([]);\n  const chatEndRef = useRef(null);\n\n  // Enhanced DSA section states\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [sortBy, setSortBy] = useState('name');\n  const [favoriteCompanies, setFavoriteCompanies] = useState([]);\n  const [recentCompanies, setRecentCompanies] = useState([]);\n  const [showRevertButton, setShowRevertButton] = useState(true);\n\n  // Memoized configurations for better performance\n  const supabase = useMemo(() => {\n    const SUPABASE_URL = 'https://gziaptswfepiveyylven.supabase.co';\n    const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imd6aWFwdHN3ZmVwaXZleXlsdmVuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU2NzczNTYsImV4cCI6MjA2MTI1MzM1Nn0.wmqXZGffrox8E_PuCwbzh4xJEffsvFmZCVcF6WFAX6Q';\n    return createClient(SUPABASE_URL, SUPABASE_ANON_KEY);\n  }, []);\n  const API_KEY = \"AIzaSyC6kHWto78QdqHz7Uu9RzEXb443ZO7tG5M\";\n  const ADMIN_EMAIL = '<EMAIL>';\n\n  // Memoized theme\n  const theme = useMemo(() => getTheme(isDarkMode), [isDarkMode]);\n\n  // Company categories for enhanced DSA section\n  const companyCategories = {\n    'FAANG': ['Facebook', 'Apple', 'Amazon', 'Netflix', 'Google'],\n    'Big Tech': ['Microsoft', 'Adobe', 'Salesforce', 'Oracle', 'IBM', 'Intel', 'Nvidia'],\n    'Startups': ['Stripe', 'Airbnb', 'Uber', 'Lyft', 'DoorDash', 'Instacart', 'Coinbase'],\n    'Finance': ['Goldman Sachs', 'JPMorgan', 'Morgan Stanley', 'BlackRock', 'Citadel', 'Two Sigma'],\n    'Consulting': ['McKinsey', 'BCG', 'Bain', 'Deloitte', 'Accenture', 'PwC'],\n    'E-commerce': ['Amazon', 'eBay', 'Shopify', 'Etsy', 'Wayfair', 'Booking.com'],\n    'Gaming': ['Blizzard', 'Epic Games', 'Riot Games', 'Unity', 'Roblox'],\n    'Indian': ['TCS', 'Infosys', 'Wipro', 'HCL', 'Flipkart', 'Paytm', 'Zomato', 'Swiggy']\n  };\n\n  // Complete list of companies\n  const companies = [\"Accenture\", \"Accolite\", \"Adobe\", \"Affirm\", \"Agoda\", \"Airbnb\", \"Airtel\", \"Akamar\", \"Akuna Capital\", \"Alibaba\", \"Altimetrik\", \"Amazon\", \"AMD\", \"Amdocs\", \"American Express\", \"Anduril\", \"Apple\", \"Arista Networks\", \"Arcesium\", \"Atlassian\", \"Attentive\", \"athenahealth\", \"Autodesk\", \"Avito\", \"Baidu\", \"Barclays\", \"BitGo\", \"BlackRock\", \"Blizzard\", \"Block\", \"Bloomberg\", \"BNY Mellon\", \"Boft\", \"Booking.com\", \"Bos\", \"BP\", \"ByteDance\", \"Cadence\", \"Capgemini\", \"Capital One\", \"CARS24\", \"carwale\", \"Cashfree\", \"Chewy\", \"Cisco\", \"Citadel\", \"Citrix\", \"Cloudera\", \"Cloudflare\", \"Cognizant\", \"Coinbase\", \"Commvault\", \"Confluent\", \"Coupang\", \"Coursera\", \"CrowdStrike\", \"Cruise\", \"Curefit\", \"Databricks\", \"Datadog\", \"DE Shaw\", \"Deloitte\", \"Dell\", \"Deliveroo\", \"Derantior\", \"Deutsche Bank\", \"Devflev\", \"Directi\", \"Disney\", \"Docusign\", \"DoorDash\", \"Dream11\", \"Dropbox\", \"DRW\", \"Dunzo\", \"eBay\", \"EPAM Systems\", \"Epic Systems\", \"Expedia\", \"FactSet\", \"Flexport\", \"Flipkart\", \"Freshworks\", \"GE Healthcare\", \"Geico\", \"Goldman Sachs\", \"Google\", \"Grab\", \"Grammarly\", \"Graviton\", \"Groww\", \"GSN Games\", \"Hashedin\", \"HCL\", \"HPE\", \"Hubspot\", \"Hudson River Trading\", \"Huawei\", \"IBM\", \"IMC\", \"Indeed\", \"Infosys\", \"InMobi\", \"Intel\", \"Intuit\", \"JPMorgan\", \"Jane Street\", \"Josh Technology\", \"Jump Trading\", \"Juspay\", \"Karat\", \"KLA\", \"LinkedIn\", \"LiveRamp\", \"Lowe's\", \"Lucid\", \"Lyft\", \"MakeMyTrip\", \"Mastercard\", \"MathWorks\", \"Media.net\", \"Meesho\", \"Mercari\", \"Meta\", \"Microsoft\", \"Millennium\", \"Mitsogo\", \"Moloco\", \"MongoDB\", \"Morgan Stanley\", \"Moveworks\", \"Myntra\", \"Nagarro\", \"NetApp\", \"Netflix\", \"Nextdoor\", \"Nielsen\", \"Nike\", \"Niantic\", \"Nordstrom\", \"Nutanix\", \"Nvidia\", \"Okta\", \"OKX\", \"OpenAI\", \"OpenText\", \"Oracle\", \"Otter.ai\", \"Oyo\", \"Ozon\", \"Palantir Technologies\", \"Palo Alto Networks\", \"PayPal\", \"Paytm\", \"Persistent Systems\", \"PhonePe\", \"Pinterest\", \"Pocket Gems\", \"Point72\", \"Pure Storage\", \"Qualcomm\", \"Quora\", \"Rakuten\", \"Razorpay\", \"RBC\", \"Reddit\", \"Revolut\", \"Robinhood\", \"Roblox\", \"Rubrik\", \"Salesforce\", \"Samsung\", \"SAP\", \"ServiceNow\", \"Shopify\", \"Siemens\", \"Sigmoid\", \"SIG\", \"Snowflake\", \"Snap\", \"Sofi\", \"Splunk\", \"Spotify\", \"Sprinklr\", \"Squarepoint Capital\", \"Stripe\", \"Swiggy\", \"TCS\", \"Tekion\", \"Tencent\", \"Tesla\", \"ThoughtSpot\", \"ThoughtWorks\", \"TikTok\", \"Tinkoff\", \"Trilogy\", \"Turing\", \"Turo\", \"Twilio\", \"Twitch\", \"Two Sigma\", \"Uber\", \"UiPath\", \"UKG\", \"Veeva Systems\", \"Verily\", \"Verkada\", \"Virtu Financial\", \"Visa\", \"VK\", \"VMware\", \"Walmart Labs\", \"WarnerMedia\", \"Wayfair\", \"Wells Fargo\", \"Wipro\", \"Wix\", \"Workday\", \"X\", \"Yahoo\", \"Yandex\", \"Yelp\", \"Zalando\", \"Zenefits\", \"Zepto\", \"Zeta\", \"Zillow\", \"Zoho\", \"Zomato\", \"ZScaler\", \"Zopsmart\"];\n\n  // Quiz buttons data\n  const quizButtons = [{\n    title: \"OP and CN Quiz\",\n    description: \"Test your knowledge of Operating System and Computer Networks\",\n    link: \"https://opcn.netlify.app\"\n  }, {\n    title: \"OOPs and DBMS Quiz\",\n    description: \"Challenge yourself with oops and dbms\",\n    link: \"https://oopsanddbms.netlify.app/\"\n  }, {\n    title: \"System Design Quiz\",\n    description: \"Test your system design knowledge\",\n    link: \"https://system-design041.netlify.app\"\n  }, {\n    title: \"Quantitative Aptitude and Reasoning Quiz\",\n    description: \"Practice common quant and reasoning questions\",\n    link: \"https://quantandreasoning.netlify.app\"\n  }, {\n    title: \"Cloud & DevOps Quiz\",\n    description: \"Test your knowledge of Cloud and DevOps concepts\",\n    link: \"https://cloud-devops.netlify.app\"\n  }, {\n    title: \"DSA Quiz\",\n    description: \"Data Structures and Algorithms quiz\",\n    link: \"https://dsa041.netlify.app\"\n  }, {\n    title: \"Operating System & Computer Networks Quiz\",\n    description: \"Quiz on OS and Computer Networks\",\n    link: \"https://opcn.netlify.app\"\n  }, {\n    title: \"Web Development Quiz\",\n    description: \"Quiz on Web Development topics\",\n    link: \"https://web-dev041.netlify.app\"\n  }];\n\n  // Optimized callback functions with useCallback\n  const showNotification = useCallback((msg, type = 'info') => {\n    setNotification({\n      msg,\n      type\n    });\n  }, []);\n  const logActivity = useCallback(activity => {\n    setActivityLog(prev => [{\n      activity,\n      timestamp: new Date().toISOString(),\n      user: (user === null || user === void 0 ? void 0 : user.email) || 'Anonymous'\n    }, ...prev.slice(0, 99)]); // Keep only last 100 activities\n  }, [user === null || user === void 0 ? void 0 : user.email]);\n  const handleTabChange = useCallback(tab => {\n    const renderStart = startRender();\n    setActiveTab(tab);\n    logActivity(`Navigated to ${tab}`);\n    endRender(renderStart);\n  }, [startRender, endRender, logActivity]);\n  const handleMenuToggle = useCallback(menuTitle => {\n    setExpandedMenus(prev => ({\n      ...prev,\n      [menuTitle]: !prev[menuTitle]\n    }));\n  }, []);\n  const handleLogout = useCallback(async () => {\n    try {\n      await supabase.auth.signOut();\n      setUser(null);\n      showNotification('Logged out successfully!', 'success');\n      logActivity('User logged out');\n    } catch (error) {\n      showNotification('Logout failed', 'error');\n    }\n  }, [supabase.auth, showNotification, logActivity]);\n\n  // Fetch user profile\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, user => {\n      if (user) {\n        setUserId(user.uid);\n      } else {\n        console.log('User is not authenticated');\n        setLoading(false);\n      }\n    });\n    return () => unsubscribe();\n  }, []);\n  useEffect(() => {\n    if (userId) {\n      const fetchUserProfile = async () => {\n        const userRef = doc(db, \"users\", userId);\n        const userDoc = await getDoc(userRef);\n        if (userDoc.exists()) {\n          const userData = userDoc.data();\n          // Profile pic functionality can be added later\n          console.log(\"User data loaded:\", userData.dp);\n        } else {\n          console.log(\"No such user!\");\n        }\n        setLoading(false);\n      };\n      fetchUserProfile();\n    }\n  }, [userId]);\n\n  // Fetch training data\n  useEffect(() => {\n    fetch(\"/training-data.txt\").then(res => res.text()).then(data => setKnowledge(data)).catch(err => console.error(\"Failed to load training data:\", err));\n  }, []);\n\n  // Supabase auth state\n  useEffect(() => {\n    supabase.auth.getSession().then(({\n      data: {\n        session\n      }\n    }) => {\n      setUser((session === null || session === void 0 ? void 0 : session.user) || null);\n    });\n    const {\n      data: listener\n    } = supabase.auth.onAuthStateChange((_event, session) => {\n      setUser((session === null || session === void 0 ? void 0 : session.user) || null);\n    });\n    return () => {\n      listener === null || listener === void 0 ? void 0 : listener.subscription.unsubscribe();\n    };\n  }, [supabase.auth]);\n\n  // Handle resume upload\n  const handleResumeUpload = async e => {\n    const file = e.target.files[0];\n    if (!file || !user) return;\n    setResumeUploadLoading(true);\n    const filePath = `${user.id}/${file.name}`;\n    const {\n      error\n    } = await supabase.storage.from('resumes').upload(filePath, file, {\n      upsert: true\n    });\n    if (!error) {\n      const {\n        data: urlData\n      } = supabase.storage.from('resumes').getPublicUrl(filePath);\n      setResumeUrl(urlData.publicUrl);\n      showNotification('Resume uploaded successfully!', 'success');\n      logActivity('Uploaded a resume');\n    } else {\n      showNotification('Resume upload failed.', 'error');\n    }\n    setResumeUploadLoading(false);\n  };\n\n  // Handle resource upload\n  const handleResourceUpload = async e => {\n    const file = e.target.files[0];\n    if (!file || !user) return;\n    setResourceUploadLoading(true);\n    const filePath = `${user.id}/${file.name}`;\n    const {\n      error\n    } = await supabase.storage.from('resources').upload(filePath, file, {\n      upsert: true\n    });\n    if (!error) {\n      showNotification('Resource uploaded!', 'success');\n      logActivity(`Uploaded resource: ${file.name}`);\n    } else {\n      showNotification('Resource upload failed.', 'error');\n    }\n    setResourceUploadLoading(false);\n  };\n\n  // Optimized company handlers with useCallback\n  const handleCompanyClick = useCallback(company => {\n    // Add to recent companies\n    setRecentCompanies(prev => {\n      const filtered = prev.filter(c => c !== company);\n      return [company, ...filtered].slice(0, 5); // Keep only 5 recent\n    });\n    logActivity(`Viewed ${company} DSA questions`);\n    if (company.toLowerCase() === 'microsoft') {\n      window.open('/company-dsa/Microsoft_questions.html', '_blank');\n      return;\n    }\n    const formattedCompany = company.replace(/\\s+/g, '');\n    window.open(`/company-dsa/${formattedCompany}.html`, '_blank');\n  }, [logActivity]);\n\n  // Toggle favorite company\n  const toggleFavorite = useCallback((company, e) => {\n    e.stopPropagation(); // Prevent company click\n    setFavoriteCompanies(prev => {\n      if (prev.includes(company)) {\n        return prev.filter(c => c !== company);\n      } else {\n        return [...prev, company];\n      }\n    });\n  }, []);\n\n  // Revert header color changes\n  const revertHeaderChanges = () => {\n    setShowRevertButton(false);\n    showNotification('Header text color reverted to theme default!', 'success');\n\n    // Actually revert the header text color by updating the DOM\n    const eduNovaElement = document.querySelector('[data-header-title]');\n    const subtitleElement = document.querySelector('[data-header-subtitle]');\n    if (eduNovaElement) {\n      eduNovaElement.style.color = '#333';\n    }\n    if (subtitleElement) {\n      subtitleElement.style.color = '#333';\n    }\n  };\n\n  // Memoized filtered companies for better performance\n  const filteredCompanies = useMemo(() => {\n    let filtered = companies;\n\n    // Filter by category\n    if (selectedCategory !== 'all') {\n      const categoryCompanies = companyCategories[selectedCategory] || [];\n      filtered = filtered.filter(company => categoryCompanies.some(catCompany => company.toLowerCase().includes(catCompany.toLowerCase()) || catCompany.toLowerCase().includes(company.toLowerCase())));\n    }\n\n    // Filter by search term\n    filtered = filtered.filter(company => company.toLowerCase().includes(searchTerm.toLowerCase()));\n\n    // Sort companies\n    if (sortBy === 'name') {\n      filtered.sort();\n    } else if (sortBy === 'favorites') {\n      filtered.sort((a, b) => {\n        const aFav = favoriteCompanies.includes(a);\n        const bFav = favoriteCompanies.includes(b);\n        if (aFav && !bFav) return -1;\n        if (!aFav && bFav) return 1;\n        return a.localeCompare(b);\n      });\n    }\n    return filtered;\n  }, [companies, selectedCategory, searchTerm, sortBy, favoriteCompanies, companyCategories]);\n\n  // Optimized quiz link handler\n  const openQuizLink = useCallback(url => {\n    window.open(url, \"_blank\");\n    logActivity(`Opened quiz: ${url}`);\n  }, [logActivity]);\n\n  // Optimized send message function with useCallback\n  const sendMessage = useCallback(async () => {\n    if (!input.trim()) return;\n    const userMessage = {\n      role: \"user\",\n      content: input\n    };\n    setMessages(prev => [...prev, userMessage]);\n    const currentInput = input;\n    setInput(\"\");\n    setLoading(true);\n    try {\n      var _res$data$candidates, _res$data$candidates$, _res$data$candidates$2, _res$data$candidates$3, _res$data$candidates$4;\n      const prompt = `You are a resume assistant. Help users improve their resumes, provide suggestions, and answer career-related questions. Use the following knowledge if it helps. If it's not relevant, use your own intelligence.\\n\\nKnowledge:\\n${knowledge}\\n\\nQuestion: ${currentInput}`;\n      const res = await axios.post(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEY}`, {\n        contents: [{\n          parts: [{\n            text: prompt\n          }]\n        }]\n      }, {\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        timeout: 30000 // 30 second timeout\n      });\n      const botReply = ((_res$data$candidates = res.data.candidates) === null || _res$data$candidates === void 0 ? void 0 : (_res$data$candidates$ = _res$data$candidates[0]) === null || _res$data$candidates$ === void 0 ? void 0 : (_res$data$candidates$2 = _res$data$candidates$.content) === null || _res$data$candidates$2 === void 0 ? void 0 : (_res$data$candidates$3 = _res$data$candidates$2.parts) === null || _res$data$candidates$3 === void 0 ? void 0 : (_res$data$candidates$4 = _res$data$candidates$3[0]) === null || _res$data$candidates$4 === void 0 ? void 0 : _res$data$candidates$4.text) || \"⚠ No response received.\";\n      const botMessage = {\n        role: \"bot\",\n        content: botReply\n      };\n      setMessages(prev => [...prev, botMessage]);\n      logActivity(`Chat message sent: ${currentInput.substring(0, 50)}...`);\n    } catch (error) {\n      console.error(\"Gemini API Error:\", error);\n      setMessages(prev => [...prev, {\n        role: \"bot\",\n        content: \"❌ Error: \" + error.message\n      }]);\n      showNotification('Failed to send message', 'error');\n    } finally {\n      setLoading(false);\n    }\n  }, [input, knowledge, API_KEY, logActivity, showNotification]);\n\n  // Authentication functionality can be added later if needed\n\n  // Auto-scroll chat\n  useEffect(() => {\n    if (chatEndRef.current) chatEndRef.current.scrollIntoView({\n      behavior: 'smooth'\n    });\n  }, [messages, loading]);\n\n  // Chart data\n  const getLast7Days = () => {\n    const days = [];\n    for (let i = 6; i >= 0; i--) {\n      const d = new Date();\n      d.setDate(d.getDate() - i);\n      days.push(d.toLocaleDateString());\n    }\n    return days;\n  };\n  const chartLabels = getLast7Days();\n  const chartData = {\n    labels: chartLabels,\n    datasets: [{\n      label: 'Resource Uploads',\n      data: chartLabels.map(day => activityLog.filter(a => a.type === 'activity' && a.msg.startsWith('Uploaded resource') && new Date(a.date).toLocaleDateString() === day).length),\n      backgroundColor: '#3182ce'\n    }, {\n      label: 'Coding Practice',\n      data: chartLabels.map(day => activityLog.filter(a => a.type === 'activity' && a.msg === 'Clicked coding practice link' && new Date(a.date).toLocaleDateString() === day).length),\n      backgroundColor: '#805ad5'\n    }]\n  };\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'top'\n      },\n      tooltip: {\n        enabled: true\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          stepSize: 1\n        }\n      }\n    }\n  };\n\n  // Render optimized component structure\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `app-container ${shouldAnimate ? 'smooth-transition' : ''}`,\n    style: {\n      backgroundColor: theme.background,\n      color: theme.text\n    },\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {\n      sidebarOpen: sidebarOpen,\n      onToggleSidebar: toggleSidebar,\n      user: user,\n      onLogout: handleLogout,\n      adminEmail: ADMIN_EMAIL\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 517,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Sidebar, {\n      isOpen: sidebarOpen,\n      onClose: closeSidebar,\n      activeTab: activeTab,\n      onTabChange: handleTabChange,\n      sidebarItems: updatedSidebarItems,\n      expandedMenus: expandedMenus,\n      onToggleMenu: handleMenuToggle,\n      theme: theme\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 526,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: `main-content ${sidebarOpen && !isMobile ? 'main-content-with-sidebar' : ''}`,\n      children: [activeTab === \"dashboard\" && /*#__PURE__*/_jsxDEV(Suspense, {\n        fallback: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\",\n          style: {\n            margin: '50px auto'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 31\n        }, this),\n        children: /*#__PURE__*/_jsxDEV(Dashboard, {\n          user: user,\n          onTabChange: handleTabChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 541,\n        columnNumber: 11\n      }, this), activeTab === \"quizzes\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px',\n          backgroundColor: theme.background\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          style: {\n            backgroundColor: theme.surface,\n            border: `1px solid ${theme.border}`\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              marginTop: 0,\n              color: theme.text\n            },\n            children: \"Career Quizzes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 550,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              opacity: 0.8,\n              marginBottom: '24px',\n              color: theme.textLight\n            },\n            children: \"Test your knowledge with our career-focused quizzes!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 551,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',\n              gap: '16px'\n            },\n            children: quizButtons.map((quiz, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"quiz-card hover-lift\",\n              style: {\n                backgroundColor: theme.surface,\n                border: `1px solid ${theme.border}`,\n                borderRadius: '12px',\n                padding: '20px',\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                cursor: 'pointer'\n              },\n              onClick: () => openQuizLink(quiz.link),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  style: {\n                    margin: '0 0 8px 0',\n                    color: theme.text\n                  },\n                  children: quiz.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 577,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '14px',\n                    opacity: 0.8,\n                    color: theme.textLight\n                  },\n                  children: quiz.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 578,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: theme.primary\n                },\n                children: /*#__PURE__*/_jsxDEV(FiExternalLink, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 555,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 549,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 548,\n        columnNumber: 11\n      }, this), activeTab === \"dsa\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px',\n          backgroundColor: theme.background,\n          minHeight: '100vh',\n          position: 'relative',\n          overflow: 'hidden'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: '10%',\n            left: '5%',\n            width: '300px',\n            height: '300px',\n            background: 'linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))',\n            borderRadius: '50%',\n            animation: 'float 6s ease-in-out infinite',\n            zIndex: 0\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 607,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: '60%',\n            right: '10%',\n            width: '200px',\n            height: '200px',\n            background: 'linear-gradient(45deg, rgba(255,255,255,0.08), rgba(255,255,255,0.03))',\n            borderRadius: '50%',\n            animation: 'float 8s ease-in-out infinite reverse',\n            zIndex: 0\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 618,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            bottom: '20%',\n            left: '15%',\n            width: '150px',\n            height: '150px',\n            background: 'linear-gradient(45deg, rgba(255,255,255,0.06), rgba(255,255,255,0.02))',\n            borderRadius: '50%',\n            animation: 'float 10s ease-in-out infinite',\n            zIndex: 0\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 629,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            maxWidth: '1400px',\n            margin: '0 auto',\n            position: 'relative',\n            zIndex: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.1))',\n              backdropFilter: 'blur(20px)',\n              borderRadius: '25px',\n              padding: '3rem',\n              marginBottom: '2rem',\n              color: 'white',\n              position: 'relative',\n              overflow: 'hidden',\n              border: '1px solid rgba(255,255,255,0.2)',\n              boxShadow: '0 25px 50px rgba(0,0,0,0.2)'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'absolute',\n                top: '20px',\n                right: '20px',\n                fontSize: '2rem',\n                animation: 'bounce 2s infinite'\n              },\n              children: \"\\uD83D\\uDE80\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 657,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'absolute',\n                bottom: '20px',\n                left: '20px',\n                fontSize: '1.5rem',\n                animation: 'bounce 3s infinite'\n              },\n              children: \"\\u2B50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 664,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'absolute',\n                top: '50%',\n                right: '10%',\n                fontSize: '1.2rem',\n                animation: 'bounce 4s infinite'\n              },\n              children: \"\\uD83D\\uDC8E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 671,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '2rem',\n                position: 'relative',\n                zIndex: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '120px',\n                  height: '120px',\n                  borderRadius: '50%',\n                  background: 'linear-gradient(135deg, #ff6b6b, #feca57)',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  fontSize: '3rem',\n                  fontWeight: 'bold',\n                  border: '4px solid rgba(255, 255, 255, 0.3)',\n                  boxShadow: '0 15px 35px rgba(0,0,0,0.2)',\n                  animation: 'pulse 3s infinite'\n                },\n                children: user ? user.email[0].toUpperCase() : '👤'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 680,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '3.5rem',\n                    fontWeight: 800,\n                    marginBottom: '1rem',\n                    background: 'linear-gradient(45deg, #fff, #f0f0f0)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '0 2px 4px rgba(0,0,0,0.3)'\n                  },\n                  children: [\"Hey \", user ? user.email.split('@')[0] : 'Champion', \"! \\uD83C\\uDFAF\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 697,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '1.3rem',\n                    opacity: 0.95,\n                    fontWeight: 500\n                  },\n                  children: \"Time to level up your skills and dominate your goals! \\uD83D\\uDCAA\\u2728\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 709,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: '1rem',\n                    display: 'flex',\n                    gap: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      background: 'rgba(255,255,255,0.2)',\n                      padding: '0.5rem 1rem',\n                      borderRadius: '20px',\n                      fontSize: '0.9rem',\n                      fontWeight: 600\n                    },\n                    children: \"\\uD83D\\uDD25 12 Day Streak\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 722,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      background: 'rgba(255,255,255,0.2)',\n                      padding: '0.5rem 1rem',\n                      borderRadius: '20px',\n                      fontSize: '0.9rem',\n                      fontWeight: 600\n                    },\n                    children: \"\\uD83C\\uDFC6 Level 15\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 731,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 717,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 696,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 644,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n              gap: '1.5rem',\n              marginBottom: '2rem'\n            },\n            children: [{\n              title: 'FIRE STREAK',\n              value: '12',\n              unit: 'DAYS',\n              icon: '🔥',\n              color: '#ff4757',\n              bgGradient: 'linear-gradient(135deg, #ff4757, #ff6b7a)',\n              glowColor: '#ff4757',\n              description: 'Unstoppable momentum!'\n            }, {\n              title: 'SKILL POINTS',\n              value: '2,847',\n              unit: 'XP',\n              icon: '⚡',\n              color: '#3742fa',\n              bgGradient: 'linear-gradient(135deg, #3742fa, #5352ed)',\n              glowColor: '#3742fa',\n              description: 'Level up achieved!'\n            }, {\n              title: 'POWER LEVEL',\n              value: '47',\n              unit: 'HOURS',\n              icon: '💪',\n              color: '#2ed573',\n              bgGradient: 'linear-gradient(135deg, #2ed573, #7bed9f)',\n              glowColor: '#2ed573',\n              description: 'Training complete!'\n            }, {\n              title: 'ACHIEVEMENTS',\n              value: '15',\n              unit: 'UNLOCKED',\n              icon: '🏆',\n              color: '#ffa502',\n              bgGradient: 'linear-gradient(135deg, #ffa502, #ffb142)',\n              glowColor: '#ffa502',\n              description: 'Champion status!'\n            }].map((stat, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: 'linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.1))',\n                backdropFilter: 'blur(20px)',\n                borderRadius: '20px',\n                padding: '2rem',\n                position: 'relative',\n                overflow: 'hidden',\n                border: '1px solid rgba(255,255,255,0.2)',\n                cursor: 'pointer',\n                transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',\n                boxShadow: `0 15px 35px rgba(0,0,0,0.1), 0 0 0 1px ${stat.glowColor}20`\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.transform = 'translateY(-10px) scale(1.02)';\n                e.currentTarget.style.boxShadow = `0 25px 50px rgba(0,0,0,0.2), 0 0 30px ${stat.glowColor}40`;\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.transform = 'translateY(0) scale(1)';\n                e.currentTarget.style.boxShadow = `0 15px 35px rgba(0,0,0,0.1), 0 0 0 1px ${stat.glowColor}20`;\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: '-50%',\n                  left: '-50%',\n                  width: '200%',\n                  height: '200%',\n                  background: `conic-gradient(from 0deg, transparent, ${stat.color}20, transparent)`,\n                  animation: 'rotate 20s linear infinite',\n                  opacity: 0.3\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 816,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: '10px',\n                  right: '10px',\n                  width: '80px',\n                  height: '80px',\n                  background: stat.bgGradient,\n                  borderRadius: '50%',\n                  opacity: 0.2,\n                  filter: 'blur(20px)'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 828,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'relative',\n                  zIndex: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    marginBottom: '1.5rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.8rem',\n                      fontWeight: 800,\n                      color: 'rgba(255,255,255,0.8)',\n                      letterSpacing: '2px'\n                    },\n                    children: stat.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 848,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '2rem',\n                      filter: 'drop-shadow(0 0 10px currentColor)'\n                    },\n                    children: stat.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 856,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 842,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'baseline',\n                    gap: '0.5rem',\n                    marginBottom: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '3rem',\n                      fontWeight: 900,\n                      color: 'white',\n                      textShadow: `0 0 20px ${stat.color}`,\n                      lineHeight: 1\n                    },\n                    children: stat.value\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 871,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.9rem',\n                      fontWeight: 600,\n                      color: stat.color,\n                      textTransform: 'uppercase',\n                      letterSpacing: '1px'\n                    },\n                    children: stat.unit\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 880,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 865,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '0.9rem',\n                    color: 'rgba(255,255,255,0.9)',\n                    fontWeight: 500,\n                    fontStyle: 'italic'\n                  },\n                  children: stat.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 892,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: '1rem',\n                    height: '4px',\n                    background: 'rgba(255,255,255,0.2)',\n                    borderRadius: '2px',\n                    overflow: 'hidden'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      height: '100%',\n                      width: `${Math.min(100, (index + 1) * 25)}%`,\n                      background: stat.bgGradient,\n                      borderRadius: '2px',\n                      animation: 'slideIn 2s ease-out'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 909,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 902,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 840,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 794,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 746,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.1))',\n              backdropFilter: 'blur(20px)',\n              borderRadius: '25px',\n              padding: '2rem',\n              marginBottom: '2rem',\n              border: '1px solid rgba(255,255,255,0.2)',\n              boxShadow: '0 25px 50px rgba(0,0,0,0.2)',\n              position: 'relative',\n              overflow: 'hidden'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'absolute',\n                top: '-100px',\n                right: '-100px',\n                width: '300px',\n                height: '300px',\n                background: 'conic-gradient(from 0deg, #ff4757, #3742fa, #2ed573, #ffa502, #ff4757)',\n                borderRadius: '50%',\n                opacity: 0.1,\n                animation: 'rotate 30s linear infinite'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 934,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              style: {\n                margin: '0 0 2rem 0',\n                fontSize: '2rem',\n                fontWeight: 800,\n                color: 'white',\n                textAlign: 'center',\n                textShadow: '0 2px 4px rgba(0,0,0,0.3)'\n              },\n              children: \"\\uD83C\\uDFAE MISSION CONTROL CENTER \\uD83C\\uDFAE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 946,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                gap: '1.5rem'\n              },\n              children: [{\n                icon: '🎯',\n                title: 'BATTLE MODE',\n                subtitle: 'Take Quiz Challenge',\n                desc: 'Test your skills in epic battles!',\n                action: () => setActiveTab('quizzes'),\n                color: '#ff4757',\n                bgGradient: 'linear-gradient(135deg, #ff4757, #ff6b7a)'\n              }, {\n                icon: '⚔️',\n                title: 'CODE ARENA',\n                subtitle: 'DSA Combat Zone',\n                desc: 'Sharpen your coding weapons!',\n                action: () => setActiveTab('dsa'),\n                color: '#3742fa',\n                bgGradient: 'linear-gradient(135deg, #3742fa, #5352ed)'\n              }, {\n                icon: '📜',\n                title: 'SCROLL REVIEW',\n                subtitle: 'Resume Enhancement',\n                desc: 'Upgrade your legendary resume!',\n                action: () => setActiveTab('resume'),\n                color: '#2ed573',\n                bgGradient: 'linear-gradient(135deg, #2ed573, #7bed9f)'\n              }, {\n                icon: '�',\n                title: 'KNOWLEDGE VAULT',\n                subtitle: 'Study Materials',\n                desc: 'Access ancient wisdom scrolls!',\n                action: () => setActiveTab('resources'),\n                color: '#ffa502',\n                bgGradient: 'linear-gradient(135deg, #ffa502, #ffb142)'\n              }].map((action, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: action.action,\n                style: {\n                  background: 'linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.05))',\n                  backdropFilter: 'blur(15px)',\n                  borderRadius: '20px',\n                  padding: '1.5rem',\n                  cursor: 'pointer',\n                  transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',\n                  border: `2px solid ${action.color}30`,\n                  position: 'relative',\n                  overflow: 'hidden',\n                  boxShadow: `0 10px 30px rgba(0,0,0,0.1), 0 0 0 1px ${action.color}20`\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.transform = 'translateY(-8px) scale(1.05)';\n                  e.currentTarget.style.boxShadow = `0 20px 40px rgba(0,0,0,0.2), 0 0 30px ${action.color}40`;\n                  e.currentTarget.style.borderColor = action.color + '80';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.transform = 'translateY(0) scale(1)';\n                  e.currentTarget.style.boxShadow = `0 10px 30px rgba(0,0,0,0.1), 0 0 0 1px ${action.color}20`;\n                  e.currentTarget.style.borderColor = action.color + '30';\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'absolute',\n                    top: '50%',\n                    left: '50%',\n                    width: '100px',\n                    height: '100px',\n                    background: action.bgGradient,\n                    borderRadius: '50%',\n                    transform: 'translate(-50%, -50%)',\n                    opacity: 0.1,\n                    filter: 'blur(30px)',\n                    animation: 'pulse 3s ease-in-out infinite'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1026,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'relative',\n                    zIndex: 1,\n                    textAlign: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '3rem',\n                      marginBottom: '1rem',\n                      filter: 'drop-shadow(0 0 10px currentColor)',\n                      animation: 'bounce 2s ease-in-out infinite'\n                    },\n                    children: action.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1041,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '1.1rem',\n                      fontWeight: 800,\n                      color: 'white',\n                      marginBottom: '0.5rem',\n                      textShadow: `0 0 10px ${action.color}`,\n                      letterSpacing: '1px'\n                    },\n                    children: action.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1050,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.8rem',\n                      color: action.color,\n                      fontWeight: 600,\n                      marginBottom: '0.75rem',\n                      textTransform: 'uppercase',\n                      letterSpacing: '0.5px'\n                    },\n                    children: action.subtitle\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1061,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.85rem',\n                      color: 'rgba(255,255,255,0.8)',\n                      fontStyle: 'italic',\n                      lineHeight: 1.4\n                    },\n                    children: action.desc\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1072,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginTop: '1rem',\n                      height: '3px',\n                      background: 'rgba(255,255,255,0.2)',\n                      borderRadius: '2px',\n                      overflow: 'hidden'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        height: '100%',\n                        width: `${75 + index * 5}%`,\n                        background: action.bgGradient,\n                        borderRadius: '2px',\n                        animation: 'slideIn 1.5s ease-out'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1089,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1082,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1040,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1000,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 957,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 923,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: '1fr 1fr',\n              gap: '2rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: globalStyles.currentTheme.surface,\n                borderRadius: '16px',\n                padding: '1.5rem',\n                boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n                border: `1px solid ${globalStyles.currentTheme.border}`\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: '0 0 1.5rem 0',\n                  fontSize: '1.3rem',\n                  fontWeight: 600,\n                  color: globalStyles.currentTheme.text\n                },\n                children: \"\\uD83D\\uDCC8 Recent Activity\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1118,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  maxHeight: '250px',\n                  overflowY: 'auto'\n                },\n                children: activityLog.slice(0, 5).map((log, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '1rem',\n                    padding: '0.75rem',\n                    borderRadius: '8px',\n                    marginBottom: '0.5rem',\n                    background: index % 2 === 0 ? globalStyles.currentTheme.secondary : 'transparent',\n                    transition: 'all 0.3s ease'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '10px',\n                      height: '10px',\n                      borderRadius: '50%',\n                      background: log.type === 'login' ? '#4ECDC4' : '#45B7D1',\n                      flexShrink: 0\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1142,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '0.9rem',\n                        fontWeight: 500,\n                        color: globalStyles.currentTheme.text,\n                        marginBottom: '0.2rem'\n                      },\n                      children: log.msg\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1150,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '0.8rem',\n                        color: globalStyles.currentTheme.textLight\n                      },\n                      children: new Date(log.date).toLocaleString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1158,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1149,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1132,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1127,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1111,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: globalStyles.currentTheme.surface,\n                borderRadius: '16px',\n                padding: '1.5rem',\n                boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n                border: `1px solid ${globalStyles.currentTheme.border}`\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: '0 0 1.5rem 0',\n                  fontSize: '1.3rem',\n                  fontWeight: 600,\n                  color: globalStyles.currentTheme.text\n                },\n                children: \"\\uD83D\\uDCC4 Resume Management\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1178,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  gap: '1rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.75rem',\n                    padding: '1rem',\n                    borderRadius: '12px',\n                    background: globalStyles.currentTheme.secondary,\n                    border: `2px dashed ${globalStyles.currentTheme.border}`,\n                    cursor: resumeUploadLoading ? 'not-allowed' : 'pointer',\n                    transition: 'all 0.3s ease'\n                  },\n                  onMouseEnter: e => {\n                    if (!resumeUploadLoading) {\n                      e.currentTarget.style.borderColor = globalStyles.currentTheme.primary;\n                      e.currentTarget.style.background = globalStyles.currentTheme.primary + '10';\n                    }\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.borderColor = globalStyles.currentTheme.border;\n                    e.currentTarget.style.background = globalStyles.currentTheme.secondary;\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(FiUpload, {\n                    size: 20,\n                    color: globalStyles.currentTheme.primary\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1209,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontWeight: 600,\n                        color: globalStyles.currentTheme.text,\n                        marginBottom: '0.2rem'\n                      },\n                      children: resumeUploadLoading ? 'Uploading...' : 'Upload Resume'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1211,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '0.8rem',\n                        color: globalStyles.currentTheme.textLight\n                      },\n                      children: \"PDF files only\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1218,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1210,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"file\",\n                    accept: \"application/pdf\",\n                    onChange: handleResumeUpload,\n                    disabled: resumeUploadLoading,\n                    style: {\n                      display: 'none'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1225,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1188,\n                  columnNumber: 21\n                }, this), resumeUrl && /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: resumeUrl,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.75rem',\n                    padding: '1rem',\n                    borderRadius: '12px',\n                    background: '#4ECDC4',\n                    color: 'white',\n                    textDecoration: 'none',\n                    fontWeight: 600,\n                    transition: 'all 0.3s ease'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.background = '#3DBDB6';\n                    e.currentTarget.style.transform = 'translateY(-2px)';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.background = '#4ECDC4';\n                    e.currentTarget.style.transform = 'translateY(0)';\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(FiFileText, {\n                    size: 20\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1260,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"View Resume\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1261,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1235,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1187,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1171,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1104,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 641,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 599,\n        columnNumber: 11\n      }, this), activeTab === \"resume\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px',\n          maxWidth: '1200px',\n          margin: '0 auto'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: getStyle('card'),\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              marginTop: 0,\n              color: '#333'\n            },\n            children: \"Career Assistant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1275,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              opacity: 0.8,\n              marginBottom: '24px',\n              color: '#666'\n            },\n            children: \"Get personalized resume advice and career guidance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1279,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              height: '50vh',\n              overflowY: 'auto',\n              marginBottom: '24px',\n              padding: '16px',\n              backgroundColor: '#f5f5f5',\n              border: '1px solid #e0e0e0',\n              borderRadius: '8px'\n            },\n            children: [messages.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                height: '100%',\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                justifyContent: 'center',\n                textAlign: 'center',\n                opacity: 0.7\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '48px',\n                  marginBottom: '16px'\n                },\n                children: \"\\uD83D\\uDCAC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1308,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: 0,\n                  color: '#333'\n                },\n                children: \"Start a conversation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1309,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: '#666'\n                },\n                children: \"Ask about resumes, interviews, or career advice\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1313,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1299,\n              columnNumber: 19\n            }, this) : messages.map((msg, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                ...(msg.role === 'user' ? getStyle('chatBubbleUser') : getStyle('chatBubbleBot')),\n                animation: 'fadeIn 0.3s ease'\n              },\n              children: msg.role === 'bot' ? /*#__PURE__*/_jsxDEV(ReactMarkdown, {\n                children: msg.content\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1327,\n                columnNumber: 25\n              }, this) : msg.content\n            }, idx, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1319,\n              columnNumber: 21\n            }, this)), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: getStyle('chatBubbleBot'),\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '8px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '10px',\n                    height: '10px',\n                    borderRadius: '50%',\n                    backgroundColor: '#1976d2',\n                    animation: 'pulse 1.4s infinite ease-in-out'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1337,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '10px',\n                    height: '10px',\n                    borderRadius: '50%',\n                    backgroundColor: '#1976d2',\n                    animation: 'pulse 1.4s infinite ease-in-out',\n                    animationDelay: '0.2s'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1344,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '10px',\n                    height: '10px',\n                    borderRadius: '50%',\n                    backgroundColor: '#1976d2',\n                    animation: 'pulse 1.4s infinite ease-in-out',\n                    animationDelay: '0.4s'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1352,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1336,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1335,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              ref: chatEndRef\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1363,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1288,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            style: {\n              display: 'flex',\n              gap: '12px'\n            },\n            onSubmit: e => {\n              e.preventDefault();\n              sendMessage();\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Type your message...\",\n              style: getStyle('inputField'),\n              value: input,\n              onChange: e => setInput(e.target.value),\n              disabled: loading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1374,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              style: {\n                ...getStyle('buttonPrimary'),\n                minWidth: '100px'\n              },\n              disabled: loading || !input.trim(),\n              children: loading ? 'Sending...' : 'Send'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1382,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1367,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1274,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1273,\n        columnNumber: 11\n      }, this), activeTab === \"dsa\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: getStyle('card'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: '16px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                style: {\n                  marginTop: 0,\n                  marginBottom: '8px'\n                },\n                children: \"\\uD83D\\uDE80 Company Wise DSA Questions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1404,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  opacity: 0.8,\n                  margin: 0\n                },\n                children: \"Explore DSA questions from top companies with enhanced filtering and favorites\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1405,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1403,\n              columnNumber: 17\n            }, this), showRevertButton && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: revertHeaderChanges,\n              style: {\n                ...getStyle('buttonPrimary'),\n                background: '#ff6b6b',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '8px',\n                fontSize: '14px',\n                padding: '8px 16px',\n                border: 'none',\n                borderRadius: '8px',\n                color: 'white',\n                cursor: 'pointer',\n                transition: 'all 0.3s ease'\n              },\n              onMouseEnter: e => e.target.style.background = '#ff5252',\n              onMouseLeave: e => e.target.style.background = '#ff6b6b',\n              children: [/*#__PURE__*/_jsxDEV(FiRefreshCw, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1429,\n                columnNumber: 21\n              }, this), \"Revert Header Color\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1410,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1402,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '8px',\n              marginBottom: '20px',\n              flexWrap: 'wrap',\n              borderBottom: '1px solid #eee',\n              paddingBottom: '16px'\n            },\n            children: ['all', ...Object.keys(companyCategories)].map(category => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSelectedCategory(category),\n              style: {\n                padding: '8px 16px',\n                borderRadius: '20px',\n                border: selectedCategory === category ? 'none' : '1px solid #ddd',\n                background: selectedCategory === category ? globalStyles.currentTheme.primary : 'transparent',\n                color: selectedCategory === category ? 'white' : '#666',\n                cursor: 'pointer',\n                fontSize: '14px',\n                fontWeight: selectedCategory === category ? 600 : 400,\n                transition: 'all 0.3s ease',\n                textTransform: 'capitalize'\n              },\n              onMouseEnter: e => {\n                if (selectedCategory !== category) {\n                  e.target.style.background = '#f5f5f5';\n                }\n              },\n              onMouseLeave: e => {\n                if (selectedCategory !== category) {\n                  e.target.style.background = 'transparent';\n                }\n              },\n              children: category === 'all' ? '🌟 All' : `${category === 'FAANG' ? '🔥' : category === 'Big Tech' ? '💻' : category === 'Startups' ? '🚀' : category === 'Finance' ? '💰' : category === 'Indian' ? '🇮🇳' : '🏢'} ${category}`\n            }, category, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1445,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1436,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '16px',\n              marginBottom: '24px',\n              flexWrap: 'wrap',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'relative',\n                flex: 1,\n                minWidth: '300px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  left: '16px',\n                  top: '50%',\n                  transform: 'translateY(-50%)',\n                  color: '#666'\n                },\n                children: /*#__PURE__*/_jsxDEV(FiSearch, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1499,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1492,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search companies...\",\n                style: {\n                  ...getStyle('inputField'),\n                  paddingLeft: '48px',\n                  width: '100%'\n                },\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1501,\n                columnNumber: 19\n              }, this), searchTerm && /*#__PURE__*/_jsxDEV(\"button\", {\n                style: {\n                  position: 'absolute',\n                  right: '16px',\n                  top: '50%',\n                  transform: 'translateY(-50%)',\n                  background: 'none',\n                  border: 'none',\n                  color: '#666',\n                  cursor: 'pointer'\n                },\n                onClick: () => setSearchTerm(\"\"),\n                children: /*#__PURE__*/_jsxDEV(FiX, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1526,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1513,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1491,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: sortBy,\n              onChange: e => setSortBy(e.target.value),\n              style: {\n                ...getStyle('inputField'),\n                width: 'auto',\n                minWidth: '150px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"name\",\n                children: \"\\uD83D\\uDCDD Sort by Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1541,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"favorites\",\n                children: \"\\u2B50 Favorites First\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1542,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1532,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1483,\n            columnNumber: 15\n          }, this), recentCompanies.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '24px',\n              padding: '16px',\n              borderRadius: '12px',\n              background: '#f8f9fa',\n              border: '1px solid #e9ecef'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '8px',\n                fontSize: '16px',\n                marginBottom: '12px',\n                color: '#333',\n                margin: '0 0 12px 0'\n              },\n              children: [/*#__PURE__*/_jsxDEV(FiClock, {\n                color: \"#666\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1564,\n                columnNumber: 21\n              }, this), \" Recently Viewed\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1555,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                gap: '8px',\n                flexWrap: 'wrap'\n              },\n              children: recentCompanies.map(company => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleCompanyClick(company),\n                style: {\n                  padding: '6px 12px',\n                  borderRadius: '16px',\n                  border: `1px solid ${globalStyles.currentTheme.primary}`,\n                  background: 'transparent',\n                  color: globalStyles.currentTheme.primary,\n                  cursor: 'pointer',\n                  fontSize: '12px',\n                  transition: 'all 0.3s ease'\n                },\n                onMouseEnter: e => {\n                  e.target.style.background = globalStyles.currentTheme.primary;\n                  e.target.style.color = 'white';\n                },\n                onMouseLeave: e => {\n                  e.target.style.background = 'transparent';\n                  e.target.style.color = globalStyles.currentTheme.primary;\n                },\n                children: company\n              }, company, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1572,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1566,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1548,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fill, minmax(220px, 1fr))',\n              gap: '16px',\n              marginTop: '24px'\n            },\n            children: filteredCompanies.map((company, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                ...getStyle('companyCard'),\n                position: 'relative',\n                transform: favoriteCompanies.includes(company) ? 'scale(1.02)' : 'scale(1)',\n                border: favoriteCompanies.includes(company) ? `2px solid ${globalStyles.currentTheme.primary}` : `1px solid ${globalStyles.currentTheme.border}`,\n                background: globalStyles.currentTheme.surface,\n                color: globalStyles.currentTheme.text,\n                animation: `fadeIn 0.3s ease ${index * 0.1}s both`,\n                boxShadow: `0 4px 6px ${globalStyles.currentTheme.shadow}`\n              },\n              onClick: () => handleCompanyClick(company),\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: e => toggleFavorite(company, e),\n                style: {\n                  position: 'absolute',\n                  top: '8px',\n                  right: '8px',\n                  background: 'none',\n                  border: 'none',\n                  cursor: 'pointer',\n                  color: favoriteCompanies.includes(company) ? '#ff6b6b' : '#ccc',\n                  transition: 'all 0.3s ease',\n                  fontSize: '18px'\n                },\n                children: /*#__PURE__*/_jsxDEV(FiHeart, {\n                  fill: favoriteCompanies.includes(company) ? 'currentColor' : 'none'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1640,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1626,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '56px',\n                  height: '56px',\n                  borderRadius: '50%',\n                  background: `linear-gradient(135deg, ${globalStyles.currentTheme.primary}, ${globalStyles.currentTheme.primaryDark})`,\n                  color: 'white',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  fontSize: '24px',\n                  fontWeight: 700,\n                  marginBottom: '12px',\n                  boxShadow: `0 4px 8px ${globalStyles.currentTheme.shadow}`\n                },\n                children: company.charAt(0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1644,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontWeight: 600,\n                  textAlign: 'center',\n                  fontSize: '14px',\n                  marginBottom: '8px'\n                },\n                children: company\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1662,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  fontSize: '12px',\n                  opacity: 0.7,\n                  marginTop: '8px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"\\uD83D\\uDCCA \", Math.floor(Math.random() * 50) + 10, \" Questions\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1679,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"\\u2B50 \", (Math.random() * 2 + 3).toFixed(1)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1680,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1672,\n                columnNumber: 21\n              }, this), Object.entries(companyCategories).map(([category, companies]) => {\n                if (companies.some(c => c.toLowerCase() === company.toLowerCase())) {\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      position: 'absolute',\n                      top: '8px',\n                      left: '8px',\n                      background: globalStyles.currentTheme.primary,\n                      color: 'white',\n                      padding: '2px 6px',\n                      borderRadius: '8px',\n                      fontSize: '10px',\n                      fontWeight: 600\n                    },\n                    children: category\n                  }, category, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1687,\n                    columnNumber: 27\n                  }, this);\n                }\n                return null;\n              })]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1609,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1602,\n            columnNumber: 15\n          }, this), filteredCompanies.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '40px',\n              opacity: 0.7,\n              color: '#666'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '48px',\n                marginBottom: '16px'\n              },\n              children: \"\\uD83D\\uDD0D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1719,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                color: '#333'\n              },\n              children: \"No companies found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1720,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#666'\n              },\n              children: \"Try adjusting your search or category filter\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1721,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1713,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1400,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1399,\n        columnNumber: 11\n      }, this), activeTab === \"quizzes\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: getStyle('card'),\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              marginTop: 0\n            },\n            children: \"Career Quizzes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1732,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              opacity: 0.8,\n              marginBottom: '24px'\n            },\n            children: \"Test your knowledge with our career-focused quizzes!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1733,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',\n              gap: '16px'\n            },\n            children: quizButtons.map((quiz, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: getStyle('quizCard'),\n              onClick: () => openQuizLink(quiz.link),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  style: {\n                    margin: '0 0 8px 0'\n                  },\n                  children: quiz.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1749,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '14px',\n                    opacity: 0.8\n                  },\n                  children: quiz.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1750,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1748,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#1976d2'\n                },\n                children: /*#__PURE__*/_jsxDEV(FiExternalLink, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1759,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1758,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1743,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1737,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1731,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1730,\n        columnNumber: 11\n      }, this), activeTab === \"coding\" && /*#__PURE__*/_jsxDEV(Suspense, {\n        fallback: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\",\n          style: {\n            margin: '50px auto'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1770,\n          columnNumber: 31\n        }, this),\n        children: /*#__PURE__*/_jsxDEV(LazyCoding, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1771,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1770,\n        columnNumber: 11\n      }, this), activeTab === \"resources\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: getStyle('card'),\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              marginTop: 0,\n              color: '#333'\n            },\n            children: \"Resources\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1778,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              opacity: 0.8,\n              marginBottom: '24px',\n              color: '#666'\n            },\n            children: \"Upload and manage your study materials and notes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1782,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '24px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                ...getStyle('buttonPrimary'),\n                background: '#f5f5f5',\n                color: '#333',\n                border: '1px solid #ddd',\n                cursor: resourceUploadLoading ? 'not-allowed' : 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(FiUpload, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1798,\n                columnNumber: 19\n              }, this), resourceUploadLoading ? 'Uploading...' : 'Upload Resource', /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                accept: \".pdf,.doc,.docx,.txt\",\n                onChange: handleResourceUpload,\n                disabled: resourceUploadLoading,\n                style: {\n                  display: 'none'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1800,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1791,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1790,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                marginBottom: '16px',\n                color: '#333'\n              },\n              children: \"Your Resources\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1811,\n              columnNumber: 17\n            }, this), userResources.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                opacity: 0.7,\n                color: '#666'\n              },\n              children: \"No resources uploaded yet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1816,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                backgroundColor: '#f5f5f5',\n                border: '1px solid #e0e0e0',\n                borderRadius: '8px',\n                padding: '16px'\n              },\n              children: userResources.map((file, idx) => {\n                const {\n                  data: urlData\n                } = supabase.storage.from('resources').getPublicUrl(`${user.id}/${file.name}`);\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    padding: '12px',\n                    borderBottom: '1px solid #eee',\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: '#333'\n                    },\n                    children: file.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1837,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: urlData.publicUrl,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    style: {\n                      color: '#1976d2',\n                      textDecoration: 'none',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '4px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(FiExternalLink, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1852,\n                      columnNumber: 29\n                    }, this), \"Open\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1840,\n                    columnNumber: 27\n                  }, this)]\n                }, idx, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1830,\n                  columnNumber: 25\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1821,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1810,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1777,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1776,\n        columnNumber: 11\n      }, this), activeTab === \"academics\" && /*#__PURE__*/_jsxDEV(Suspense, {\n        fallback: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\",\n          style: {\n            margin: '50px auto'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1865,\n          columnNumber: 31\n        }, this),\n        children: /*#__PURE__*/_jsxDEV(LazyExams, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1866,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1865,\n        columnNumber: 11\n      }, this), activeTab === \"faq\" && /*#__PURE__*/_jsxDEV(Suspense, {\n        fallback: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\",\n          style: {\n            margin: '50px auto'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1871,\n          columnNumber: 31\n        }, this),\n        children: /*#__PURE__*/_jsxDEV(LazyFaq, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1872,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1871,\n        columnNumber: 11\n      }, this), activeTab === \"admin\" && (user === null || user === void 0 ? void 0 : user.email) === ADMIN_EMAIL && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: getStyle('card'),\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              marginTop: 0,\n              color: '#333'\n            },\n            children: \"Admin Panel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1878,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '16px',\n              marginBottom: '24px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                ...getStyle('buttonPrimary'),\n                background: adminTab === 'users' ? globalStyles.currentTheme.primary : 'transparent',\n                color: adminTab === 'users' ? 'white' : '#333',\n                border: '1px solid #ddd'\n              },\n              onClick: () => setAdminTab('users'),\n              children: \"Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1887,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                ...getStyle('buttonPrimary'),\n                background: adminTab === 'resources' ? globalStyles.currentTheme.primary : 'transparent',\n                color: adminTab === 'resources' ? 'white' : '#333',\n                border: '1px solid #ddd'\n              },\n              onClick: () => setAdminTab('resources'),\n              children: \"Resources\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1900,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1882,\n            columnNumber: 15\n          }, this), adminTab === 'users' && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                marginBottom: '16px',\n                color: '#333'\n              },\n              children: \"All Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1917,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                backgroundColor: '#f5f5f5',\n                border: '1px solid #e0e0e0',\n                borderRadius: '8px',\n                padding: '16px'\n              },\n              children: allUsers.map((user, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '12px',\n                  borderBottom: '1px solid #eee',\n                  color: '#333'\n                },\n                children: user.email\n              }, idx, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1928,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1921,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1916,\n            columnNumber: 17\n          }, this), adminTab === 'resources' && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                marginBottom: '16px',\n                color: '#333'\n              },\n              children: \"All Resources\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1942,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                opacity: 0.7,\n                color: '#666'\n              },\n              children: \"Resource management coming soon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1946,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1941,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1877,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1876,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 538,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Notification, {\n      notification: notification,\n      onClose: () => setNotification(null)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1958,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 515,\n    columnNumber: 5\n  }, this);\n};\n_s(EduAIChatBot, \"tzRt2rjYiqsf7L//0UuY32zO+zk=\", false, function () {\n  return [usePerformanceMonitor, useResponsive, useSidebar, useOptimizedAnimation];\n});\n_c1 = EduAIChatBot;\nexport default EduAIChatBot;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1;\n$RefreshReg$(_c, \"LazyFaq$React.lazy\");\n$RefreshReg$(_c2, \"LazyFaq\");\n$RefreshReg$(_c3, \"LazyExams$React.lazy\");\n$RefreshReg$(_c4, \"LazyExams\");\n$RefreshReg$(_c5, \"LazyCoding$React.lazy\");\n$RefreshReg$(_c6, \"LazyCoding\");\n$RefreshReg$(_c7, \"LazyChart$React.lazy\");\n$RefreshReg$(_c8, \"LazyChart\");\n$RefreshReg$(_c9, \"LazyMarkdown$React.lazy\");\n$RefreshReg$(_c0, \"LazyMarkdown\");\n$RefreshReg$(_c1, \"EduAIChatBot\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "useMemo", "Suspense", "getDoc", "doc", "auth", "db", "axios", "sidebarItems", "onAuthStateChanged", "<PERSON><PERSON><PERSON>", "Sidebar", "Dashboard", "Notification", "useResponsive", "useSidebar", "useOptimizedAnimation", "usePerformanceMonitor", "getTheme", "FiFileText", "FiCode", "FiHelpCircle", "FiAward", "FiBook", "FiUser", "FiShield", "FiSearch", "FiUpload", "FiBriefcase", "FiBarChart2", "FiLayers", "FiCheckCircle", "FiExternalLink", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "FiRefreshCw", "createClient", "jsxDEV", "_jsxDEV", "LazyFaq", "lazy", "_c", "_c2", "LazyExams", "_c3", "_c4", "LazyCoding", "_c5", "_c6", "<PERSON><PERSON><PERSON><PERSON>", "_c7", "Bar", "Chart", "BarElement", "CategoryScale", "LinearScale", "<PERSON><PERSON><PERSON>", "Legend", "register", "default", "_c8", "LazyMarkdown", "_c9", "_c0", "updatedSidebarItems", "map", "item", "iconMap", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "icon", "tab", "title", "toLowerCase", "EduAIChatBot", "_s", "metrics", "startRender", "endRender", "isMobile", "isOpen", "sidebarOpen", "toggle", "toggleSidebar", "close", "closeSidebar", "shouldAnimate", "input", "setInput", "messages", "setMessages", "userId", "setUserId", "loading", "setLoading", "knowledge", "setKnowledge", "activeTab", "setActiveTab", "searchTerm", "setSearchTerm", "expandedMenus", "setExpandedMenus", "user", "setUser", "isDarkMode", "setIsDarkMode", "resumeUploadLoading", "setResumeUploadLoading", "resumeUrl", "setResumeUrl", "resourceUploadLoading", "setResourceUploadLoading", "userResources", "allUsers", "adminTab", "setAdminTab", "notification", "setNotification", "activityLog", "setActivityLog", "chatEndRef", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "sortBy", "setSortBy", "favoriteCompanies", "setFavoriteCompanies", "recentCompanies", "setRecentCompanies", "showRevertButton", "setShowRevertButton", "supabase", "SUPABASE_URL", "SUPABASE_ANON_KEY", "API_KEY", "ADMIN_EMAIL", "theme", "companyCategories", "companies", "quizButtons", "description", "link", "showNotification", "msg", "type", "logActivity", "activity", "prev", "timestamp", "Date", "toISOString", "email", "slice", "handleTabChange", "renderStart", "handleMenuToggle", "menuTitle", "handleLogout", "signOut", "error", "unsubscribe", "uid", "console", "log", "fetchUserProfile", "userRef", "userDoc", "exists", "userData", "data", "dp", "fetch", "then", "res", "text", "catch", "err", "getSession", "session", "listener", "onAuthStateChange", "_event", "subscription", "handleResumeUpload", "e", "file", "target", "files", "filePath", "id", "name", "storage", "from", "upload", "upsert", "urlData", "getPublicUrl", "publicUrl", "handleResourceUpload", "handleCompanyClick", "company", "filtered", "filter", "c", "window", "open", "formattedCompany", "replace", "toggleFavorite", "stopPropagation", "includes", "revertHeaderChanges", "eduNovaElement", "document", "querySelector", "subtitleElement", "style", "color", "filteredCompanies", "categoryCompanies", "some", "catCompany", "sort", "a", "b", "aFav", "bFav", "localeCompare", "openQuizLink", "url", "sendMessage", "trim", "userMessage", "role", "content", "currentInput", "_res$data$candidates", "_res$data$candidates$", "_res$data$candidates$2", "_res$data$candidates$3", "_res$data$candidates$4", "prompt", "post", "contents", "parts", "headers", "timeout", "botReply", "candidates", "botMessage", "substring", "message", "current", "scrollIntoView", "behavior", "getLast7Days", "days", "i", "d", "setDate", "getDate", "push", "toLocaleDateString", "chartLabels", "chartData", "labels", "datasets", "label", "day", "startsWith", "date", "length", "backgroundColor", "chartOptions", "responsive", "plugins", "legend", "position", "tooltip", "enabled", "scales", "y", "beginAtZero", "ticks", "stepSize", "className", "background", "children", "onToggleSidebar", "onLogout", "adminEmail", "onClose", "onTabChange", "onToggleMenu", "fallback", "margin", "padding", "surface", "border", "marginTop", "opacity", "marginBottom", "textLight", "display", "gridTemplateColumns", "gap", "quiz", "index", "borderRadius", "justifyContent", "alignItems", "cursor", "onClick", "fontSize", "primary", "size", "minHeight", "overflow", "top", "left", "width", "height", "animation", "zIndex", "right", "bottom", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON>ilter", "boxShadow", "fontWeight", "toUpperCase", "WebkitBackgroundClip", "WebkitTextFillColor", "textShadow", "split", "value", "unit", "bgGradient", "glowColor", "stat", "transition", "onMouseEnter", "currentTarget", "transform", "onMouseLeave", "letterSpacing", "lineHeight", "textTransform", "fontStyle", "Math", "min", "textAlign", "subtitle", "desc", "action", "borderColor", "globalStyles", "currentTheme", "shadow", "maxHeight", "overflowY", "secondary", "flexShrink", "flex", "toLocaleString", "flexDirection", "accept", "onChange", "disabled", "href", "rel", "textDecoration", "getStyle", "idx", "ReactMarkdown", "animationDelay", "ref", "onSubmit", "preventDefault", "placeholder", "min<PERSON><PERSON><PERSON>", "flexWrap", "borderBottom", "paddingBottom", "Object", "keys", "category", "paddingLeft", "FiX", "fill", "primaryDark", "char<PERSON>t", "floor", "random", "toFixed", "entries", "_c1", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/src/EduAIChatBot.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback, useMemo, Suspense } from \"react\";\nimport { getDoc, doc } from 'firebase/firestore';\nimport { auth, db } from './firebaseConfig';\nimport axios from \"axios\";\nimport { sidebarItems } from './sidebarItems';\nimport { onAuthStateChanged } from 'firebase/auth';\nimport { Navbar, Sidebar, Dashboard, Notification } from './components';\nimport { useResponsive, useSidebar, useOptimizedAnimation, usePerformanceMonitor } from './hooks/useResponsive';\nimport { getTheme } from './theme';\nimport {\n  FiFileText, FiCode, FiHelpCircle, FiAward, FiBook, FiUser, FiShield,\n  FiSearch, FiUpload, FiBriefcase, FiBarChart2, FiLayers, FiCheckCircle,\n  FiExternalLink, FiHeart, FiClock, FiRefreshCw\n} from \"react-icons/fi\";\nimport { createClient } from '@supabase/supabase-js';\nimport './App.css';\nimport './components.css';\n\n// Lazy load heavy components for better performance\nconst LazyFaq = React.lazy(() => import('./Faq'));\nconst LazyExams = React.lazy(() => import('./Exams'));\nconst LazyCoding = React.lazy(() => import('./Coding'));\n\n// Chart.js lazy loading with proper registration\nconst LazyChart = React.lazy(async () => {\n  const { Bar } = await import('react-chartjs-2');\n  const { Chart, BarElement, CategoryScale, LinearScale, Tooltip, Legend } = await import('chart.js');\n  Chart.register(BarElement, CategoryScale, LinearScale, Tooltip, Legend);\n  return { default: Bar };\n});\n\nconst LazyMarkdown = React.lazy(() => import('react-markdown'));\n\n// Enhanced sidebar items with icons\nconst updatedSidebarItems = sidebarItems.map(item => {\n  const iconMap = {\n    \"resume\": <FiFileText />,\n    \"dsa\": <FiCode />,\n    \"coding\": <FiLayers />,\n    \"resources\": <FiBriefcase />,\n    \"quizzes\": <FiCheckCircle />,\n    \"aptitude\": <FiBarChart2 />,\n    \"academics\": <FiBook />,\n    \"faq\": <FiHelpCircle />,\n    \"admin\": <FiShield />\n  };\n\n  return {\n    ...item,\n    icon: iconMap[item.tab] || iconMap[item.title.toLowerCase()] || <FiAward />\n  };\n});\n\nconst EduAIChatBot = () => {\n  // Performance monitoring\n  const { metrics, startRender, endRender } = usePerformanceMonitor();\n  const { isMobile } = useResponsive();\n  const { isOpen: sidebarOpen, toggle: toggleSidebar, close: closeSidebar } = useSidebar();\n  const { shouldAnimate } = useOptimizedAnimation();\n\n  // Optimized state declarations with proper initial values\n  const [input, setInput] = useState(\"\");\n  const [messages, setMessages] = useState([]);\n  const [userId, setUserId] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [knowledge, setKnowledge] = useState(\"\");\n  const [activeTab, setActiveTab] = useState(\"dashboard\");\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [expandedMenus, setExpandedMenus] = useState({});\n  const [user, setUser] = useState(null);\n  const [isDarkMode, setIsDarkMode] = useState(false);\n\n  const [resumeUploadLoading, setResumeUploadLoading] = useState(false);\n  const [resumeUrl, setResumeUrl] = useState(null);\n  const [resourceUploadLoading, setResourceUploadLoading] = useState(false);\n  const [userResources] = useState([]);\n  const [allUsers] = useState([]);\n  const [adminTab, setAdminTab] = useState('users');\n  const [notification, setNotification] = useState(null);\n  const [activityLog, setActivityLog] = useState([]);\n  const chatEndRef = useRef(null);\n\n  // Enhanced DSA section states\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [sortBy, setSortBy] = useState('name');\n  const [favoriteCompanies, setFavoriteCompanies] = useState([]);\n  const [recentCompanies, setRecentCompanies] = useState([]);\n  const [showRevertButton, setShowRevertButton] = useState(true);\n\n  // Memoized configurations for better performance\n  const supabase = useMemo(() => {\n    const SUPABASE_URL = 'https://gziaptswfepiveyylven.supabase.co';\n    const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imd6aWFwdHN3ZmVwaXZleXlsdmVuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU2NzczNTYsImV4cCI6MjA2MTI1MzM1Nn0.wmqXZGffrox8E_PuCwbzh4xJEffsvFmZCVcF6WFAX6Q';\n    return createClient(SUPABASE_URL, SUPABASE_ANON_KEY);\n  }, []);\n\n  const API_KEY = \"AIzaSyC6kHWto78QdqHz7Uu9RzEXb443ZO7tG5M\";\n  const ADMIN_EMAIL = '<EMAIL>';\n\n  // Memoized theme\n  const theme = useMemo(() => getTheme(isDarkMode), [isDarkMode]);\n\n  // Company categories for enhanced DSA section\n  const companyCategories = {\n    'FAANG': ['Facebook', 'Apple', 'Amazon', 'Netflix', 'Google'],\n    'Big Tech': ['Microsoft', 'Adobe', 'Salesforce', 'Oracle', 'IBM', 'Intel', 'Nvidia'],\n    'Startups': ['Stripe', 'Airbnb', 'Uber', 'Lyft', 'DoorDash', 'Instacart', 'Coinbase'],\n    'Finance': ['Goldman Sachs', 'JPMorgan', 'Morgan Stanley', 'BlackRock', 'Citadel', 'Two Sigma'],\n    'Consulting': ['McKinsey', 'BCG', 'Bain', 'Deloitte', 'Accenture', 'PwC'],\n    'E-commerce': ['Amazon', 'eBay', 'Shopify', 'Etsy', 'Wayfair', 'Booking.com'],\n    'Gaming': ['Blizzard', 'Epic Games', 'Riot Games', 'Unity', 'Roblox'],\n    'Indian': ['TCS', 'Infosys', 'Wipro', 'HCL', 'Flipkart', 'Paytm', 'Zomato', 'Swiggy']\n  };\n\n  // Complete list of companies\n  const companies = [\n     \"Accenture\", \"Accolite\", \"Adobe\", \"Affirm\", \"Agoda\", \"Airbnb\", \"Airtel\",\n    \"Akamar\", \"Akuna Capital\", \"Alibaba\", \"Altimetrik\", \"Amazon\", \"AMD\",\n    \"Amdocs\", \"American Express\", \"Anduril\", \"Apple\", \"Arista Networks\",\n    \"Arcesium\", \"Atlassian\", \"Attentive\", \"athenahealth\", \"Autodesk\",\n    \"Avito\", \"Baidu\", \"Barclays\", \"BitGo\", \"BlackRock\", \"Blizzard\",\n    \"Block\", \"Bloomberg\", \"BNY Mellon\", \"Boft\", \"Booking.com\", \"Bos\",\n    \"BP\", \"ByteDance\", \"Cadence\", \"Capgemini\", \"Capital One\", \"CARS24\",\n    \"carwale\", \"Cashfree\", \"Chewy\", \"Cisco\", \"Citadel\", \"Citrix\",\n    \"Cloudera\", \"Cloudflare\", \"Cognizant\", \"Coinbase\", \"Commvault\",\n    \"Confluent\", \"Coupang\", \"Coursera\", \"CrowdStrike\", \"Cruise\",\n    \"Curefit\", \"Databricks\", \"Datadog\", \"DE Shaw\", \"Deloitte\", \"Dell\",\n    \"Deliveroo\", \"Derantior\", \"Deutsche Bank\", \"Devflev\", \"Directi\",\n    \"Disney\", \"Docusign\", \"DoorDash\", \"Dream11\", \"Dropbox\", \"DRW\",\n    \"Dunzo\", \"eBay\", \"EPAM Systems\", \"Epic Systems\", \"Expedia\",\n    \"FactSet\", \"Flexport\", \"Flipkart\", \"Freshworks\", \"GE Healthcare\",\n    \"Geico\", \"Goldman Sachs\", \"Google\", \"Grab\", \"Grammarly\", \"Graviton\",\n    \"Groww\", \"GSN Games\", \"Hashedin\", \"HCL\", \"HPE\", \"Hubspot\",\n    \"Hudson River Trading\", \"Huawei\", \"IBM\", \"IMC\", \"Indeed\", \"Infosys\",\n    \"InMobi\", \"Intel\", \"Intuit\", \"JPMorgan\", \"Jane Street\",\n    \"Josh Technology\", \"Jump Trading\", \"Juspay\", \"Karat\", \"KLA\",\n    \"LinkedIn\", \"LiveRamp\", \"Lowe's\", \"Lucid\", \"Lyft\", \"MakeMyTrip\",\n    \"Mastercard\", \"MathWorks\", \"Media.net\", \"Meesho\", \"Mercari\", \"Meta\",\n    \"Microsoft\", \"Millennium\", \"Mitsogo\", \"Moloco\", \"MongoDB\",\n    \"Morgan Stanley\", \"Moveworks\", \"Myntra\", \"Nagarro\", \"NetApp\",\n    \"Netflix\", \"Nextdoor\", \"Nielsen\", \"Nike\", \"Niantic\", \"Nordstrom\",\n    \"Nutanix\", \"Nvidia\", \"Okta\", \"OKX\", \"OpenAI\", \"OpenText\", \"Oracle\",\n    \"Otter.ai\", \"Oyo\", \"Ozon\", \"Palantir Technologies\", \"Palo Alto Networks\",\n    \"PayPal\", \"Paytm\", \"Persistent Systems\", \"PhonePe\", \"Pinterest\",\n    \"Pocket Gems\", \"Point72\", \"Pure Storage\", \"Qualcomm\", \"Quora\",\n    \"Rakuten\", \"Razorpay\", \"RBC\", \"Reddit\", \"Revolut\", \"Robinhood\",\n    \"Roblox\", \"Rubrik\", \"Salesforce\", \"Samsung\", \"SAP\", \"ServiceNow\",\n    \"Shopify\", \"Siemens\", \"Sigmoid\", \"SIG\", \"Snowflake\", \"Snap\", \"Sofi\",\n    \"Splunk\", \"Spotify\", \"Sprinklr\", \"Squarepoint Capital\", \"Stripe\",\n    \"Swiggy\", \"TCS\", \"Tekion\", \"Tencent\", \"Tesla\", \"ThoughtSpot\",\n    \"ThoughtWorks\", \"TikTok\", \"Tinkoff\", \"Trilogy\", \"Turing\", \"Turo\",\n    \"Twilio\", \"Twitch\", \"Two Sigma\", \"Uber\", \"UiPath\", \"UKG\",\n    \"Veeva Systems\", \"Verily\", \"Verkada\", \"Virtu Financial\", \"Visa\",\n    \"VK\", \"VMware\", \"Walmart Labs\", \"WarnerMedia\", \"Wayfair\",\n    \"Wells Fargo\", \"Wipro\", \"Wix\", \"Workday\", \"X\", \"Yahoo\", \"Yandex\",\n    \"Yelp\", \"Zalando\", \"Zenefits\", \"Zepto\", \"Zeta\", \"Zillow\", \"Zoho\",\n    \"Zomato\", \"ZScaler\", \"Zopsmart\"\n  ];\n\n  // Quiz buttons data\n  const quizButtons = [\n    {\n      title: \"OP and CN Quiz\",\n      description: \"Test your knowledge of Operating System and Computer Networks\",\n      link: \"https://opcn.netlify.app\",\n    },\n    {\n      title: \"OOPs and DBMS Quiz\",\n      description: \"Challenge yourself with oops and dbms\",\n      link: \"https://oopsanddbms.netlify.app/\",\n    },\n    {\n      title: \"System Design Quiz\",\n      description: \"Test your system design knowledge\",\n      link: \"https://system-design041.netlify.app\",\n    },\n    {\n      title: \"Quantitative Aptitude and Reasoning Quiz\",\n      description: \"Practice common quant and reasoning questions\",\n      link: \"https://quantandreasoning.netlify.app\",\n    },\n    {\n      title: \"Cloud & DevOps Quiz\",\n      description: \"Test your knowledge of Cloud and DevOps concepts\",\n      link: \"https://cloud-devops.netlify.app\",\n    },\n    {\n      title: \"DSA Quiz\",\n      description: \"Data Structures and Algorithms quiz\",\n      link: \"https://dsa041.netlify.app\",\n    },\n    {\n      title: \"Operating System & Computer Networks Quiz\",\n      description: \"Quiz on OS and Computer Networks\",\n      link: \"https://opcn.netlify.app\",\n    },\n     {\n      title: \"Web Development Quiz\",\n      description: \"Quiz on Web Development topics\",\n      link: \"https://web-dev041.netlify.app\",\n\n    },\n  ];\n\n  // Optimized callback functions with useCallback\n  const showNotification = useCallback((msg, type = 'info') => {\n    setNotification({ msg, type });\n  }, []);\n\n  const logActivity = useCallback((activity) => {\n    setActivityLog(prev => [{\n      activity,\n      timestamp: new Date().toISOString(),\n      user: user?.email || 'Anonymous'\n    }, ...prev.slice(0, 99)]); // Keep only last 100 activities\n  }, [user?.email]);\n\n  const handleTabChange = useCallback((tab) => {\n    const renderStart = startRender();\n    setActiveTab(tab);\n    logActivity(`Navigated to ${tab}`);\n    endRender(renderStart);\n  }, [startRender, endRender, logActivity]);\n\n  const handleMenuToggle = useCallback((menuTitle) => {\n    setExpandedMenus(prev => ({\n      ...prev,\n      [menuTitle]: !prev[menuTitle]\n    }));\n  }, []);\n\n  const handleLogout = useCallback(async () => {\n    try {\n      await supabase.auth.signOut();\n      setUser(null);\n      showNotification('Logged out successfully!', 'success');\n      logActivity('User logged out');\n    } catch (error) {\n      showNotification('Logout failed', 'error');\n    }\n  }, [supabase.auth, showNotification, logActivity]);\n\n  // Fetch user profile\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, (user) => {\n      if (user) {\n        setUserId(user.uid);\n      } else {\n        console.log('User is not authenticated');\n        setLoading(false);\n      }\n    });\n    return () => unsubscribe();\n  }, []);\n\n  useEffect(() => {\n    if (userId) {\n      const fetchUserProfile = async () => {\n        const userRef = doc(db, \"users\", userId);\n        const userDoc = await getDoc(userRef);\n\n        if (userDoc.exists()) {\n          const userData = userDoc.data();\n          // Profile pic functionality can be added later\n          console.log(\"User data loaded:\", userData.dp);\n        } else {\n          console.log(\"No such user!\");\n        }\n        setLoading(false);\n      };\n      fetchUserProfile();\n    }\n  }, [userId]);\n\n  // Fetch training data\n  useEffect(() => {\n    fetch(\"/training-data.txt\")\n      .then((res) => res.text())\n      .then((data) => setKnowledge(data))\n      .catch((err) => console.error(\"Failed to load training data:\", err));\n  }, []);\n\n  // Supabase auth state\n  useEffect(() => {\n    supabase.auth.getSession().then(({ data: { session } }) => {\n      setUser(session?.user || null);\n    });\n    const { data: listener } = supabase.auth.onAuthStateChange((_event, session) => {\n      setUser(session?.user || null);\n    });\n    return () => {\n      listener?.subscription.unsubscribe();\n    };\n  }, [supabase.auth]);\n\n  // Handle resume upload\n  const handleResumeUpload = async (e) => {\n    const file = e.target.files[0];\n    if (!file || !user) return;\n    setResumeUploadLoading(true);\n    const filePath = `${user.id}/${file.name}`;\n    const { error } = await supabase.storage.from('resumes').upload(filePath, file, { upsert: true });\n    if (!error) {\n      const { data: urlData } = supabase.storage.from('resumes').getPublicUrl(filePath);\n      setResumeUrl(urlData.publicUrl);\n      showNotification('Resume uploaded successfully!', 'success');\n      logActivity('Uploaded a resume');\n    } else {\n      showNotification('Resume upload failed.', 'error');\n    }\n    setResumeUploadLoading(false);\n  };\n\n  // Handle resource upload\n  const handleResourceUpload = async (e) => {\n    const file = e.target.files[0];\n    if (!file || !user) return;\n    setResourceUploadLoading(true);\n    const filePath = `${user.id}/${file.name}`;\n    const { error } = await supabase.storage.from('resources').upload(filePath, file, { upsert: true });\n    if (!error) {\n      showNotification('Resource uploaded!', 'success');\n      logActivity(`Uploaded resource: ${file.name}`);\n    } else {\n      showNotification('Resource upload failed.', 'error');\n    }\n    setResourceUploadLoading(false);\n  };\n\n  // Optimized company handlers with useCallback\n  const handleCompanyClick = useCallback((company) => {\n    // Add to recent companies\n    setRecentCompanies(prev => {\n      const filtered = prev.filter(c => c !== company);\n      return [company, ...filtered].slice(0, 5); // Keep only 5 recent\n    });\n\n    logActivity(`Viewed ${company} DSA questions`);\n\n    if (company.toLowerCase() === 'microsoft') {\n      window.open('/company-dsa/Microsoft_questions.html', '_blank');\n      return;\n    }\n    const formattedCompany = company.replace(/\\s+/g, '');\n    window.open(`/company-dsa/${formattedCompany}.html`, '_blank');\n  }, [logActivity]);\n\n  // Toggle favorite company\n  const toggleFavorite = useCallback((company, e) => {\n    e.stopPropagation(); // Prevent company click\n    setFavoriteCompanies(prev => {\n      if (prev.includes(company)) {\n        return prev.filter(c => c !== company);\n      } else {\n        return [...prev, company];\n      }\n    });\n  }, []);\n\n  // Revert header color changes\n  const revertHeaderChanges = () => {\n    setShowRevertButton(false);\n    showNotification('Header text color reverted to theme default!', 'success');\n\n    // Actually revert the header text color by updating the DOM\n    const eduNovaElement = document.querySelector('[data-header-title]');\n    const subtitleElement = document.querySelector('[data-header-subtitle]');\n\n    if (eduNovaElement) {\n      eduNovaElement.style.color = '#333';\n    }\n    if (subtitleElement) {\n      subtitleElement.style.color = '#333';\n    }\n  };\n\n  // Memoized filtered companies for better performance\n  const filteredCompanies = useMemo(() => {\n    let filtered = companies;\n\n    // Filter by category\n    if (selectedCategory !== 'all') {\n      const categoryCompanies = companyCategories[selectedCategory] || [];\n      filtered = filtered.filter(company =>\n        categoryCompanies.some(catCompany =>\n          company.toLowerCase().includes(catCompany.toLowerCase()) ||\n          catCompany.toLowerCase().includes(company.toLowerCase())\n        )\n      );\n    }\n\n    // Filter by search term\n    filtered = filtered.filter(company =>\n      company.toLowerCase().includes(searchTerm.toLowerCase())\n    );\n\n    // Sort companies\n    if (sortBy === 'name') {\n      filtered.sort();\n    } else if (sortBy === 'favorites') {\n      filtered.sort((a, b) => {\n        const aFav = favoriteCompanies.includes(a);\n        const bFav = favoriteCompanies.includes(b);\n        if (aFav && !bFav) return -1;\n        if (!aFav && bFav) return 1;\n        return a.localeCompare(b);\n      });\n    }\n\n    return filtered;\n  }, [companies, selectedCategory, searchTerm, sortBy, favoriteCompanies, companyCategories]);\n\n  // Optimized quiz link handler\n  const openQuizLink = useCallback((url) => {\n    window.open(url, \"_blank\");\n    logActivity(`Opened quiz: ${url}`);\n  }, [logActivity]);\n\n  // Optimized send message function with useCallback\n  const sendMessage = useCallback(async () => {\n    if (!input.trim()) return;\n\n    const userMessage = { role: \"user\", content: input };\n    setMessages((prev) => [...prev, userMessage]);\n    const currentInput = input;\n    setInput(\"\");\n    setLoading(true);\n\n    try {\n      const prompt = `You are a resume assistant. Help users improve their resumes, provide suggestions, and answer career-related questions. Use the following knowledge if it helps. If it's not relevant, use your own intelligence.\\n\\nKnowledge:\\n${knowledge}\\n\\nQuestion: ${currentInput}`;\n\n      const res = await axios.post(\n        `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEY}`,\n        {\n          contents: [\n            {\n              parts: [{ text: prompt }],\n            },\n          ],\n        },\n        {\n          headers: {\n            \"Content-Type\": \"application/json\",\n          },\n          timeout: 30000 // 30 second timeout\n        }\n      );\n\n      const botReply =\n        res.data.candidates?.[0]?.content?.parts?.[0]?.text ||\n        \"⚠ No response received.\";\n      const botMessage = { role: \"bot\", content: botReply };\n      setMessages((prev) => [...prev, botMessage]);\n      logActivity(`Chat message sent: ${currentInput.substring(0, 50)}...`);\n    } catch (error) {\n      console.error(\"Gemini API Error:\", error);\n      setMessages((prev) => [\n        ...prev,\n        { role: \"bot\", content: \"❌ Error: \" + error.message },\n      ]);\n      showNotification('Failed to send message', 'error');\n    } finally {\n      setLoading(false);\n    }\n  }, [input, knowledge, API_KEY, logActivity, showNotification]);\n\n  // Authentication functionality can be added later if needed\n\n  // Auto-scroll chat\n  useEffect(() => {\n    if (chatEndRef.current) chatEndRef.current.scrollIntoView({ behavior: 'smooth' });\n  }, [messages, loading]);\n\n  // Chart data\n  const getLast7Days = () => {\n    const days = [];\n    for (let i = 6; i >= 0; i--) {\n      const d = new Date();\n      d.setDate(d.getDate() - i);\n      days.push(d.toLocaleDateString());\n    }\n    return days;\n  };\n\n  const chartLabels = getLast7Days();\n  const chartData = {\n    labels: chartLabels,\n    datasets: [\n      {\n        label: 'Resource Uploads',\n        data: chartLabels.map(day => activityLog.filter(a => a.type === 'activity' && a.msg.startsWith('Uploaded resource') && new Date(a.date).toLocaleDateString() === day).length),\n        backgroundColor: '#3182ce',\n      },\n      {\n        label: 'Coding Practice',\n        data: chartLabels.map(day => activityLog.filter(a => a.type === 'activity' && a.msg === 'Clicked coding practice link' && new Date(a.date).toLocaleDateString() === day).length),\n        backgroundColor: '#805ad5',\n      },\n    ],\n  };\n\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: { position: 'top' },\n      tooltip: { enabled: true },\n    },\n    scales: {\n      y: { beginAtZero: true, ticks: { stepSize: 1 } },\n    },\n  };\n\n  // Render optimized component structure\n  return (\n    <div className={`app-container ${shouldAnimate ? 'smooth-transition' : ''}`} style={{ backgroundColor: theme.background, color: theme.text }}>\n      {/* Performance-optimized Navbar */}\n      <Navbar\n        sidebarOpen={sidebarOpen}\n        onToggleSidebar={toggleSidebar}\n        user={user}\n        onLogout={handleLogout}\n        adminEmail={ADMIN_EMAIL}\n      />\n\n      {/* Performance-optimized Sidebar */}\n      <Sidebar\n        isOpen={sidebarOpen}\n        onClose={closeSidebar}\n        activeTab={activeTab}\n        onTabChange={handleTabChange}\n        sidebarItems={updatedSidebarItems}\n        expandedMenus={expandedMenus}\n        onToggleMenu={handleMenuToggle}\n        theme={theme}\n      />\n\n      {/* Main Content with optimized layout */}\n      <main className={`main-content ${sidebarOpen && !isMobile ? 'main-content-with-sidebar' : ''}`}>\n        {/* Dashboard Content */}\n        {activeTab === \"dashboard\" && (\n          <Suspense fallback={<div className=\"loading-spinner\" style={{ margin: '50px auto' }} />}>\n            <Dashboard user={user} onTabChange={handleTabChange} />\n          </Suspense>\n        )}\n\n        {/* Quizzes Section with optimized layout */}\n        {activeTab === \"quizzes\" && (\n          <div style={{ padding: '24px', backgroundColor: theme.background }}>\n            <div className=\"card\" style={{ backgroundColor: theme.surface, border: `1px solid ${theme.border}` }}>\n              <h2 style={{ marginTop: 0, color: theme.text }}>Career Quizzes</h2>\n              <p style={{ opacity: 0.8, marginBottom: '24px', color: theme.textLight }}>\n                Test your knowledge with our career-focused quizzes!\n              </p>\n\n              <div style={{\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',\n                gap: '16px'\n              }}>\n                {quizButtons.map((quiz, index) => (\n                  <div\n                    key={index}\n                    className=\"quiz-card hover-lift\"\n                    style={{\n                      backgroundColor: theme.surface,\n                      border: `1px solid ${theme.border}`,\n                      borderRadius: '12px',\n                      padding: '20px',\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      alignItems: 'center',\n                      cursor: 'pointer'\n                    }}\n                    onClick={() => openQuizLink(quiz.link)}\n                  >\n                    <div>\n                      <h3 style={{ margin: '0 0 8px 0', color: theme.text }}>{quiz.title}</h3>\n                      <p style={{\n                        margin: 0,\n                        fontSize: '14px',\n                        opacity: 0.8,\n                        color: theme.textLight\n                      }}>\n                        {quiz.description}\n                      </p>\n                    </div>\n                    <div style={{ color: theme.primary }}>\n                      <FiExternalLink size={20} />\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* DSA Section with optimized company grid */}\n        {activeTab === \"dsa\" && (\n          <div style={{\n            padding: '24px',\n            backgroundColor: theme.background,\n            minHeight: '100vh',\n            position: 'relative',\n            overflow: 'hidden'\n          }}>\n            {/* Animated Background Elements */}\n            <div style={{\n              position: 'absolute',\n              top: '10%',\n              left: '5%',\n              width: '300px',\n              height: '300px',\n              background: 'linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))',\n              borderRadius: '50%',\n              animation: 'float 6s ease-in-out infinite',\n              zIndex: 0\n            }} />\n            <div style={{\n              position: 'absolute',\n              top: '60%',\n              right: '10%',\n              width: '200px',\n              height: '200px',\n              background: 'linear-gradient(45deg, rgba(255,255,255,0.08), rgba(255,255,255,0.03))',\n              borderRadius: '50%',\n              animation: 'float 8s ease-in-out infinite reverse',\n              zIndex: 0\n            }} />\n            <div style={{\n              position: 'absolute',\n              bottom: '20%',\n              left: '15%',\n              width: '150px',\n              height: '150px',\n              background: 'linear-gradient(45deg, rgba(255,255,255,0.06), rgba(255,255,255,0.02))',\n              borderRadius: '50%',\n              animation: 'float 10s ease-in-out infinite',\n              zIndex: 0\n            }} />\n\n            <div style={{ maxWidth: '1400px', margin: '0 auto', position: 'relative', zIndex: 1 }}>\n\n              {/* Hero Section */}\n              <div style={{\n                background: 'linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.1))',\n                backdropFilter: 'blur(20px)',\n                borderRadius: '25px',\n                padding: '3rem',\n                marginBottom: '2rem',\n                color: 'white',\n                position: 'relative',\n                overflow: 'hidden',\n                border: '1px solid rgba(255,255,255,0.2)',\n                boxShadow: '0 25px 50px rgba(0,0,0,0.2)'\n              }}>\n                {/* Animated particles */}\n                <div style={{\n                  position: 'absolute',\n                  top: '20px',\n                  right: '20px',\n                  fontSize: '2rem',\n                  animation: 'bounce 2s infinite'\n                }}>🚀</div>\n                <div style={{\n                  position: 'absolute',\n                  bottom: '20px',\n                  left: '20px',\n                  fontSize: '1.5rem',\n                  animation: 'bounce 3s infinite'\n                }}>⭐</div>\n                <div style={{\n                  position: 'absolute',\n                  top: '50%',\n                  right: '10%',\n                  fontSize: '1.2rem',\n                  animation: 'bounce 4s infinite'\n                }}>💎</div>\n\n                <div style={{ display: 'flex', alignItems: 'center', gap: '2rem', position: 'relative', zIndex: 1 }}>\n                  <div style={{\n                    width: '120px',\n                    height: '120px',\n                    borderRadius: '50%',\n                    background: 'linear-gradient(135deg, #ff6b6b, #feca57)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    fontSize: '3rem',\n                    fontWeight: 'bold',\n                    border: '4px solid rgba(255, 255, 255, 0.3)',\n                    boxShadow: '0 15px 35px rgba(0,0,0,0.2)',\n                    animation: 'pulse 3s infinite'\n                  }}>\n                    {user ? user.email[0].toUpperCase() : '👤'}\n                  </div>\n                  <div>\n                    <h1 style={{\n                      margin: 0,\n                      fontSize: '3.5rem',\n                      fontWeight: 800,\n                      marginBottom: '1rem',\n                      background: 'linear-gradient(45deg, #fff, #f0f0f0)',\n                      WebkitBackgroundClip: 'text',\n                      WebkitTextFillColor: 'transparent',\n                      textShadow: '0 2px 4px rgba(0,0,0,0.3)'\n                    }}>\n                      Hey {user ? user.email.split('@')[0] : 'Champion'}! 🎯\n                    </h1>\n                    <p style={{\n                      margin: 0,\n                      fontSize: '1.3rem',\n                      opacity: 0.95,\n                      fontWeight: 500\n                    }}>\n                      Time to level up your skills and dominate your goals! 💪✨\n                    </p>\n                    <div style={{\n                      marginTop: '1rem',\n                      display: 'flex',\n                      gap: '1rem'\n                    }}>\n                      <div style={{\n                        background: 'rgba(255,255,255,0.2)',\n                        padding: '0.5rem 1rem',\n                        borderRadius: '20px',\n                        fontSize: '0.9rem',\n                        fontWeight: 600\n                      }}>\n                        🔥 12 Day Streak\n                      </div>\n                      <div style={{\n                        background: 'rgba(255,255,255,0.2)',\n                        padding: '0.5rem 1rem',\n                        borderRadius: '20px',\n                        fontSize: '0.9rem',\n                        fontWeight: 600\n                      }}>\n                        🏆 Level 15\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Gaming-Style Stats Cards */}\n              <div style={{\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n                gap: '1.5rem',\n                marginBottom: '2rem'\n              }}>\n                {[\n                  {\n                    title: 'FIRE STREAK',\n                    value: '12',\n                    unit: 'DAYS',\n                    icon: '🔥',\n                    color: '#ff4757',\n                    bgGradient: 'linear-gradient(135deg, #ff4757, #ff6b7a)',\n                    glowColor: '#ff4757',\n                    description: 'Unstoppable momentum!'\n                  },\n                  {\n                    title: 'SKILL POINTS',\n                    value: '2,847',\n                    unit: 'XP',\n                    icon: '⚡',\n                    color: '#3742fa',\n                    bgGradient: 'linear-gradient(135deg, #3742fa, #5352ed)',\n                    glowColor: '#3742fa',\n                    description: 'Level up achieved!'\n                  },\n                  {\n                    title: 'POWER LEVEL',\n                    value: '47',\n                    unit: 'HOURS',\n                    icon: '💪',\n                    color: '#2ed573',\n                    bgGradient: 'linear-gradient(135deg, #2ed573, #7bed9f)',\n                    glowColor: '#2ed573',\n                    description: 'Training complete!'\n                  },\n                  {\n                    title: 'ACHIEVEMENTS',\n                    value: '15',\n                    unit: 'UNLOCKED',\n                    icon: '🏆',\n                    color: '#ffa502',\n                    bgGradient: 'linear-gradient(135deg, #ffa502, #ffb142)',\n                    glowColor: '#ffa502',\n                    description: 'Champion status!'\n                  }\n                ].map((stat, index) => (\n                  <div key={index} style={{\n                    background: 'linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.1))',\n                    backdropFilter: 'blur(20px)',\n                    borderRadius: '20px',\n                    padding: '2rem',\n                    position: 'relative',\n                    overflow: 'hidden',\n                    border: '1px solid rgba(255,255,255,0.2)',\n                    cursor: 'pointer',\n                    transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',\n                    boxShadow: `0 15px 35px rgba(0,0,0,0.1), 0 0 0 1px ${stat.glowColor}20`\n                  }}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.transform = 'translateY(-10px) scale(1.02)';\n                    e.currentTarget.style.boxShadow = `0 25px 50px rgba(0,0,0,0.2), 0 0 30px ${stat.glowColor}40`;\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.transform = 'translateY(0) scale(1)';\n                    e.currentTarget.style.boxShadow = `0 15px 35px rgba(0,0,0,0.1), 0 0 0 1px ${stat.glowColor}20`;\n                  }}>\n\n                    {/* Animated background pattern */}\n                    <div style={{\n                      position: 'absolute',\n                      top: '-50%',\n                      left: '-50%',\n                      width: '200%',\n                      height: '200%',\n                      background: `conic-gradient(from 0deg, transparent, ${stat.color}20, transparent)`,\n                      animation: 'rotate 20s linear infinite',\n                      opacity: 0.3\n                    }} />\n\n                    {/* Glow effect */}\n                    <div style={{\n                      position: 'absolute',\n                      top: '10px',\n                      right: '10px',\n                      width: '80px',\n                      height: '80px',\n                      background: stat.bgGradient,\n                      borderRadius: '50%',\n                      opacity: 0.2,\n                      filter: 'blur(20px)'\n                    }} />\n\n                    <div style={{ position: 'relative', zIndex: 1 }}>\n                      {/* Header */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        marginBottom: '1.5rem'\n                      }}>\n                        <div style={{\n                          fontSize: '0.8rem',\n                          fontWeight: 800,\n                          color: 'rgba(255,255,255,0.8)',\n                          letterSpacing: '2px'\n                        }}>\n                          {stat.title}\n                        </div>\n                        <div style={{\n                          fontSize: '2rem',\n                          filter: 'drop-shadow(0 0 10px currentColor)'\n                        }}>\n                          {stat.icon}\n                        </div>\n                      </div>\n\n                      {/* Main Value */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'baseline',\n                        gap: '0.5rem',\n                        marginBottom: '1rem'\n                      }}>\n                        <div style={{\n                          fontSize: '3rem',\n                          fontWeight: 900,\n                          color: 'white',\n                          textShadow: `0 0 20px ${stat.color}`,\n                          lineHeight: 1\n                        }}>\n                          {stat.value}\n                        </div>\n                        <div style={{\n                          fontSize: '0.9rem',\n                          fontWeight: 600,\n                          color: stat.color,\n                          textTransform: 'uppercase',\n                          letterSpacing: '1px'\n                        }}>\n                          {stat.unit}\n                        </div>\n                      </div>\n\n                      {/* Description */}\n                      <div style={{\n                        fontSize: '0.9rem',\n                        color: 'rgba(255,255,255,0.9)',\n                        fontWeight: 500,\n                        fontStyle: 'italic'\n                      }}>\n                        {stat.description}\n                      </div>\n\n                      {/* Progress bar */}\n                      <div style={{\n                        marginTop: '1rem',\n                        height: '4px',\n                        background: 'rgba(255,255,255,0.2)',\n                        borderRadius: '2px',\n                        overflow: 'hidden'\n                      }}>\n                        <div style={{\n                          height: '100%',\n                          width: `${Math.min(100, (index + 1) * 25)}%`,\n                          background: stat.bgGradient,\n                          borderRadius: '2px',\n                          animation: 'slideIn 2s ease-out'\n                        }} />\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n\n              {/* Epic Action Center */}\n              <div style={{\n                background: 'linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.1))',\n                backdropFilter: 'blur(20px)',\n                borderRadius: '25px',\n                padding: '2rem',\n                marginBottom: '2rem',\n                border: '1px solid rgba(255,255,255,0.2)',\n                boxShadow: '0 25px 50px rgba(0,0,0,0.2)',\n                position: 'relative',\n                overflow: 'hidden'\n              }}>\n                <div style={{\n                  position: 'absolute',\n                  top: '-100px',\n                  right: '-100px',\n                  width: '300px',\n                  height: '300px',\n                  background: 'conic-gradient(from 0deg, #ff4757, #3742fa, #2ed573, #ffa502, #ff4757)',\n                  borderRadius: '50%',\n                  opacity: 0.1,\n                  animation: 'rotate 30s linear infinite'\n                }} />\n\n                <h2 style={{\n                  margin: '0 0 2rem 0',\n                  fontSize: '2rem',\n                  fontWeight: 800,\n                  color: 'white',\n                  textAlign: 'center',\n                  textShadow: '0 2px 4px rgba(0,0,0,0.3)'\n                }}>\n                  🎮 MISSION CONTROL CENTER 🎮\n                </h2>\n\n                <div style={{\n                  display: 'grid',\n                  gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                  gap: '1.5rem'\n                }}>\n                  {[\n                    {\n                      icon: '🎯',\n                      title: 'BATTLE MODE',\n                      subtitle: 'Take Quiz Challenge',\n                      desc: 'Test your skills in epic battles!',\n                      action: () => setActiveTab('quizzes'),\n                      color: '#ff4757',\n                      bgGradient: 'linear-gradient(135deg, #ff4757, #ff6b7a)'\n                    },\n                    {\n                      icon: '⚔️',\n                      title: 'CODE ARENA',\n                      subtitle: 'DSA Combat Zone',\n                      desc: 'Sharpen your coding weapons!',\n                      action: () => setActiveTab('dsa'),\n                      color: '#3742fa',\n                      bgGradient: 'linear-gradient(135deg, #3742fa, #5352ed)'\n                    },\n                    {\n                      icon: '📜',\n                      title: 'SCROLL REVIEW',\n                      subtitle: 'Resume Enhancement',\n                      desc: 'Upgrade your legendary resume!',\n                      action: () => setActiveTab('resume'),\n                      color: '#2ed573',\n                      bgGradient: 'linear-gradient(135deg, #2ed573, #7bed9f)'\n                    },\n                    {\n                      icon: '�',\n                      title: 'KNOWLEDGE VAULT',\n                      subtitle: 'Study Materials',\n                      desc: 'Access ancient wisdom scrolls!',\n                      action: () => setActiveTab('resources'),\n                      color: '#ffa502',\n                      bgGradient: 'linear-gradient(135deg, #ffa502, #ffb142)'\n                    }\n                  ].map((action, index) => (\n                    <div key={index}\n                      onClick={action.action}\n                      style={{\n                        background: 'linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.05))',\n                        backdropFilter: 'blur(15px)',\n                        borderRadius: '20px',\n                        padding: '1.5rem',\n                        cursor: 'pointer',\n                        transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',\n                        border: `2px solid ${action.color}30`,\n                        position: 'relative',\n                        overflow: 'hidden',\n                        boxShadow: `0 10px 30px rgba(0,0,0,0.1), 0 0 0 1px ${action.color}20`\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.transform = 'translateY(-8px) scale(1.05)';\n                        e.currentTarget.style.boxShadow = `0 20px 40px rgba(0,0,0,0.2), 0 0 30px ${action.color}40`;\n                        e.currentTarget.style.borderColor = action.color + '80';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.transform = 'translateY(0) scale(1)';\n                        e.currentTarget.style.boxShadow = `0 10px 30px rgba(0,0,0,0.1), 0 0 0 1px ${action.color}20`;\n                        e.currentTarget.style.borderColor = action.color + '30';\n                      }}\n                    >\n                      {/* Animated glow */}\n                      <div style={{\n                        position: 'absolute',\n                        top: '50%',\n                        left: '50%',\n                        width: '100px',\n                        height: '100px',\n                        background: action.bgGradient,\n                        borderRadius: '50%',\n                        transform: 'translate(-50%, -50%)',\n                        opacity: 0.1,\n                        filter: 'blur(30px)',\n                        animation: 'pulse 3s ease-in-out infinite'\n                      }} />\n\n                      <div style={{ position: 'relative', zIndex: 1, textAlign: 'center' }}>\n                        <div style={{\n                          fontSize: '3rem',\n                          marginBottom: '1rem',\n                          filter: 'drop-shadow(0 0 10px currentColor)',\n                          animation: 'bounce 2s ease-in-out infinite'\n                        }}>\n                          {action.icon}\n                        </div>\n\n                        <div style={{\n                          fontSize: '1.1rem',\n                          fontWeight: 800,\n                          color: 'white',\n                          marginBottom: '0.5rem',\n                          textShadow: `0 0 10px ${action.color}`,\n                          letterSpacing: '1px'\n                        }}>\n                          {action.title}\n                        </div>\n\n                        <div style={{\n                          fontSize: '0.8rem',\n                          color: action.color,\n                          fontWeight: 600,\n                          marginBottom: '0.75rem',\n                          textTransform: 'uppercase',\n                          letterSpacing: '0.5px'\n                        }}>\n                          {action.subtitle}\n                        </div>\n\n                        <div style={{\n                          fontSize: '0.85rem',\n                          color: 'rgba(255,255,255,0.8)',\n                          fontStyle: 'italic',\n                          lineHeight: 1.4\n                        }}>\n                          {action.desc}\n                        </div>\n\n                        {/* Power level indicator */}\n                        <div style={{\n                          marginTop: '1rem',\n                          height: '3px',\n                          background: 'rgba(255,255,255,0.2)',\n                          borderRadius: '2px',\n                          overflow: 'hidden'\n                        }}>\n                          <div style={{\n                            height: '100%',\n                            width: `${75 + index * 5}%`,\n                            background: action.bgGradient,\n                            borderRadius: '2px',\n                            animation: 'slideIn 1.5s ease-out'\n                          }} />\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              {/* Recent Activity & Resume Management */}\n              <div style={{\n                display: 'grid',\n                gridTemplateColumns: '1fr 1fr',\n                gap: '2rem'\n              }}>\n\n                {/* Recent Activity */}\n                <div style={{\n                  background: globalStyles.currentTheme.surface,\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n                  border: `1px solid ${globalStyles.currentTheme.border}`\n                }}>\n                  <h3 style={{\n                    margin: '0 0 1.5rem 0',\n                    fontSize: '1.3rem',\n                    fontWeight: 600,\n                    color: globalStyles.currentTheme.text\n                  }}>\n                    📈 Recent Activity\n                  </h3>\n\n                  <div style={{\n                    maxHeight: '250px',\n                    overflowY: 'auto'\n                  }}>\n                    {activityLog.slice(0, 5).map((log, index) => (\n                      <div key={index} style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1rem',\n                        padding: '0.75rem',\n                        borderRadius: '8px',\n                        marginBottom: '0.5rem',\n                        background: index % 2 === 0 ? globalStyles.currentTheme.secondary : 'transparent',\n                        transition: 'all 0.3s ease'\n                      }}>\n                        <div style={{\n                          width: '10px',\n                          height: '10px',\n                          borderRadius: '50%',\n                          background: log.type === 'login' ? '#4ECDC4' : '#45B7D1',\n                          flexShrink: 0\n                        }} />\n                        <div style={{ flex: 1 }}>\n                          <div style={{\n                            fontSize: '0.9rem',\n                            fontWeight: 500,\n                            color: globalStyles.currentTheme.text,\n                            marginBottom: '0.2rem'\n                          }}>\n                            {log.msg}\n                          </div>\n                          <div style={{\n                            fontSize: '0.8rem',\n                            color: globalStyles.currentTheme.textLight\n                          }}>\n                            {new Date(log.date).toLocaleString()}\n                          </div>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Resume Management */}\n                <div style={{\n                  background: globalStyles.currentTheme.surface,\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n                  border: `1px solid ${globalStyles.currentTheme.border}`\n                }}>\n                  <h3 style={{\n                    margin: '0 0 1.5rem 0',\n                    fontSize: '1.3rem',\n                    fontWeight: 600,\n                    color: globalStyles.currentTheme.text\n                  }}>\n                    📄 Resume Management\n                  </h3>\n\n                  <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n                    <label style={{\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.75rem',\n                      padding: '1rem',\n                      borderRadius: '12px',\n                      background: globalStyles.currentTheme.secondary,\n                      border: `2px dashed ${globalStyles.currentTheme.border}`,\n                      cursor: resumeUploadLoading ? 'not-allowed' : 'pointer',\n                      transition: 'all 0.3s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      if (!resumeUploadLoading) {\n                        e.currentTarget.style.borderColor = globalStyles.currentTheme.primary;\n                        e.currentTarget.style.background = globalStyles.currentTheme.primary + '10';\n                      }\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.borderColor = globalStyles.currentTheme.border;\n                      e.currentTarget.style.background = globalStyles.currentTheme.secondary;\n                    }}>\n                      <FiUpload size={20} color={globalStyles.currentTheme.primary} />\n                      <div>\n                        <div style={{\n                          fontWeight: 600,\n                          color: globalStyles.currentTheme.text,\n                          marginBottom: '0.2rem'\n                        }}>\n                          {resumeUploadLoading ? 'Uploading...' : 'Upload Resume'}\n                        </div>\n                        <div style={{\n                          fontSize: '0.8rem',\n                          color: globalStyles.currentTheme.textLight\n                        }}>\n                          PDF files only\n                        </div>\n                      </div>\n                      <input\n                        type=\"file\"\n                        accept=\"application/pdf\"\n                        onChange={handleResumeUpload}\n                        disabled={resumeUploadLoading}\n                        style={{ display: 'none' }}\n                      />\n                    </label>\n\n                    {resumeUrl && (\n                      <a\n                        href={resumeUrl}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.75rem',\n                          padding: '1rem',\n                          borderRadius: '12px',\n                          background: '#4ECDC4',\n                          color: 'white',\n                          textDecoration: 'none',\n                          fontWeight: 600,\n                          transition: 'all 0.3s ease'\n                        }}\n                        onMouseEnter={(e) => {\n                          e.currentTarget.style.background = '#3DBDB6';\n                          e.currentTarget.style.transform = 'translateY(-2px)';\n                        }}\n                        onMouseLeave={(e) => {\n                          e.currentTarget.style.background = '#4ECDC4';\n                          e.currentTarget.style.transform = 'translateY(0)';\n                        }}\n                      >\n                        <FiFileText size={20} />\n                        <span>View Resume</span>\n                      </a>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Chat Interface */}\n        {activeTab === \"resume\" && (\n          <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>\n            <div style={getStyle('card')}>\n              <h2 style={{\n                marginTop: 0,\n                color: '#333'\n              }}>Career Assistant</h2>\n              <p style={{\n                opacity: 0.8,\n                marginBottom: '24px',\n                color: '#666'\n              }}>\n                Get personalized resume advice and career guidance\n              </p>\n\n              {/* Chat messages */}\n              <div style={{\n                height: '50vh',\n                overflowY: 'auto',\n                marginBottom: '24px',\n                padding: '16px',\n                backgroundColor: '#f5f5f5',\n                border: '1px solid #e0e0e0',\n                borderRadius: '8px'\n              }}>\n\n                {messages.length === 0 ? (\n                  <div style={{\n                    height: '100%',\n                    display: 'flex',\n                    flexDirection: 'column',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    textAlign: 'center',\n                    opacity: 0.7\n                  }}>\n                    <div style={{ fontSize: '48px', marginBottom: '16px' }}>💬</div>\n                    <h3 style={{\n                      margin: 0,\n                      color: '#333'\n                    }}>Start a conversation</h3>\n                    <p style={{\n                      color: '#666'\n                    }}>Ask about resumes, interviews, or career advice</p>\n                  </div>\n                ) : (\n                  messages.map((msg, idx) => (\n                    <div\n                      key={idx}\n                      style={{\n                        ...(msg.role === 'user' ? getStyle('chatBubbleUser') : getStyle('chatBubbleBot')),\n                        animation: 'fadeIn 0.3s ease'\n                      }}\n                    >\n                      {msg.role === 'bot' ? (\n                        <ReactMarkdown>{msg.content}</ReactMarkdown>\n                      ) : (\n                        msg.content\n                      )}\n                    </div>\n                  ))\n                )}\n                {loading && (\n                  <div style={getStyle('chatBubbleBot')}>\n                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n                      <div style={{\n                        width: '10px',\n                        height: '10px',\n                        borderRadius: '50%',\n                        backgroundColor: '#1976d2',\n                        animation: 'pulse 1.4s infinite ease-in-out'\n                      }} />\n                      <div style={{\n                        width: '10px',\n                        height: '10px',\n                        borderRadius: '50%',\n                        backgroundColor: '#1976d2',\n                        animation: 'pulse 1.4s infinite ease-in-out',\n                        animationDelay: '0.2s'\n                      }} />\n                      <div style={{\n                        width: '10px',\n                        height: '10px',\n                        borderRadius: '50%',\n                        backgroundColor: '#1976d2',\n                        animation: 'pulse 1.4s infinite ease-in-out',\n                        animationDelay: '0.4s'\n                      }} />\n                    </div>\n                  </div>\n                )}\n                <div ref={chatEndRef} />\n              </div>\n\n              {/* Input area */}\n              <form\n                style={{ display: 'flex', gap: '12px' }}\n                onSubmit={e => {\n                  e.preventDefault();\n                  sendMessage();\n                }}\n              >\n                <input\n                  type=\"text\"\n                  placeholder=\"Type your message...\"\n                  style={getStyle('inputField')}\n                  value={input}\n                  onChange={e => setInput(e.target.value)}\n                  disabled={loading}\n                />\n                <button\n                  type=\"submit\"\n                  style={{\n                    ...getStyle('buttonPrimary'),\n                    minWidth: '100px'\n                  }}\n                  disabled={loading || !input.trim()}\n                >\n                  {loading ? 'Sending...' : 'Send'}\n                </button>\n              </form>\n            </div>\n          </div>\n        )}\n\n        {/* Enhanced DSA Company Questions */}\n        {activeTab === \"dsa\" && (\n          <div style={{ padding: '24px' }}>\n            <div style={getStyle('card')}>\n              {/* Header with revert button */}\n              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>\n                <div>\n                  <h2 style={{ marginTop: 0, marginBottom: '8px' }}>🚀 Company Wise DSA Questions</h2>\n                  <p style={{ opacity: 0.8, margin: 0 }}>\n                    Explore DSA questions from top companies with enhanced filtering and favorites\n                  </p>\n                </div>\n                {showRevertButton && (\n                  <button\n                    onClick={revertHeaderChanges}\n                    style={{\n                      ...getStyle('buttonPrimary'),\n                      background: '#ff6b6b',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '8px',\n                      fontSize: '14px',\n                      padding: '8px 16px',\n                      border: 'none',\n                      borderRadius: '8px',\n                      color: 'white',\n                      cursor: 'pointer',\n                      transition: 'all 0.3s ease'\n                    }}\n                    onMouseEnter={(e) => e.target.style.background = '#ff5252'}\n                    onMouseLeave={(e) => e.target.style.background = '#ff6b6b'}\n                  >\n                    <FiRefreshCw size={16} />\n                    Revert Header Color\n                  </button>\n                )}\n              </div>\n\n              {/* Category Tabs */}\n              <div style={{\n                display: 'flex',\n                gap: '8px',\n                marginBottom: '20px',\n                flexWrap: 'wrap',\n                borderBottom: '1px solid #eee',\n                paddingBottom: '16px'\n              }}>\n                {['all', ...Object.keys(companyCategories)].map(category => (\n                  <button\n                    key={category}\n                    onClick={() => setSelectedCategory(category)}\n                    style={{\n                      padding: '8px 16px',\n                      borderRadius: '20px',\n                      border: selectedCategory === category\n                        ? 'none'\n                        : '1px solid #ddd',\n                      background: selectedCategory === category\n                        ? globalStyles.currentTheme.primary\n                        : 'transparent',\n                      color: selectedCategory === category\n                        ? 'white'\n                        : '#666',\n                      cursor: 'pointer',\n                      fontSize: '14px',\n                      fontWeight: selectedCategory === category ? 600 : 400,\n                      transition: 'all 0.3s ease',\n                      textTransform: 'capitalize'\n                    }}\n                    onMouseEnter={(e) => {\n                      if (selectedCategory !== category) {\n                        e.target.style.background = '#f5f5f5';\n                      }\n                    }}\n                    onMouseLeave={(e) => {\n                      if (selectedCategory !== category) {\n                        e.target.style.background = 'transparent';\n                      }\n                    }}\n                  >\n                    {category === 'all' ? '🌟 All' : `${category === 'FAANG' ? '🔥' : category === 'Big Tech' ? '💻' : category === 'Startups' ? '🚀' : category === 'Finance' ? '💰' : category === 'Indian' ? '🇮🇳' : '🏢'} ${category}`}\n                  </button>\n                ))}\n              </div>\n\n              {/* Controls Row */}\n              <div style={{\n                display: 'flex',\n                gap: '16px',\n                marginBottom: '24px',\n                flexWrap: 'wrap',\n                alignItems: 'center'\n              }}>\n                {/* Search box */}\n                <div style={{ position: 'relative', flex: 1, minWidth: '300px' }}>\n                  <div style={{\n                    position: 'absolute',\n                    left: '16px',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    color: '#666'\n                  }}>\n                    <FiSearch size={20} />\n                  </div>\n                  <input\n                    type=\"text\"\n                    placeholder=\"Search companies...\"\n                    style={{\n                      ...getStyle('inputField'),\n                      paddingLeft: '48px',\n                      width: '100%'\n                    }}\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                  />\n                  {searchTerm && (\n                    <button\n                      style={{\n                        position: 'absolute',\n                        right: '16px',\n                        top: '50%',\n                        transform: 'translateY(-50%)',\n                        background: 'none',\n                        border: 'none',\n                        color: '#666',\n                        cursor: 'pointer'\n                      }}\n                      onClick={() => setSearchTerm(\"\")}\n                    >\n                      <FiX size={20} />\n                    </button>\n                  )}\n                </div>\n\n                {/* Sort dropdown */}\n                <select\n                  value={sortBy}\n                  onChange={(e) => setSortBy(e.target.value)}\n                  style={{\n                    ...getStyle('inputField'),\n                    width: 'auto',\n                    minWidth: '150px'\n                  }}\n                >\n                  <option value=\"name\">📝 Sort by Name</option>\n                  <option value=\"favorites\">⭐ Favorites First</option>\n                </select>\n              </div>\n\n              {/* Recent Companies */}\n              {recentCompanies.length > 0 && (\n                <div style={{\n                  marginBottom: '24px',\n                  padding: '16px',\n                  borderRadius: '12px',\n                  background: '#f8f9fa',\n                  border: '1px solid #e9ecef'\n                }}>\n                  <h3 style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '8px',\n                    fontSize: '16px',\n                    marginBottom: '12px',\n                    color: '#333',\n                    margin: '0 0 12px 0'\n                  }}>\n                    <FiClock color=\"#666\" /> Recently Viewed\n                  </h3>\n                  <div style={{\n                    display: 'flex',\n                    gap: '8px',\n                    flexWrap: 'wrap'\n                  }}>\n                    {recentCompanies.map(company => (\n                      <button\n                        key={company}\n                        onClick={() => handleCompanyClick(company)}\n                        style={{\n                          padding: '6px 12px',\n                          borderRadius: '16px',\n                          border: `1px solid ${globalStyles.currentTheme.primary}`,\n                          background: 'transparent',\n                          color: globalStyles.currentTheme.primary,\n                          cursor: 'pointer',\n                          fontSize: '12px',\n                          transition: 'all 0.3s ease'\n                        }}\n                        onMouseEnter={(e) => {\n                          e.target.style.background = globalStyles.currentTheme.primary;\n                          e.target.style.color = 'white';\n                        }}\n                        onMouseLeave={(e) => {\n                          e.target.style.background = 'transparent';\n                          e.target.style.color = globalStyles.currentTheme.primary;\n                        }}\n                      >\n                        {company}\n                      </button>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {/* Companies grid */}\n              <div style={{\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fill, minmax(220px, 1fr))',\n                gap: '16px',\n                marginTop: '24px'\n              }}>\n                {filteredCompanies.map((company, index) => (\n                  <div\n                    key={index}\n                    style={{\n                      ...getStyle('companyCard'),\n                      position: 'relative',\n                      transform: favoriteCompanies.includes(company) ? 'scale(1.02)' : 'scale(1)',\n                      border: favoriteCompanies.includes(company)\n                        ? `2px solid ${globalStyles.currentTheme.primary}`\n                        : `1px solid ${globalStyles.currentTheme.border}`,\n                      background: globalStyles.currentTheme.surface,\n                      color: globalStyles.currentTheme.text,\n                      animation: `fadeIn 0.3s ease ${index * 0.1}s both`,\n                      boxShadow: `0 4px 6px ${globalStyles.currentTheme.shadow}`\n                    }}\n                    onClick={() => handleCompanyClick(company)}\n                  >\n                    {/* Favorite button */}\n                    <button\n                      onClick={(e) => toggleFavorite(company, e)}\n                      style={{\n                        position: 'absolute',\n                        top: '8px',\n                        right: '8px',\n                        background: 'none',\n                        border: 'none',\n                        cursor: 'pointer',\n                        color: favoriteCompanies.includes(company) ? '#ff6b6b' : '#ccc',\n                        transition: 'all 0.3s ease',\n                        fontSize: '18px'\n                      }}\n                    >\n                      <FiHeart fill={favoriteCompanies.includes(company) ? 'currentColor' : 'none'} />\n                    </button>\n\n                    {/* Company initial with gradient */}\n                    <div style={{\n                      width: '56px',\n                      height: '56px',\n                      borderRadius: '50%',\n                      background: `linear-gradient(135deg, ${globalStyles.currentTheme.primary}, ${globalStyles.currentTheme.primaryDark})`,\n                      color: 'white',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      fontSize: '24px',\n                      fontWeight: 700,\n                      marginBottom: '12px',\n                      boxShadow: `0 4px 8px ${globalStyles.currentTheme.shadow}`\n                    }}>\n                      {company.charAt(0)}\n                    </div>\n\n                    {/* Company name */}\n                    <div style={{\n                      fontWeight: 600,\n                      textAlign: 'center',\n                      fontSize: '14px',\n                      marginBottom: '8px'\n                    }}>\n                      {company}\n                    </div>\n\n                    {/* Mock stats */}\n                    <div style={{\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      fontSize: '12px',\n                      opacity: 0.7,\n                      marginTop: '8px'\n                    }}>\n                      <span>📊 {Math.floor(Math.random() * 50) + 10} Questions</span>\n                      <span>⭐ {(Math.random() * 2 + 3).toFixed(1)}</span>\n                    </div>\n\n                    {/* Category badge */}\n                    {Object.entries(companyCategories).map(([category, companies]) => {\n                      if (companies.some(c => c.toLowerCase() === company.toLowerCase())) {\n                        return (\n                          <div\n                            key={category}\n                            style={{\n                              position: 'absolute',\n                              top: '8px',\n                              left: '8px',\n                              background: globalStyles.currentTheme.primary,\n                              color: 'white',\n                              padding: '2px 6px',\n                              borderRadius: '8px',\n                              fontSize: '10px',\n                              fontWeight: 600\n                            }}\n                          >\n                            {category}\n                          </div>\n                        );\n                      }\n                      return null;\n                    })}\n                  </div>\n                ))}\n              </div>\n\n              {/* No results message */}\n              {filteredCompanies.length === 0 && (\n                <div style={{\n                  textAlign: 'center',\n                  padding: '40px',\n                  opacity: 0.7,\n                  color: '#666'\n                }}>\n                  <div style={{ fontSize: '48px', marginBottom: '16px' }}>🔍</div>\n                  <h3 style={{ color: '#333' }}>No companies found</h3>\n                  <p style={{ color: '#666' }}>Try adjusting your search or category filter</p>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* Quizzes Section */}\n        {activeTab === \"quizzes\" && (\n          <div style={{ padding: '24px' }}>\n            <div style={getStyle('card')}>\n              <h2 style={{ marginTop: 0 }}>Career Quizzes</h2>\n              <p style={{ opacity: 0.8, marginBottom: '24px' }}>\n                Test your knowledge with our career-focused quizzes!\n              </p>\n\n              <div style={{\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',\n                gap: '16px'\n              }}>\n                {quizButtons.map((quiz, index) => (\n                  <div\n                    key={index}\n                    style={getStyle('quizCard')}\n                    onClick={() => openQuizLink(quiz.link)}\n                  >\n                    <div>\n                      <h3 style={{ margin: '0 0 8px 0' }}>{quiz.title}</h3>\n                      <p style={{\n                        margin: 0,\n                        fontSize: '14px',\n                        opacity: 0.8\n                      }}>\n                        {quiz.description}\n                      </p>\n                    </div>\n                    <div style={{ color: '#1976d2' }}>\n                      <FiExternalLink size={20} />\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Other tabs with lazy loading */}\n        {activeTab === \"coding\" && (\n          <Suspense fallback={<div className=\"loading-spinner\" style={{ margin: '50px auto' }} />}>\n            <LazyCoding />\n          </Suspense>\n        )}\n\n        {activeTab === \"resources\" && (\n          <div style={{ padding: '24px' }}>\n            <div style={getStyle('card')}>\n              <h2 style={{\n                marginTop: 0,\n                color: '#333'\n              }}>Resources</h2>\n              <p style={{\n                opacity: 0.8,\n                marginBottom: '24px',\n                color: '#666'\n              }}>\n                Upload and manage your study materials and notes\n              </p>\n\n              <div style={{ marginBottom: '24px' }}>\n                <label style={{\n                  ...getStyle('buttonPrimary'),\n                  background: '#f5f5f5',\n                  color: '#333',\n                  border: '1px solid #ddd',\n                  cursor: resourceUploadLoading ? 'not-allowed' : 'pointer'\n                }}>\n                  <FiUpload />\n                  {resourceUploadLoading ? 'Uploading...' : 'Upload Resource'}\n                  <input\n                    type=\"file\"\n                    accept=\".pdf,.doc,.docx,.txt\"\n                    onChange={handleResourceUpload}\n                    disabled={resourceUploadLoading}\n                    style={{ display: 'none' }}\n                  />\n                </label>\n              </div>\n\n              <div>\n                <h3 style={{\n                  marginBottom: '16px',\n                  color: '#333'\n                }}>Your Resources</h3>\n                {userResources.length === 0 ? (\n                  <p style={{\n                    opacity: 0.7,\n                    color: '#666'\n                  }}>No resources uploaded yet</p>\n                ) : (\n                  <div style={{\n                    backgroundColor: '#f5f5f5',\n                    border: '1px solid #e0e0e0',\n                    borderRadius: '8px',\n                    padding: '16px'\n                  }}>\n                    {userResources.map((file, idx) => {\n                      const { data: urlData } = supabase.storage.from('resources').getPublicUrl(`${user.id}/${file.name}`);\n                      return (\n                        <div key={idx} style={{\n                          padding: '12px',\n                          borderBottom: '1px solid #eee',\n                          display: 'flex',\n                          justifyContent: 'space-between',\n                          alignItems: 'center'\n                        }}>\n                          <span style={{\n                            color: '#333'\n                          }}>{file.name}</span>\n                          <a\n                            href={urlData.publicUrl}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            style={{\n                              color: '#1976d2',\n                              textDecoration: 'none',\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '4px'\n                            }}\n                          >\n                            <FiExternalLink size={16} />\n                            Open\n                          </a>\n                        </div>\n                      );\n                    })}\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n        {activeTab === \"academics\" && (\n          <Suspense fallback={<div className=\"loading-spinner\" style={{ margin: '50px auto' }} />}>\n            <LazyExams />\n          </Suspense>\n        )}\n\n        {activeTab === \"faq\" && (\n          <Suspense fallback={<div className=\"loading-spinner\" style={{ margin: '50px auto' }} />}>\n            <LazyFaq />\n          </Suspense>\n        )}\n        {activeTab === \"admin\" && user?.email === ADMIN_EMAIL && (\n          <div style={{ padding: '24px' }}>\n            <div style={getStyle('card')}>\n              <h2 style={{\n                marginTop: 0,\n                color: '#333'\n              }}>Admin Panel</h2>\n              <div style={{\n                display: 'flex',\n                gap: '16px',\n                marginBottom: '24px'\n              }}>\n                <button\n                  style={{\n                    ...getStyle('buttonPrimary'),\n                    background: adminTab === 'users' ?\n                      globalStyles.currentTheme.primary : 'transparent',\n                    color: adminTab === 'users' ?\n                      'white' : '#333',\n                    border: '1px solid #ddd'\n                  }}\n                  onClick={() => setAdminTab('users')}\n                >\n                  Users\n                </button>\n                <button\n                  style={{\n                    ...getStyle('buttonPrimary'),\n                    background: adminTab === 'resources' ?\n                      globalStyles.currentTheme.primary : 'transparent',\n                    color: adminTab === 'resources' ?\n                      'white' : '#333',\n                    border: '1px solid #ddd'\n                  }}\n                  onClick={() => setAdminTab('resources')}\n                >\n                  Resources\n                </button>\n              </div>\n\n              {adminTab === 'users' && (\n                <div>\n                  <h3 style={{\n                    marginBottom: '16px',\n                    color: '#333'\n                  }}>All Users</h3>\n                  <div style={{\n                    backgroundColor: '#f5f5f5',\n                    border: '1px solid #e0e0e0',\n                    borderRadius: '8px',\n                    padding: '16px'\n                  }}>\n                    {allUsers.map((user, idx) => (\n                      <div key={idx} style={{\n                        padding: '12px',\n                        borderBottom: '1px solid #eee',\n                        color: '#333'\n                      }}>\n                        {user.email}\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {adminTab === 'resources' && (\n                <div>\n                  <h3 style={{\n                    marginBottom: '16px',\n                    color: '#333'\n                  }}>All Resources</h3>\n                  <p style={{\n                    opacity: 0.7,\n                    color: '#666'\n                  }}>Resource management coming soon</p>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </main>\n\n      {/* Performance-optimized Notification */}\n      <Notification\n        notification={notification}\n        onClose={() => setNotification(null)}\n      />\n    </div>\n  );\n};\nexport default EduAIChatBot;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,OAAO;AAC1F,SAASC,MAAM,EAAEC,GAAG,QAAQ,oBAAoB;AAChD,SAASC,IAAI,EAAEC,EAAE,QAAQ,kBAAkB;AAC3C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,kBAAkB,QAAQ,eAAe;AAClD,SAASC,MAAM,EAAEC,OAAO,EAAEC,SAAS,EAAEC,YAAY,QAAQ,cAAc;AACvE,SAASC,aAAa,EAAEC,UAAU,EAAEC,qBAAqB,EAAEC,qBAAqB,QAAQ,uBAAuB;AAC/G,SAASC,QAAQ,QAAQ,SAAS;AAClC,SACEC,UAAU,EAAEC,MAAM,EAAEC,YAAY,EAAEC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EACnEC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,aAAa,EACrEC,cAAc,EAAEC,OAAO,EAAEC,OAAO,EAAEC,WAAW,QACxC,gBAAgB;AACvB,SAASC,YAAY,QAAQ,uBAAuB;AACpD,OAAO,WAAW;AAClB,OAAO,kBAAkB;;AAEzB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,OAAO,gBAAG3C,KAAK,CAAC4C,IAAI,CAAAC,EAAA,GAACA,CAAA,KAAM,MAAM,CAAC,OAAO,CAAC,CAAC;AAACC,GAAA,GAA5CH,OAAO;AACb,MAAMI,SAAS,gBAAG/C,KAAK,CAAC4C,IAAI,CAAAI,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,SAAS,CAAC,CAAC;AAACC,GAAA,GAAhDF,SAAS;AACf,MAAMG,UAAU,gBAAGlD,KAAK,CAAC4C,IAAI,CAAAO,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,UAAU,CAAC,CAAC;;AAEvD;AAAAC,GAAA,GAFMF,UAAU;AAGhB,MAAMG,SAAS,gBAAGrD,KAAK,CAAC4C,IAAI,CAAAU,GAAA,GAAC,MAAAA,CAAA,KAAY;EACvC,MAAM;IAAEC;EAAI,CAAC,GAAG,MAAM,MAAM,CAAC,iBAAiB,CAAC;EAC/C,MAAM;IAAEC,KAAK;IAAEC,UAAU;IAAEC,aAAa;IAAEC,WAAW;IAAEC,OAAO;IAAEC;EAAO,CAAC,GAAG,MAAM,MAAM,CAAC,UAAU,CAAC;EACnGL,KAAK,CAACM,QAAQ,CAACL,UAAU,EAAEC,aAAa,EAAEC,WAAW,EAAEC,OAAO,EAAEC,MAAM,CAAC;EACvE,OAAO;IAAEE,OAAO,EAAER;EAAI,CAAC;AACzB,CAAC,CAAC;AAACS,GAAA,GALGX,SAAS;AAOf,MAAMY,YAAY,gBAAGjE,KAAK,CAAC4C,IAAI,CAAAsB,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,gBAAgB,CAAC,CAAC;;AAE/D;AAAAC,GAAA,GAFMF,YAAY;AAGlB,MAAMG,mBAAmB,GAAGxD,YAAY,CAACyD,GAAG,CAACC,IAAI,IAAI;EACnD,MAAMC,OAAO,GAAG;IACd,QAAQ,eAAE7B,OAAA,CAACnB,UAAU;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxB,KAAK,eAAEjC,OAAA,CAAClB,MAAM;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjB,QAAQ,eAAEjC,OAAA,CAACR,QAAQ;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtB,WAAW,eAAEjC,OAAA,CAACV,WAAW;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5B,SAAS,eAAEjC,OAAA,CAACP,aAAa;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5B,UAAU,eAAEjC,OAAA,CAACT,WAAW;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3B,WAAW,eAAEjC,OAAA,CAACf,MAAM;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvB,KAAK,eAAEjC,OAAA,CAACjB,YAAY;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvB,OAAO,eAAEjC,OAAA,CAACb,QAAQ;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACtB,CAAC;EAED,OAAO;IACL,GAAGL,IAAI;IACPM,IAAI,EAAEL,OAAO,CAACD,IAAI,CAACO,GAAG,CAAC,IAAIN,OAAO,CAACD,IAAI,CAACQ,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC,iBAAIrC,OAAA,CAAChB,OAAO;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC5E,CAAC;AACH,CAAC,CAAC;AAEF,MAAMK,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB;EACA,MAAM;IAAEC,OAAO;IAAEC,WAAW;IAAEC;EAAU,CAAC,GAAG/D,qBAAqB,CAAC,CAAC;EACnE,MAAM;IAAEgE;EAAS,CAAC,GAAGnE,aAAa,CAAC,CAAC;EACpC,MAAM;IAAEoE,MAAM,EAAEC,WAAW;IAAEC,MAAM,EAAEC,aAAa;IAAEC,KAAK,EAAEC;EAAa,CAAC,GAAGxE,UAAU,CAAC,CAAC;EACxF,MAAM;IAAEyE;EAAc,CAAC,GAAGxE,qBAAqB,CAAC,CAAC;;EAEjD;EACA,MAAM,CAACyE,KAAK,EAAEC,QAAQ,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC8F,QAAQ,EAAEC,WAAW,CAAC,GAAG/F,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgG,MAAM,EAAEC,SAAS,CAAC,GAAGjG,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACkG,OAAO,EAAEC,UAAU,CAAC,GAAGnG,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoG,SAAS,EAAEC,YAAY,CAAC,GAAGrG,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACsG,SAAS,EAAEC,YAAY,CAAC,GAAGvG,QAAQ,CAAC,WAAW,CAAC;EACvD,MAAM,CAACwG,UAAU,EAAEC,aAAa,CAAC,GAAGzG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0G,aAAa,EAAEC,gBAAgB,CAAC,GAAG3G,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAAC4G,IAAI,EAAEC,OAAO,CAAC,GAAG7G,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAAC8G,UAAU,EAAEC,aAAa,CAAC,GAAG/G,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAM,CAACgH,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjH,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACkH,SAAS,EAAEC,YAAY,CAAC,GAAGnH,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACoH,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGrH,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACsH,aAAa,CAAC,GAAGtH,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACuH,QAAQ,CAAC,GAAGvH,QAAQ,CAAC,EAAE,CAAC;EAC/B,MAAM,CAACwH,QAAQ,EAAEC,WAAW,CAAC,GAAGzH,QAAQ,CAAC,OAAO,CAAC;EACjD,MAAM,CAAC0H,YAAY,EAAEC,eAAe,CAAC,GAAG3H,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC4H,WAAW,EAAEC,cAAc,CAAC,GAAG7H,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM8H,UAAU,GAAG5H,MAAM,CAAC,IAAI,CAAC;;EAE/B;EACA,MAAM,CAAC6H,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhI,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACiI,MAAM,EAAEC,SAAS,CAAC,GAAGlI,QAAQ,CAAC,MAAM,CAAC;EAC5C,MAAM,CAACmI,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpI,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACqI,eAAe,EAAEC,kBAAkB,CAAC,GAAGtI,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACuI,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxI,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAMyI,QAAQ,GAAGrI,OAAO,CAAC,MAAM;IAC7B,MAAMsI,YAAY,GAAG,0CAA0C;IAC/D,MAAMC,iBAAiB,GAAG,kNAAkN;IAC5O,OAAOpG,YAAY,CAACmG,YAAY,EAAEC,iBAAiB,CAAC;EACtD,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,OAAO,GAAG,yCAAyC;EACzD,MAAMC,WAAW,GAAG,mBAAmB;;EAEvC;EACA,MAAMC,KAAK,GAAG1I,OAAO,CAAC,MAAMiB,QAAQ,CAACyF,UAAU,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;;EAE/D;EACA,MAAMiC,iBAAiB,GAAG;IACxB,OAAO,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC;IAC7D,UAAU,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,CAAC;IACpF,UAAU,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC;IACrF,SAAS,EAAE,CAAC,eAAe,EAAE,UAAU,EAAE,gBAAgB,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,CAAC;IAC/F,YAAY,EAAE,CAAC,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,KAAK,CAAC;IACzE,YAAY,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,aAAa,CAAC;IAC7E,QAAQ,EAAE,CAAC,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,OAAO,EAAE,QAAQ,CAAC;IACrE,QAAQ,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ;EACtF,CAAC;;EAED;EACA,MAAMC,SAAS,GAAG,CACf,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EACxE,QAAQ,EAAE,eAAe,EAAE,SAAS,EAAE,YAAY,EAAE,QAAQ,EAAE,KAAK,EACnE,QAAQ,EAAE,kBAAkB,EAAE,SAAS,EAAE,OAAO,EAAE,iBAAiB,EACnE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,cAAc,EAAE,UAAU,EAChE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,EAC9D,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,MAAM,EAAE,aAAa,EAAE,KAAK,EAChE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,QAAQ,EAClE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAC5D,UAAU,EAAE,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAC9D,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,aAAa,EAAE,QAAQ,EAC3D,SAAS,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EACjE,WAAW,EAAE,WAAW,EAAE,eAAe,EAAE,SAAS,EAAE,SAAS,EAC/D,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAC7D,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,SAAS,EAC1D,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,EAChE,OAAO,EAAE,eAAe,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EACnE,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EACzD,sBAAsB,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EACnE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,aAAa,EACtD,iBAAiB,EAAE,cAAc,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAC3D,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EAC/D,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EACnE,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EACzD,gBAAgB,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAC5D,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,EAChE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAClE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,uBAAuB,EAAE,oBAAoB,EACxE,QAAQ,EAAE,OAAO,EAAE,oBAAoB,EAAE,SAAS,EAAE,WAAW,EAC/D,aAAa,EAAE,SAAS,EAAE,cAAc,EAAE,UAAU,EAAE,OAAO,EAC7D,SAAS,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAC9D,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAE,KAAK,EAAE,YAAY,EAChE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,EACnE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,qBAAqB,EAAE,QAAQ,EAChE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,aAAa,EAC5D,cAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAChE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EACxD,eAAe,EAAE,QAAQ,EAAE,SAAS,EAAE,iBAAiB,EAAE,MAAM,EAC/D,IAAI,EAAE,QAAQ,EAAE,cAAc,EAAE,aAAa,EAAE,SAAS,EACxD,aAAa,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,EAAE,OAAO,EAAE,QAAQ,EAChE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAChE,QAAQ,EAAE,SAAS,EAAE,UAAU,CAChC;;EAED;EACA,MAAMC,WAAW,GAAG,CAClB;IACEpE,KAAK,EAAE,gBAAgB;IACvBqE,WAAW,EAAE,+DAA+D;IAC5EC,IAAI,EAAE;EACR,CAAC,EACD;IACEtE,KAAK,EAAE,oBAAoB;IAC3BqE,WAAW,EAAE,uCAAuC;IACpDC,IAAI,EAAE;EACR,CAAC,EACD;IACEtE,KAAK,EAAE,oBAAoB;IAC3BqE,WAAW,EAAE,mCAAmC;IAChDC,IAAI,EAAE;EACR,CAAC,EACD;IACEtE,KAAK,EAAE,0CAA0C;IACjDqE,WAAW,EAAE,+CAA+C;IAC5DC,IAAI,EAAE;EACR,CAAC,EACD;IACEtE,KAAK,EAAE,qBAAqB;IAC5BqE,WAAW,EAAE,kDAAkD;IAC/DC,IAAI,EAAE;EACR,CAAC,EACD;IACEtE,KAAK,EAAE,UAAU;IACjBqE,WAAW,EAAE,qCAAqC;IAClDC,IAAI,EAAE;EACR,CAAC,EACD;IACEtE,KAAK,EAAE,2CAA2C;IAClDqE,WAAW,EAAE,kCAAkC;IAC/CC,IAAI,EAAE;EACR,CAAC,EACA;IACCtE,KAAK,EAAE,sBAAsB;IAC7BqE,WAAW,EAAE,gCAAgC;IAC7CC,IAAI,EAAE;EAER,CAAC,CACF;;EAED;EACA,MAAMC,gBAAgB,GAAGjJ,WAAW,CAAC,CAACkJ,GAAG,EAAEC,IAAI,GAAG,MAAM,KAAK;IAC3D3B,eAAe,CAAC;MAAE0B,GAAG;MAAEC;IAAK,CAAC,CAAC;EAChC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,WAAW,GAAGpJ,WAAW,CAAEqJ,QAAQ,IAAK;IAC5C3B,cAAc,CAAC4B,IAAI,IAAI,CAAC;MACtBD,QAAQ;MACRE,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnChD,IAAI,EAAE,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiD,KAAK,KAAI;IACvB,CAAC,EAAE,GAAGJ,IAAI,CAACK,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B,CAAC,EAAE,CAAClD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiD,KAAK,CAAC,CAAC;EAEjB,MAAME,eAAe,GAAG5J,WAAW,CAAEyE,GAAG,IAAK;IAC3C,MAAMoF,WAAW,GAAG9E,WAAW,CAAC,CAAC;IACjCqB,YAAY,CAAC3B,GAAG,CAAC;IACjB2E,WAAW,CAAC,gBAAgB3E,GAAG,EAAE,CAAC;IAClCO,SAAS,CAAC6E,WAAW,CAAC;EACxB,CAAC,EAAE,CAAC9E,WAAW,EAAEC,SAAS,EAAEoE,WAAW,CAAC,CAAC;EAEzC,MAAMU,gBAAgB,GAAG9J,WAAW,CAAE+J,SAAS,IAAK;IAClDvD,gBAAgB,CAAC8C,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP,CAACS,SAAS,GAAG,CAACT,IAAI,CAACS,SAAS;IAC9B,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,YAAY,GAAGhK,WAAW,CAAC,YAAY;IAC3C,IAAI;MACF,MAAMsI,QAAQ,CAACjI,IAAI,CAAC4J,OAAO,CAAC,CAAC;MAC7BvD,OAAO,CAAC,IAAI,CAAC;MACbuC,gBAAgB,CAAC,0BAA0B,EAAE,SAAS,CAAC;MACvDG,WAAW,CAAC,iBAAiB,CAAC;IAChC,CAAC,CAAC,OAAOc,KAAK,EAAE;MACdjB,gBAAgB,CAAC,eAAe,EAAE,OAAO,CAAC;IAC5C;EACF,CAAC,EAAE,CAACX,QAAQ,CAACjI,IAAI,EAAE4I,gBAAgB,EAAEG,WAAW,CAAC,CAAC;;EAElD;EACAtJ,SAAS,CAAC,MAAM;IACd,MAAMqK,WAAW,GAAG1J,kBAAkB,CAACJ,IAAI,EAAGoG,IAAI,IAAK;MACrD,IAAIA,IAAI,EAAE;QACRX,SAAS,CAACW,IAAI,CAAC2D,GAAG,CAAC;MACrB,CAAC,MAAM;QACLC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxCtE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC;IACF,OAAO,MAAMmE,WAAW,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;EAENrK,SAAS,CAAC,MAAM;IACd,IAAI+F,MAAM,EAAE;MACV,MAAM0E,gBAAgB,GAAG,MAAAA,CAAA,KAAY;QACnC,MAAMC,OAAO,GAAGpK,GAAG,CAACE,EAAE,EAAE,OAAO,EAAEuF,MAAM,CAAC;QACxC,MAAM4E,OAAO,GAAG,MAAMtK,MAAM,CAACqK,OAAO,CAAC;QAErC,IAAIC,OAAO,CAACC,MAAM,CAAC,CAAC,EAAE;UACpB,MAAMC,QAAQ,GAAGF,OAAO,CAACG,IAAI,CAAC,CAAC;UAC/B;UACAP,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEK,QAAQ,CAACE,EAAE,CAAC;QAC/C,CAAC,MAAM;UACLR,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;QAC9B;QACAtE,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC;MACDuE,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAAC1E,MAAM,CAAC,CAAC;;EAEZ;EACA/F,SAAS,CAAC,MAAM;IACdgL,KAAK,CAAC,oBAAoB,CAAC,CACxBC,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACzBF,IAAI,CAAEH,IAAI,IAAK1E,YAAY,CAAC0E,IAAI,CAAC,CAAC,CAClCM,KAAK,CAAEC,GAAG,IAAKd,OAAO,CAACH,KAAK,CAAC,+BAA+B,EAAEiB,GAAG,CAAC,CAAC;EACxE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACArL,SAAS,CAAC,MAAM;IACdwI,QAAQ,CAACjI,IAAI,CAAC+K,UAAU,CAAC,CAAC,CAACL,IAAI,CAAC,CAAC;MAAEH,IAAI,EAAE;QAAES;MAAQ;IAAE,CAAC,KAAK;MACzD3E,OAAO,CAAC,CAAA2E,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE5E,IAAI,KAAI,IAAI,CAAC;IAChC,CAAC,CAAC;IACF,MAAM;MAAEmE,IAAI,EAAEU;IAAS,CAAC,GAAGhD,QAAQ,CAACjI,IAAI,CAACkL,iBAAiB,CAAC,CAACC,MAAM,EAAEH,OAAO,KAAK;MAC9E3E,OAAO,CAAC,CAAA2E,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE5E,IAAI,KAAI,IAAI,CAAC;IAChC,CAAC,CAAC;IACF,OAAO,MAAM;MACX6E,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEG,YAAY,CAACtB,WAAW,CAAC,CAAC;IACtC,CAAC;EACH,CAAC,EAAE,CAAC7B,QAAQ,CAACjI,IAAI,CAAC,CAAC;;EAEnB;EACA,MAAMqL,kBAAkB,GAAG,MAAOC,CAAC,IAAK;IACtC,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACF,IAAI,IAAI,CAACnF,IAAI,EAAE;IACpBK,sBAAsB,CAAC,IAAI,CAAC;IAC5B,MAAMiF,QAAQ,GAAG,GAAGtF,IAAI,CAACuF,EAAE,IAAIJ,IAAI,CAACK,IAAI,EAAE;IAC1C,MAAM;MAAE/B;IAAM,CAAC,GAAG,MAAM5B,QAAQ,CAAC4D,OAAO,CAACC,IAAI,CAAC,SAAS,CAAC,CAACC,MAAM,CAACL,QAAQ,EAAEH,IAAI,EAAE;MAAES,MAAM,EAAE;IAAK,CAAC,CAAC;IACjG,IAAI,CAACnC,KAAK,EAAE;MACV,MAAM;QAAEU,IAAI,EAAE0B;MAAQ,CAAC,GAAGhE,QAAQ,CAAC4D,OAAO,CAACC,IAAI,CAAC,SAAS,CAAC,CAACI,YAAY,CAACR,QAAQ,CAAC;MACjF/E,YAAY,CAACsF,OAAO,CAACE,SAAS,CAAC;MAC/BvD,gBAAgB,CAAC,+BAA+B,EAAE,SAAS,CAAC;MAC5DG,WAAW,CAAC,mBAAmB,CAAC;IAClC,CAAC,MAAM;MACLH,gBAAgB,CAAC,uBAAuB,EAAE,OAAO,CAAC;IACpD;IACAnC,sBAAsB,CAAC,KAAK,CAAC;EAC/B,CAAC;;EAED;EACA,MAAM2F,oBAAoB,GAAG,MAAOd,CAAC,IAAK;IACxC,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACF,IAAI,IAAI,CAACnF,IAAI,EAAE;IACpBS,wBAAwB,CAAC,IAAI,CAAC;IAC9B,MAAM6E,QAAQ,GAAG,GAAGtF,IAAI,CAACuF,EAAE,IAAIJ,IAAI,CAACK,IAAI,EAAE;IAC1C,MAAM;MAAE/B;IAAM,CAAC,GAAG,MAAM5B,QAAQ,CAAC4D,OAAO,CAACC,IAAI,CAAC,WAAW,CAAC,CAACC,MAAM,CAACL,QAAQ,EAAEH,IAAI,EAAE;MAAES,MAAM,EAAE;IAAK,CAAC,CAAC;IACnG,IAAI,CAACnC,KAAK,EAAE;MACVjB,gBAAgB,CAAC,oBAAoB,EAAE,SAAS,CAAC;MACjDG,WAAW,CAAC,sBAAsBwC,IAAI,CAACK,IAAI,EAAE,CAAC;IAChD,CAAC,MAAM;MACLhD,gBAAgB,CAAC,yBAAyB,EAAE,OAAO,CAAC;IACtD;IACA/B,wBAAwB,CAAC,KAAK,CAAC;EACjC,CAAC;;EAED;EACA,MAAMwF,kBAAkB,GAAG1M,WAAW,CAAE2M,OAAO,IAAK;IAClD;IACAxE,kBAAkB,CAACmB,IAAI,IAAI;MACzB,MAAMsD,QAAQ,GAAGtD,IAAI,CAACuD,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKH,OAAO,CAAC;MAChD,OAAO,CAACA,OAAO,EAAE,GAAGC,QAAQ,CAAC,CAACjD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC;IAEFP,WAAW,CAAC,UAAUuD,OAAO,gBAAgB,CAAC;IAE9C,IAAIA,OAAO,CAAChI,WAAW,CAAC,CAAC,KAAK,WAAW,EAAE;MACzCoI,MAAM,CAACC,IAAI,CAAC,uCAAuC,EAAE,QAAQ,CAAC;MAC9D;IACF;IACA,MAAMC,gBAAgB,GAAGN,OAAO,CAACO,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;IACpDH,MAAM,CAACC,IAAI,CAAC,gBAAgBC,gBAAgB,OAAO,EAAE,QAAQ,CAAC;EAChE,CAAC,EAAE,CAAC7D,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAM+D,cAAc,GAAGnN,WAAW,CAAC,CAAC2M,OAAO,EAAEhB,CAAC,KAAK;IACjDA,CAAC,CAACyB,eAAe,CAAC,CAAC,CAAC,CAAC;IACrBnF,oBAAoB,CAACqB,IAAI,IAAI;MAC3B,IAAIA,IAAI,CAAC+D,QAAQ,CAACV,OAAO,CAAC,EAAE;QAC1B,OAAOrD,IAAI,CAACuD,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKH,OAAO,CAAC;MACxC,CAAC,MAAM;QACL,OAAO,CAAC,GAAGrD,IAAI,EAAEqD,OAAO,CAAC;MAC3B;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMW,mBAAmB,GAAGA,CAAA,KAAM;IAChCjF,mBAAmB,CAAC,KAAK,CAAC;IAC1BY,gBAAgB,CAAC,8CAA8C,EAAE,SAAS,CAAC;;IAE3E;IACA,MAAMsE,cAAc,GAAGC,QAAQ,CAACC,aAAa,CAAC,qBAAqB,CAAC;IACpE,MAAMC,eAAe,GAAGF,QAAQ,CAACC,aAAa,CAAC,wBAAwB,CAAC;IAExE,IAAIF,cAAc,EAAE;MAClBA,cAAc,CAACI,KAAK,CAACC,KAAK,GAAG,MAAM;IACrC;IACA,IAAIF,eAAe,EAAE;MACnBA,eAAe,CAACC,KAAK,CAACC,KAAK,GAAG,MAAM;IACtC;EACF,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAG5N,OAAO,CAAC,MAAM;IACtC,IAAI2M,QAAQ,GAAG/D,SAAS;;IAExB;IACA,IAAIjB,gBAAgB,KAAK,KAAK,EAAE;MAC9B,MAAMkG,iBAAiB,GAAGlF,iBAAiB,CAAChB,gBAAgB,CAAC,IAAI,EAAE;MACnEgF,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACF,OAAO,IAChCmB,iBAAiB,CAACC,IAAI,CAACC,UAAU,IAC/BrB,OAAO,CAAChI,WAAW,CAAC,CAAC,CAAC0I,QAAQ,CAACW,UAAU,CAACrJ,WAAW,CAAC,CAAC,CAAC,IACxDqJ,UAAU,CAACrJ,WAAW,CAAC,CAAC,CAAC0I,QAAQ,CAACV,OAAO,CAAChI,WAAW,CAAC,CAAC,CACzD,CACF,CAAC;IACH;;IAEA;IACAiI,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACF,OAAO,IAChCA,OAAO,CAAChI,WAAW,CAAC,CAAC,CAAC0I,QAAQ,CAAChH,UAAU,CAAC1B,WAAW,CAAC,CAAC,CACzD,CAAC;;IAED;IACA,IAAImD,MAAM,KAAK,MAAM,EAAE;MACrB8E,QAAQ,CAACqB,IAAI,CAAC,CAAC;IACjB,CAAC,MAAM,IAAInG,MAAM,KAAK,WAAW,EAAE;MACjC8E,QAAQ,CAACqB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QACtB,MAAMC,IAAI,GAAGpG,iBAAiB,CAACqF,QAAQ,CAACa,CAAC,CAAC;QAC1C,MAAMG,IAAI,GAAGrG,iBAAiB,CAACqF,QAAQ,CAACc,CAAC,CAAC;QAC1C,IAAIC,IAAI,IAAI,CAACC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC5B,IAAI,CAACD,IAAI,IAAIC,IAAI,EAAE,OAAO,CAAC;QAC3B,OAAOH,CAAC,CAACI,aAAa,CAACH,CAAC,CAAC;MAC3B,CAAC,CAAC;IACJ;IAEA,OAAOvB,QAAQ;EACjB,CAAC,EAAE,CAAC/D,SAAS,EAAEjB,gBAAgB,EAAEvB,UAAU,EAAEyB,MAAM,EAAEE,iBAAiB,EAAEY,iBAAiB,CAAC,CAAC;;EAE3F;EACA,MAAM2F,YAAY,GAAGvO,WAAW,CAAEwO,GAAG,IAAK;IACxCzB,MAAM,CAACC,IAAI,CAACwB,GAAG,EAAE,QAAQ,CAAC;IAC1BpF,WAAW,CAAC,gBAAgBoF,GAAG,EAAE,CAAC;EACpC,CAAC,EAAE,CAACpF,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMqF,WAAW,GAAGzO,WAAW,CAAC,YAAY;IAC1C,IAAI,CAACyF,KAAK,CAACiJ,IAAI,CAAC,CAAC,EAAE;IAEnB,MAAMC,WAAW,GAAG;MAAEC,IAAI,EAAE,MAAM;MAAEC,OAAO,EAAEpJ;IAAM,CAAC;IACpDG,WAAW,CAAE0D,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEqF,WAAW,CAAC,CAAC;IAC7C,MAAMG,YAAY,GAAGrJ,KAAK;IAC1BC,QAAQ,CAAC,EAAE,CAAC;IACZM,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MAAA,IAAA+I,oBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACF,MAAMC,MAAM,GAAG,oOAAoOnJ,SAAS,iBAAiB6I,YAAY,EAAE;MAE3R,MAAM9D,GAAG,GAAG,MAAMzK,KAAK,CAAC8O,IAAI,CAC1B,gGAAgG5G,OAAO,EAAE,EACzG;QACE6G,QAAQ,EAAE,CACR;UACEC,KAAK,EAAE,CAAC;YAAEtE,IAAI,EAAEmE;UAAO,CAAC;QAC1B,CAAC;MAEL,CAAC,EACD;QACEI,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,OAAO,EAAE,KAAK,CAAC;MACjB,CACF,CAAC;MAED,MAAMC,QAAQ,GACZ,EAAAX,oBAAA,GAAA/D,GAAG,CAACJ,IAAI,CAAC+E,UAAU,cAAAZ,oBAAA,wBAAAC,qBAAA,GAAnBD,oBAAA,CAAsB,CAAC,CAAC,cAAAC,qBAAA,wBAAAC,sBAAA,GAAxBD,qBAAA,CAA0BH,OAAO,cAAAI,sBAAA,wBAAAC,sBAAA,GAAjCD,sBAAA,CAAmCM,KAAK,cAAAL,sBAAA,wBAAAC,sBAAA,GAAxCD,sBAAA,CAA2C,CAAC,CAAC,cAAAC,sBAAA,uBAA7CA,sBAAA,CAA+ClE,IAAI,KACnD,yBAAyB;MAC3B,MAAM2E,UAAU,GAAG;QAAEhB,IAAI,EAAE,KAAK;QAAEC,OAAO,EAAEa;MAAS,CAAC;MACrD9J,WAAW,CAAE0D,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEsG,UAAU,CAAC,CAAC;MAC5CxG,WAAW,CAAC,sBAAsB0F,YAAY,CAACe,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC;IACvE,CAAC,CAAC,OAAO3F,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzCtE,WAAW,CAAE0D,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QAAEsF,IAAI,EAAE,KAAK;QAAEC,OAAO,EAAE,WAAW,GAAG3E,KAAK,CAAC4F;MAAQ,CAAC,CACtD,CAAC;MACF7G,gBAAgB,CAAC,wBAAwB,EAAE,OAAO,CAAC;IACrD,CAAC,SAAS;MACRjD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACP,KAAK,EAAEQ,SAAS,EAAEwC,OAAO,EAAEW,WAAW,EAAEH,gBAAgB,CAAC,CAAC;;EAE9D;;EAEA;EACAnJ,SAAS,CAAC,MAAM;IACd,IAAI6H,UAAU,CAACoI,OAAO,EAAEpI,UAAU,CAACoI,OAAO,CAACC,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACnF,CAAC,EAAE,CAACtK,QAAQ,EAAEI,OAAO,CAAC,CAAC;;EAEvB;EACA,MAAMmK,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,IAAI,GAAG,EAAE;IACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC3B,MAAMC,CAAC,GAAG,IAAI7G,IAAI,CAAC,CAAC;MACpB6G,CAAC,CAACC,OAAO,CAACD,CAAC,CAACE,OAAO,CAAC,CAAC,GAAGH,CAAC,CAAC;MAC1BD,IAAI,CAACK,IAAI,CAACH,CAAC,CAACI,kBAAkB,CAAC,CAAC,CAAC;IACnC;IACA,OAAON,IAAI;EACb,CAAC;EAED,MAAMO,WAAW,GAAGR,YAAY,CAAC,CAAC;EAClC,MAAMS,SAAS,GAAG;IAChBC,MAAM,EAAEF,WAAW;IACnBG,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,kBAAkB;MACzBlG,IAAI,EAAE8F,WAAW,CAACzM,GAAG,CAAC8M,GAAG,IAAItJ,WAAW,CAACoF,MAAM,CAACqB,CAAC,IAAIA,CAAC,CAAC/E,IAAI,KAAK,UAAU,IAAI+E,CAAC,CAAChF,GAAG,CAAC8H,UAAU,CAAC,mBAAmB,CAAC,IAAI,IAAIxH,IAAI,CAAC0E,CAAC,CAAC+C,IAAI,CAAC,CAACR,kBAAkB,CAAC,CAAC,KAAKM,GAAG,CAAC,CAACG,MAAM,CAAC;MAC7KC,eAAe,EAAE;IACnB,CAAC,EACD;MACEL,KAAK,EAAE,iBAAiB;MACxBlG,IAAI,EAAE8F,WAAW,CAACzM,GAAG,CAAC8M,GAAG,IAAItJ,WAAW,CAACoF,MAAM,CAACqB,CAAC,IAAIA,CAAC,CAAC/E,IAAI,KAAK,UAAU,IAAI+E,CAAC,CAAChF,GAAG,KAAK,8BAA8B,IAAI,IAAIM,IAAI,CAAC0E,CAAC,CAAC+C,IAAI,CAAC,CAACR,kBAAkB,CAAC,CAAC,KAAKM,GAAG,CAAC,CAACG,MAAM,CAAC;MAChLC,eAAe,EAAE;IACnB,CAAC;EAEL,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE;MACPC,MAAM,EAAE;QAAEC,QAAQ,EAAE;MAAM,CAAC;MAC3BC,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAK;IAC3B,CAAC;IACDC,MAAM,EAAE;MACNC,CAAC,EAAE;QAAEC,WAAW,EAAE,IAAI;QAAEC,KAAK,EAAE;UAAEC,QAAQ,EAAE;QAAE;MAAE;IACjD;EACF,CAAC;;EAED;EACA,oBACEzP,OAAA;IAAK0P,SAAS,EAAE,iBAAiBxM,aAAa,GAAG,mBAAmB,GAAG,EAAE,EAAG;IAACmI,KAAK,EAAE;MAAEwD,eAAe,EAAExI,KAAK,CAACsJ,UAAU;MAAErE,KAAK,EAAEjF,KAAK,CAACsC;IAAK,CAAE;IAAAiH,QAAA,gBAE3I5P,OAAA,CAAC5B,MAAM;MACLyE,WAAW,EAAEA,WAAY;MACzBgN,eAAe,EAAE9M,aAAc;MAC/BoB,IAAI,EAAEA,IAAK;MACX2L,QAAQ,EAAEpI,YAAa;MACvBqI,UAAU,EAAE3J;IAAY;MAAAtE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAGFjC,OAAA,CAAC3B,OAAO;MACNuE,MAAM,EAAEC,WAAY;MACpBmN,OAAO,EAAE/M,YAAa;MACtBY,SAAS,EAAEA,SAAU;MACrBoM,WAAW,EAAE3I,eAAgB;MAC7BpJ,YAAY,EAAEwD,mBAAoB;MAClCuC,aAAa,EAAEA,aAAc;MAC7BiM,YAAY,EAAE1I,gBAAiB;MAC/BnB,KAAK,EAAEA;IAAM;MAAAvE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC,eAGFjC,OAAA;MAAM0P,SAAS,EAAE,gBAAgB7M,WAAW,IAAI,CAACF,QAAQ,GAAG,2BAA2B,GAAG,EAAE,EAAG;MAAAiN,QAAA,GAE5F/L,SAAS,KAAK,WAAW,iBACxB7D,OAAA,CAACpC,QAAQ;QAACuS,QAAQ,eAAEnQ,OAAA;UAAK0P,SAAS,EAAC,iBAAiB;UAACrE,KAAK,EAAE;YAAE+E,MAAM,EAAE;UAAY;QAAE;UAAAtO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAA2N,QAAA,eACtF5P,OAAA,CAAC1B,SAAS;UAAC6F,IAAI,EAAEA,IAAK;UAAC8L,WAAW,EAAE3I;QAAgB;UAAAxF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CACX,EAGA4B,SAAS,KAAK,SAAS,iBACtB7D,OAAA;QAAKqL,KAAK,EAAE;UAAEgF,OAAO,EAAE,MAAM;UAAExB,eAAe,EAAExI,KAAK,CAACsJ;QAAW,CAAE;QAAAC,QAAA,eACjE5P,OAAA;UAAK0P,SAAS,EAAC,MAAM;UAACrE,KAAK,EAAE;YAAEwD,eAAe,EAAExI,KAAK,CAACiK,OAAO;YAAEC,MAAM,EAAE,aAAalK,KAAK,CAACkK,MAAM;UAAG,CAAE;UAAAX,QAAA,gBACnG5P,OAAA;YAAIqL,KAAK,EAAE;cAAEmF,SAAS,EAAE,CAAC;cAAElF,KAAK,EAAEjF,KAAK,CAACsC;YAAK,CAAE;YAAAiH,QAAA,EAAC;UAAc;YAAA9N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnEjC,OAAA;YAAGqL,KAAK,EAAE;cAAEoF,OAAO,EAAE,GAAG;cAAEC,YAAY,EAAE,MAAM;cAAEpF,KAAK,EAAEjF,KAAK,CAACsK;YAAU,CAAE;YAAAf,QAAA,EAAC;UAE1E;YAAA9N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJjC,OAAA;YAAKqL,KAAK,EAAE;cACVuF,OAAO,EAAE,MAAM;cACfC,mBAAmB,EAAE,uCAAuC;cAC5DC,GAAG,EAAE;YACP,CAAE;YAAAlB,QAAA,EACCpJ,WAAW,CAAC7E,GAAG,CAAC,CAACoP,IAAI,EAAEC,KAAK,kBAC3BhR,OAAA;cAEE0P,SAAS,EAAC,sBAAsB;cAChCrE,KAAK,EAAE;gBACLwD,eAAe,EAAExI,KAAK,CAACiK,OAAO;gBAC9BC,MAAM,EAAE,aAAalK,KAAK,CAACkK,MAAM,EAAE;gBACnCU,YAAY,EAAE,MAAM;gBACpBZ,OAAO,EAAE,MAAM;gBACfO,OAAO,EAAE,MAAM;gBACfM,cAAc,EAAE,eAAe;gBAC/BC,UAAU,EAAE,QAAQ;gBACpBC,MAAM,EAAE;cACV,CAAE;cACFC,OAAO,EAAEA,CAAA,KAAMpF,YAAY,CAAC8E,IAAI,CAACrK,IAAI,CAAE;cAAAkJ,QAAA,gBAEvC5P,OAAA;gBAAA4P,QAAA,gBACE5P,OAAA;kBAAIqL,KAAK,EAAE;oBAAE+E,MAAM,EAAE,WAAW;oBAAE9E,KAAK,EAAEjF,KAAK,CAACsC;kBAAK,CAAE;kBAAAiH,QAAA,EAAEmB,IAAI,CAAC3O;gBAAK;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACxEjC,OAAA;kBAAGqL,KAAK,EAAE;oBACR+E,MAAM,EAAE,CAAC;oBACTkB,QAAQ,EAAE,MAAM;oBAChBb,OAAO,EAAE,GAAG;oBACZnF,KAAK,EAAEjF,KAAK,CAACsK;kBACf,CAAE;kBAAAf,QAAA,EACCmB,IAAI,CAACtK;gBAAW;kBAAA3E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNjC,OAAA;gBAAKqL,KAAK,EAAE;kBAAEC,KAAK,EAAEjF,KAAK,CAACkL;gBAAQ,CAAE;gBAAA3B,QAAA,eACnC5P,OAAA,CAACN,cAAc;kBAAC8R,IAAI,EAAE;gBAAG;kBAAA1P,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA,GA3BD+O,KAAK;cAAAlP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA4BP,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA4B,SAAS,KAAK,KAAK,iBAClB7D,OAAA;QAAKqL,KAAK,EAAE;UACVgF,OAAO,EAAE,MAAM;UACfxB,eAAe,EAAExI,KAAK,CAACsJ,UAAU;UACjC8B,SAAS,EAAE,OAAO;UAClBvC,QAAQ,EAAE,UAAU;UACpBwC,QAAQ,EAAE;QACZ,CAAE;QAAA9B,QAAA,gBAEA5P,OAAA;UAAKqL,KAAK,EAAE;YACV6D,QAAQ,EAAE,UAAU;YACpByC,GAAG,EAAE,KAAK;YACVC,IAAI,EAAE,IAAI;YACVC,KAAK,EAAE,OAAO;YACdC,MAAM,EAAE,OAAO;YACfnC,UAAU,EAAE,uEAAuE;YACnFsB,YAAY,EAAE,KAAK;YACnBc,SAAS,EAAE,+BAA+B;YAC1CC,MAAM,EAAE;UACV;QAAE;UAAAlQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACLjC,OAAA;UAAKqL,KAAK,EAAE;YACV6D,QAAQ,EAAE,UAAU;YACpByC,GAAG,EAAE,KAAK;YACVM,KAAK,EAAE,KAAK;YACZJ,KAAK,EAAE,OAAO;YACdC,MAAM,EAAE,OAAO;YACfnC,UAAU,EAAE,wEAAwE;YACpFsB,YAAY,EAAE,KAAK;YACnBc,SAAS,EAAE,uCAAuC;YAClDC,MAAM,EAAE;UACV;QAAE;UAAAlQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACLjC,OAAA;UAAKqL,KAAK,EAAE;YACV6D,QAAQ,EAAE,UAAU;YACpBgD,MAAM,EAAE,KAAK;YACbN,IAAI,EAAE,KAAK;YACXC,KAAK,EAAE,OAAO;YACdC,MAAM,EAAE,OAAO;YACfnC,UAAU,EAAE,wEAAwE;YACpFsB,YAAY,EAAE,KAAK;YACnBc,SAAS,EAAE,gCAAgC;YAC3CC,MAAM,EAAE;UACV;QAAE;UAAAlQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAELjC,OAAA;UAAKqL,KAAK,EAAE;YAAE8G,QAAQ,EAAE,QAAQ;YAAE/B,MAAM,EAAE,QAAQ;YAAElB,QAAQ,EAAE,UAAU;YAAE8C,MAAM,EAAE;UAAE,CAAE;UAAApC,QAAA,gBAGpF5P,OAAA;YAAKqL,KAAK,EAAE;cACVsE,UAAU,EAAE,wEAAwE;cACpFyC,cAAc,EAAE,YAAY;cAC5BnB,YAAY,EAAE,MAAM;cACpBZ,OAAO,EAAE,MAAM;cACfK,YAAY,EAAE,MAAM;cACpBpF,KAAK,EAAE,OAAO;cACd4D,QAAQ,EAAE,UAAU;cACpBwC,QAAQ,EAAE,QAAQ;cAClBnB,MAAM,EAAE,iCAAiC;cACzC8B,SAAS,EAAE;YACb,CAAE;YAAAzC,QAAA,gBAEA5P,OAAA;cAAKqL,KAAK,EAAE;gBACV6D,QAAQ,EAAE,UAAU;gBACpByC,GAAG,EAAE,MAAM;gBACXM,KAAK,EAAE,MAAM;gBACbX,QAAQ,EAAE,MAAM;gBAChBS,SAAS,EAAE;cACb,CAAE;cAAAnC,QAAA,EAAC;YAAE;cAAA9N,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACXjC,OAAA;cAAKqL,KAAK,EAAE;gBACV6D,QAAQ,EAAE,UAAU;gBACpBgD,MAAM,EAAE,MAAM;gBACdN,IAAI,EAAE,MAAM;gBACZN,QAAQ,EAAE,QAAQ;gBAClBS,SAAS,EAAE;cACb,CAAE;cAAAnC,QAAA,EAAC;YAAC;cAAA9N,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACVjC,OAAA;cAAKqL,KAAK,EAAE;gBACV6D,QAAQ,EAAE,UAAU;gBACpByC,GAAG,EAAE,KAAK;gBACVM,KAAK,EAAE,KAAK;gBACZX,QAAQ,EAAE,QAAQ;gBAClBS,SAAS,EAAE;cACb,CAAE;cAAAnC,QAAA,EAAC;YAAE;cAAA9N,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAEXjC,OAAA;cAAKqL,KAAK,EAAE;gBAAEuF,OAAO,EAAE,MAAM;gBAAEO,UAAU,EAAE,QAAQ;gBAAEL,GAAG,EAAE,MAAM;gBAAE5B,QAAQ,EAAE,UAAU;gBAAE8C,MAAM,EAAE;cAAE,CAAE;cAAApC,QAAA,gBAClG5P,OAAA;gBAAKqL,KAAK,EAAE;kBACVwG,KAAK,EAAE,OAAO;kBACdC,MAAM,EAAE,OAAO;kBACfb,YAAY,EAAE,KAAK;kBACnBtB,UAAU,EAAE,2CAA2C;kBACvDiB,OAAO,EAAE,MAAM;kBACfO,UAAU,EAAE,QAAQ;kBACpBD,cAAc,EAAE,QAAQ;kBACxBI,QAAQ,EAAE,MAAM;kBAChBgB,UAAU,EAAE,MAAM;kBAClB/B,MAAM,EAAE,oCAAoC;kBAC5C8B,SAAS,EAAE,6BAA6B;kBACxCN,SAAS,EAAE;gBACb,CAAE;gBAAAnC,QAAA,EACCzL,IAAI,GAAGA,IAAI,CAACiD,KAAK,CAAC,CAAC,CAAC,CAACmL,WAAW,CAAC,CAAC,GAAG;cAAI;gBAAAzQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACNjC,OAAA;gBAAA4P,QAAA,gBACE5P,OAAA;kBAAIqL,KAAK,EAAE;oBACT+E,MAAM,EAAE,CAAC;oBACTkB,QAAQ,EAAE,QAAQ;oBAClBgB,UAAU,EAAE,GAAG;oBACf5B,YAAY,EAAE,MAAM;oBACpBf,UAAU,EAAE,uCAAuC;oBACnD6C,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE,aAAa;oBAClCC,UAAU,EAAE;kBACd,CAAE;kBAAA9C,QAAA,GAAC,MACG,EAACzL,IAAI,GAAGA,IAAI,CAACiD,KAAK,CAACuL,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,EAAC,gBACpD;gBAAA;kBAAA7Q,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLjC,OAAA;kBAAGqL,KAAK,EAAE;oBACR+E,MAAM,EAAE,CAAC;oBACTkB,QAAQ,EAAE,QAAQ;oBAClBb,OAAO,EAAE,IAAI;oBACb6B,UAAU,EAAE;kBACd,CAAE;kBAAA1C,QAAA,EAAC;gBAEH;kBAAA9N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJjC,OAAA;kBAAKqL,KAAK,EAAE;oBACVmF,SAAS,EAAE,MAAM;oBACjBI,OAAO,EAAE,MAAM;oBACfE,GAAG,EAAE;kBACP,CAAE;kBAAAlB,QAAA,gBACA5P,OAAA;oBAAKqL,KAAK,EAAE;sBACVsE,UAAU,EAAE,uBAAuB;sBACnCU,OAAO,EAAE,aAAa;sBACtBY,YAAY,EAAE,MAAM;sBACpBK,QAAQ,EAAE,QAAQ;sBAClBgB,UAAU,EAAE;oBACd,CAAE;oBAAA1C,QAAA,EAAC;kBAEH;oBAAA9N,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNjC,OAAA;oBAAKqL,KAAK,EAAE;sBACVsE,UAAU,EAAE,uBAAuB;sBACnCU,OAAO,EAAE,aAAa;sBACtBY,YAAY,EAAE,MAAM;sBACpBK,QAAQ,EAAE,QAAQ;sBAClBgB,UAAU,EAAE;oBACd,CAAE;oBAAA1C,QAAA,EAAC;kBAEH;oBAAA9N,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNjC,OAAA;YAAKqL,KAAK,EAAE;cACVuF,OAAO,EAAE,MAAM;cACfC,mBAAmB,EAAE,sCAAsC;cAC3DC,GAAG,EAAE,QAAQ;cACbJ,YAAY,EAAE;YAChB,CAAE;YAAAd,QAAA,EACC,CACC;cACExN,KAAK,EAAE,aAAa;cACpBwQ,KAAK,EAAE,IAAI;cACXC,IAAI,EAAE,MAAM;cACZ3Q,IAAI,EAAE,IAAI;cACVoJ,KAAK,EAAE,SAAS;cAChBwH,UAAU,EAAE,2CAA2C;cACvDC,SAAS,EAAE,SAAS;cACpBtM,WAAW,EAAE;YACf,CAAC,EACD;cACErE,KAAK,EAAE,cAAc;cACrBwQ,KAAK,EAAE,OAAO;cACdC,IAAI,EAAE,IAAI;cACV3Q,IAAI,EAAE,GAAG;cACToJ,KAAK,EAAE,SAAS;cAChBwH,UAAU,EAAE,2CAA2C;cACvDC,SAAS,EAAE,SAAS;cACpBtM,WAAW,EAAE;YACf,CAAC,EACD;cACErE,KAAK,EAAE,aAAa;cACpBwQ,KAAK,EAAE,IAAI;cACXC,IAAI,EAAE,OAAO;cACb3Q,IAAI,EAAE,IAAI;cACVoJ,KAAK,EAAE,SAAS;cAChBwH,UAAU,EAAE,2CAA2C;cACvDC,SAAS,EAAE,SAAS;cACpBtM,WAAW,EAAE;YACf,CAAC,EACD;cACErE,KAAK,EAAE,cAAc;cACrBwQ,KAAK,EAAE,IAAI;cACXC,IAAI,EAAE,UAAU;cAChB3Q,IAAI,EAAE,IAAI;cACVoJ,KAAK,EAAE,SAAS;cAChBwH,UAAU,EAAE,2CAA2C;cACvDC,SAAS,EAAE,SAAS;cACpBtM,WAAW,EAAE;YACf,CAAC,CACF,CAAC9E,GAAG,CAAC,CAACqR,IAAI,EAAEhC,KAAK,kBAChBhR,OAAA;cAAiBqL,KAAK,EAAE;gBACtBsE,UAAU,EAAE,wEAAwE;gBACpFyC,cAAc,EAAE,YAAY;gBAC5BnB,YAAY,EAAE,MAAM;gBACpBZ,OAAO,EAAE,MAAM;gBACfnB,QAAQ,EAAE,UAAU;gBACpBwC,QAAQ,EAAE,QAAQ;gBAClBnB,MAAM,EAAE,iCAAiC;gBACzCa,MAAM,EAAE,SAAS;gBACjB6B,UAAU,EAAE,kDAAkD;gBAC9DZ,SAAS,EAAE,0CAA0CW,IAAI,CAACD,SAAS;cACrE,CAAE;cACFG,YAAY,EAAG7J,CAAC,IAAK;gBACnBA,CAAC,CAAC8J,aAAa,CAAC9H,KAAK,CAAC+H,SAAS,GAAG,+BAA+B;gBACjE/J,CAAC,CAAC8J,aAAa,CAAC9H,KAAK,CAACgH,SAAS,GAAG,yCAAyCW,IAAI,CAACD,SAAS,IAAI;cAC/F,CAAE;cACFM,YAAY,EAAGhK,CAAC,IAAK;gBACnBA,CAAC,CAAC8J,aAAa,CAAC9H,KAAK,CAAC+H,SAAS,GAAG,wBAAwB;gBAC1D/J,CAAC,CAAC8J,aAAa,CAAC9H,KAAK,CAACgH,SAAS,GAAG,0CAA0CW,IAAI,CAACD,SAAS,IAAI;cAChG,CAAE;cAAAnD,QAAA,gBAGA5P,OAAA;gBAAKqL,KAAK,EAAE;kBACV6D,QAAQ,EAAE,UAAU;kBACpByC,GAAG,EAAE,MAAM;kBACXC,IAAI,EAAE,MAAM;kBACZC,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdnC,UAAU,EAAE,0CAA0CqD,IAAI,CAAC1H,KAAK,kBAAkB;kBAClFyG,SAAS,EAAE,4BAA4B;kBACvCtB,OAAO,EAAE;gBACX;cAAE;gBAAA3O,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAGLjC,OAAA;gBAAKqL,KAAK,EAAE;kBACV6D,QAAQ,EAAE,UAAU;kBACpByC,GAAG,EAAE,MAAM;kBACXM,KAAK,EAAE,MAAM;kBACbJ,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdnC,UAAU,EAAEqD,IAAI,CAACF,UAAU;kBAC3B7B,YAAY,EAAE,KAAK;kBACnBR,OAAO,EAAE,GAAG;kBACZlG,MAAM,EAAE;gBACV;cAAE;gBAAAzI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAELjC,OAAA;gBAAKqL,KAAK,EAAE;kBAAE6D,QAAQ,EAAE,UAAU;kBAAE8C,MAAM,EAAE;gBAAE,CAAE;gBAAApC,QAAA,gBAE9C5P,OAAA;kBAAKqL,KAAK,EAAE;oBACVuF,OAAO,EAAE,MAAM;oBACfO,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE,eAAe;oBAC/BR,YAAY,EAAE;kBAChB,CAAE;kBAAAd,QAAA,gBACA5P,OAAA;oBAAKqL,KAAK,EAAE;sBACViG,QAAQ,EAAE,QAAQ;sBAClBgB,UAAU,EAAE,GAAG;sBACfhH,KAAK,EAAE,uBAAuB;sBAC9BgI,aAAa,EAAE;oBACjB,CAAE;oBAAA1D,QAAA,EACCoD,IAAI,CAAC5Q;kBAAK;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC,eACNjC,OAAA;oBAAKqL,KAAK,EAAE;sBACViG,QAAQ,EAAE,MAAM;sBAChB/G,MAAM,EAAE;oBACV,CAAE;oBAAAqF,QAAA,EACCoD,IAAI,CAAC9Q;kBAAI;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNjC,OAAA;kBAAKqL,KAAK,EAAE;oBACVuF,OAAO,EAAE,MAAM;oBACfO,UAAU,EAAE,UAAU;oBACtBL,GAAG,EAAE,QAAQ;oBACbJ,YAAY,EAAE;kBAChB,CAAE;kBAAAd,QAAA,gBACA5P,OAAA;oBAAKqL,KAAK,EAAE;sBACViG,QAAQ,EAAE,MAAM;sBAChBgB,UAAU,EAAE,GAAG;sBACfhH,KAAK,EAAE,OAAO;sBACdoH,UAAU,EAAE,YAAYM,IAAI,CAAC1H,KAAK,EAAE;sBACpCiI,UAAU,EAAE;oBACd,CAAE;oBAAA3D,QAAA,EACCoD,IAAI,CAACJ;kBAAK;oBAAA9Q,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC,eACNjC,OAAA;oBAAKqL,KAAK,EAAE;sBACViG,QAAQ,EAAE,QAAQ;sBAClBgB,UAAU,EAAE,GAAG;sBACfhH,KAAK,EAAE0H,IAAI,CAAC1H,KAAK;sBACjBkI,aAAa,EAAE,WAAW;sBAC1BF,aAAa,EAAE;oBACjB,CAAE;oBAAA1D,QAAA,EACCoD,IAAI,CAACH;kBAAI;oBAAA/Q,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNjC,OAAA;kBAAKqL,KAAK,EAAE;oBACViG,QAAQ,EAAE,QAAQ;oBAClBhG,KAAK,EAAE,uBAAuB;oBAC9BgH,UAAU,EAAE,GAAG;oBACfmB,SAAS,EAAE;kBACb,CAAE;kBAAA7D,QAAA,EACCoD,IAAI,CAACvM;gBAAW;kBAAA3E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,eAGNjC,OAAA;kBAAKqL,KAAK,EAAE;oBACVmF,SAAS,EAAE,MAAM;oBACjBsB,MAAM,EAAE,KAAK;oBACbnC,UAAU,EAAE,uBAAuB;oBACnCsB,YAAY,EAAE,KAAK;oBACnBS,QAAQ,EAAE;kBACZ,CAAE;kBAAA9B,QAAA,eACA5P,OAAA;oBAAKqL,KAAK,EAAE;sBACVyG,MAAM,EAAE,MAAM;sBACdD,KAAK,EAAE,GAAG6B,IAAI,CAACC,GAAG,CAAC,GAAG,EAAE,CAAC3C,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG;sBAC5CrB,UAAU,EAAEqD,IAAI,CAACF,UAAU;sBAC3B7B,YAAY,EAAE,KAAK;sBACnBc,SAAS,EAAE;oBACb;kBAAE;oBAAAjQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GA3HE+O,KAAK;cAAAlP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA4HV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNjC,OAAA;YAAKqL,KAAK,EAAE;cACVsE,UAAU,EAAE,wEAAwE;cACpFyC,cAAc,EAAE,YAAY;cAC5BnB,YAAY,EAAE,MAAM;cACpBZ,OAAO,EAAE,MAAM;cACfK,YAAY,EAAE,MAAM;cACpBH,MAAM,EAAE,iCAAiC;cACzC8B,SAAS,EAAE,6BAA6B;cACxCnD,QAAQ,EAAE,UAAU;cACpBwC,QAAQ,EAAE;YACZ,CAAE;YAAA9B,QAAA,gBACA5P,OAAA;cAAKqL,KAAK,EAAE;gBACV6D,QAAQ,EAAE,UAAU;gBACpByC,GAAG,EAAE,QAAQ;gBACbM,KAAK,EAAE,QAAQ;gBACfJ,KAAK,EAAE,OAAO;gBACdC,MAAM,EAAE,OAAO;gBACfnC,UAAU,EAAE,wEAAwE;gBACpFsB,YAAY,EAAE,KAAK;gBACnBR,OAAO,EAAE,GAAG;gBACZsB,SAAS,EAAE;cACb;YAAE;cAAAjQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAELjC,OAAA;cAAIqL,KAAK,EAAE;gBACT+E,MAAM,EAAE,YAAY;gBACpBkB,QAAQ,EAAE,MAAM;gBAChBgB,UAAU,EAAE,GAAG;gBACfhH,KAAK,EAAE,OAAO;gBACdsI,SAAS,EAAE,QAAQ;gBACnBlB,UAAU,EAAE;cACd,CAAE;cAAA9C,QAAA,EAAC;YAEH;cAAA9N,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAELjC,OAAA;cAAKqL,KAAK,EAAE;gBACVuF,OAAO,EAAE,MAAM;gBACfC,mBAAmB,EAAE,sCAAsC;gBAC3DC,GAAG,EAAE;cACP,CAAE;cAAAlB,QAAA,EACC,CACC;gBACE1N,IAAI,EAAE,IAAI;gBACVE,KAAK,EAAE,aAAa;gBACpByR,QAAQ,EAAE,qBAAqB;gBAC/BC,IAAI,EAAE,mCAAmC;gBACzCC,MAAM,EAAEA,CAAA,KAAMjQ,YAAY,CAAC,SAAS,CAAC;gBACrCwH,KAAK,EAAE,SAAS;gBAChBwH,UAAU,EAAE;cACd,CAAC,EACD;gBACE5Q,IAAI,EAAE,IAAI;gBACVE,KAAK,EAAE,YAAY;gBACnByR,QAAQ,EAAE,iBAAiB;gBAC3BC,IAAI,EAAE,8BAA8B;gBACpCC,MAAM,EAAEA,CAAA,KAAMjQ,YAAY,CAAC,KAAK,CAAC;gBACjCwH,KAAK,EAAE,SAAS;gBAChBwH,UAAU,EAAE;cACd,CAAC,EACD;gBACE5Q,IAAI,EAAE,IAAI;gBACVE,KAAK,EAAE,eAAe;gBACtByR,QAAQ,EAAE,oBAAoB;gBAC9BC,IAAI,EAAE,gCAAgC;gBACtCC,MAAM,EAAEA,CAAA,KAAMjQ,YAAY,CAAC,QAAQ,CAAC;gBACpCwH,KAAK,EAAE,SAAS;gBAChBwH,UAAU,EAAE;cACd,CAAC,EACD;gBACE5Q,IAAI,EAAE,GAAG;gBACTE,KAAK,EAAE,iBAAiB;gBACxByR,QAAQ,EAAE,iBAAiB;gBAC3BC,IAAI,EAAE,gCAAgC;gBACtCC,MAAM,EAAEA,CAAA,KAAMjQ,YAAY,CAAC,WAAW,CAAC;gBACvCwH,KAAK,EAAE,SAAS;gBAChBwH,UAAU,EAAE;cACd,CAAC,CACF,CAACnR,GAAG,CAAC,CAACoS,MAAM,EAAE/C,KAAK,kBAClBhR,OAAA;gBACEqR,OAAO,EAAE0C,MAAM,CAACA,MAAO;gBACvB1I,KAAK,EAAE;kBACLsE,UAAU,EAAE,wEAAwE;kBACpFyC,cAAc,EAAE,YAAY;kBAC5BnB,YAAY,EAAE,MAAM;kBACpBZ,OAAO,EAAE,QAAQ;kBACjBe,MAAM,EAAE,SAAS;kBACjB6B,UAAU,EAAE,kDAAkD;kBAC9D1C,MAAM,EAAE,aAAawD,MAAM,CAACzI,KAAK,IAAI;kBACrC4D,QAAQ,EAAE,UAAU;kBACpBwC,QAAQ,EAAE,QAAQ;kBAClBW,SAAS,EAAE,0CAA0C0B,MAAM,CAACzI,KAAK;gBACnE,CAAE;gBACF4H,YAAY,EAAG7J,CAAC,IAAK;kBACnBA,CAAC,CAAC8J,aAAa,CAAC9H,KAAK,CAAC+H,SAAS,GAAG,8BAA8B;kBAChE/J,CAAC,CAAC8J,aAAa,CAAC9H,KAAK,CAACgH,SAAS,GAAG,yCAAyC0B,MAAM,CAACzI,KAAK,IAAI;kBAC3FjC,CAAC,CAAC8J,aAAa,CAAC9H,KAAK,CAAC2I,WAAW,GAAGD,MAAM,CAACzI,KAAK,GAAG,IAAI;gBACzD,CAAE;gBACF+H,YAAY,EAAGhK,CAAC,IAAK;kBACnBA,CAAC,CAAC8J,aAAa,CAAC9H,KAAK,CAAC+H,SAAS,GAAG,wBAAwB;kBAC1D/J,CAAC,CAAC8J,aAAa,CAAC9H,KAAK,CAACgH,SAAS,GAAG,0CAA0C0B,MAAM,CAACzI,KAAK,IAAI;kBAC5FjC,CAAC,CAAC8J,aAAa,CAAC9H,KAAK,CAAC2I,WAAW,GAAGD,MAAM,CAACzI,KAAK,GAAG,IAAI;gBACzD,CAAE;gBAAAsE,QAAA,gBAGF5P,OAAA;kBAAKqL,KAAK,EAAE;oBACV6D,QAAQ,EAAE,UAAU;oBACpByC,GAAG,EAAE,KAAK;oBACVC,IAAI,EAAE,KAAK;oBACXC,KAAK,EAAE,OAAO;oBACdC,MAAM,EAAE,OAAO;oBACfnC,UAAU,EAAEoE,MAAM,CAACjB,UAAU;oBAC7B7B,YAAY,EAAE,KAAK;oBACnBmC,SAAS,EAAE,uBAAuB;oBAClC3C,OAAO,EAAE,GAAG;oBACZlG,MAAM,EAAE,YAAY;oBACpBwH,SAAS,EAAE;kBACb;gBAAE;kBAAAjQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAELjC,OAAA;kBAAKqL,KAAK,EAAE;oBAAE6D,QAAQ,EAAE,UAAU;oBAAE8C,MAAM,EAAE,CAAC;oBAAE4B,SAAS,EAAE;kBAAS,CAAE;kBAAAhE,QAAA,gBACnE5P,OAAA;oBAAKqL,KAAK,EAAE;sBACViG,QAAQ,EAAE,MAAM;sBAChBZ,YAAY,EAAE,MAAM;sBACpBnG,MAAM,EAAE,oCAAoC;sBAC5CwH,SAAS,EAAE;oBACb,CAAE;oBAAAnC,QAAA,EACCmE,MAAM,CAAC7R;kBAAI;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eAENjC,OAAA;oBAAKqL,KAAK,EAAE;sBACViG,QAAQ,EAAE,QAAQ;sBAClBgB,UAAU,EAAE,GAAG;sBACfhH,KAAK,EAAE,OAAO;sBACdoF,YAAY,EAAE,QAAQ;sBACtBgC,UAAU,EAAE,YAAYqB,MAAM,CAACzI,KAAK,EAAE;sBACtCgI,aAAa,EAAE;oBACjB,CAAE;oBAAA1D,QAAA,EACCmE,MAAM,CAAC3R;kBAAK;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eAENjC,OAAA;oBAAKqL,KAAK,EAAE;sBACViG,QAAQ,EAAE,QAAQ;sBAClBhG,KAAK,EAAEyI,MAAM,CAACzI,KAAK;sBACnBgH,UAAU,EAAE,GAAG;sBACf5B,YAAY,EAAE,SAAS;sBACvB8C,aAAa,EAAE,WAAW;sBAC1BF,aAAa,EAAE;oBACjB,CAAE;oBAAA1D,QAAA,EACCmE,MAAM,CAACF;kBAAQ;oBAAA/R,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC,eAENjC,OAAA;oBAAKqL,KAAK,EAAE;sBACViG,QAAQ,EAAE,SAAS;sBACnBhG,KAAK,EAAE,uBAAuB;sBAC9BmI,SAAS,EAAE,QAAQ;sBACnBF,UAAU,EAAE;oBACd,CAAE;oBAAA3D,QAAA,EACCmE,MAAM,CAACD;kBAAI;oBAAAhS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eAGNjC,OAAA;oBAAKqL,KAAK,EAAE;sBACVmF,SAAS,EAAE,MAAM;sBACjBsB,MAAM,EAAE,KAAK;sBACbnC,UAAU,EAAE,uBAAuB;sBACnCsB,YAAY,EAAE,KAAK;sBACnBS,QAAQ,EAAE;oBACZ,CAAE;oBAAA9B,QAAA,eACA5P,OAAA;sBAAKqL,KAAK,EAAE;wBACVyG,MAAM,EAAE,MAAM;wBACdD,KAAK,EAAE,GAAG,EAAE,GAAGb,KAAK,GAAG,CAAC,GAAG;wBAC3BrB,UAAU,EAAEoE,MAAM,CAACjB,UAAU;wBAC7B7B,YAAY,EAAE,KAAK;wBACnBc,SAAS,EAAE;sBACb;oBAAE;sBAAAjQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GAjGE+O,KAAK;gBAAAlP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAkGV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNjC,OAAA;YAAKqL,KAAK,EAAE;cACVuF,OAAO,EAAE,MAAM;cACfC,mBAAmB,EAAE,SAAS;cAC9BC,GAAG,EAAE;YACP,CAAE;YAAAlB,QAAA,gBAGA5P,OAAA;cAAKqL,KAAK,EAAE;gBACVsE,UAAU,EAAEsE,YAAY,CAACC,YAAY,CAAC5D,OAAO;gBAC7CW,YAAY,EAAE,MAAM;gBACpBZ,OAAO,EAAE,QAAQ;gBACjBgC,SAAS,EAAE,cAAc4B,YAAY,CAACC,YAAY,CAACC,MAAM,EAAE;gBAC3D5D,MAAM,EAAE,aAAa0D,YAAY,CAACC,YAAY,CAAC3D,MAAM;cACvD,CAAE;cAAAX,QAAA,gBACA5P,OAAA;gBAAIqL,KAAK,EAAE;kBACT+E,MAAM,EAAE,cAAc;kBACtBkB,QAAQ,EAAE,QAAQ;kBAClBgB,UAAU,EAAE,GAAG;kBACfhH,KAAK,EAAE2I,YAAY,CAACC,YAAY,CAACvL;gBACnC,CAAE;gBAAAiH,QAAA,EAAC;cAEH;gBAAA9N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAELjC,OAAA;gBAAKqL,KAAK,EAAE;kBACV+I,SAAS,EAAE,OAAO;kBAClBC,SAAS,EAAE;gBACb,CAAE;gBAAAzE,QAAA,EACCzK,WAAW,CAACkC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC1F,GAAG,CAAC,CAACqG,GAAG,EAAEgJ,KAAK,kBACtChR,OAAA;kBAAiBqL,KAAK,EAAE;oBACtBuF,OAAO,EAAE,MAAM;oBACfO,UAAU,EAAE,QAAQ;oBACpBL,GAAG,EAAE,MAAM;oBACXT,OAAO,EAAE,SAAS;oBAClBY,YAAY,EAAE,KAAK;oBACnBP,YAAY,EAAE,QAAQ;oBACtBf,UAAU,EAAEqB,KAAK,GAAG,CAAC,KAAK,CAAC,GAAGiD,YAAY,CAACC,YAAY,CAACI,SAAS,GAAG,aAAa;oBACjFrB,UAAU,EAAE;kBACd,CAAE;kBAAArD,QAAA,gBACA5P,OAAA;oBAAKqL,KAAK,EAAE;sBACVwG,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdb,YAAY,EAAE,KAAK;sBACnBtB,UAAU,EAAE3H,GAAG,CAACnB,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS;sBACxD0N,UAAU,EAAE;oBACd;kBAAE;oBAAAzS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACLjC,OAAA;oBAAKqL,KAAK,EAAE;sBAAEmJ,IAAI,EAAE;oBAAE,CAAE;oBAAA5E,QAAA,gBACtB5P,OAAA;sBAAKqL,KAAK,EAAE;wBACViG,QAAQ,EAAE,QAAQ;wBAClBgB,UAAU,EAAE,GAAG;wBACfhH,KAAK,EAAE2I,YAAY,CAACC,YAAY,CAACvL,IAAI;wBACrC+H,YAAY,EAAE;sBAChB,CAAE;sBAAAd,QAAA,EACC5H,GAAG,CAACpB;oBAAG;sBAAA9E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACNjC,OAAA;sBAAKqL,KAAK,EAAE;wBACViG,QAAQ,EAAE,QAAQ;wBAClBhG,KAAK,EAAE2I,YAAY,CAACC,YAAY,CAACvD;sBACnC,CAAE;sBAAAf,QAAA,EACC,IAAI1I,IAAI,CAACc,GAAG,CAAC2G,IAAI,CAAC,CAAC8F,cAAc,CAAC;oBAAC;sBAAA3S,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAhCE+O,KAAK;kBAAAlP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAiCV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNjC,OAAA;cAAKqL,KAAK,EAAE;gBACVsE,UAAU,EAAEsE,YAAY,CAACC,YAAY,CAAC5D,OAAO;gBAC7CW,YAAY,EAAE,MAAM;gBACpBZ,OAAO,EAAE,QAAQ;gBACjBgC,SAAS,EAAE,cAAc4B,YAAY,CAACC,YAAY,CAACC,MAAM,EAAE;gBAC3D5D,MAAM,EAAE,aAAa0D,YAAY,CAACC,YAAY,CAAC3D,MAAM;cACvD,CAAE;cAAAX,QAAA,gBACA5P,OAAA;gBAAIqL,KAAK,EAAE;kBACT+E,MAAM,EAAE,cAAc;kBACtBkB,QAAQ,EAAE,QAAQ;kBAClBgB,UAAU,EAAE,GAAG;kBACfhH,KAAK,EAAE2I,YAAY,CAACC,YAAY,CAACvL;gBACnC,CAAE;gBAAAiH,QAAA,EAAC;cAEH;gBAAA9N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAELjC,OAAA;gBAAKqL,KAAK,EAAE;kBAAEuF,OAAO,EAAE,MAAM;kBAAE8D,aAAa,EAAE,QAAQ;kBAAE5D,GAAG,EAAE;gBAAO,CAAE;gBAAAlB,QAAA,gBACpE5P,OAAA;kBAAOqL,KAAK,EAAE;oBACZuF,OAAO,EAAE,MAAM;oBACfO,UAAU,EAAE,QAAQ;oBACpBL,GAAG,EAAE,SAAS;oBACdT,OAAO,EAAE,MAAM;oBACfY,YAAY,EAAE,MAAM;oBACpBtB,UAAU,EAAEsE,YAAY,CAACC,YAAY,CAACI,SAAS;oBAC/C/D,MAAM,EAAE,cAAc0D,YAAY,CAACC,YAAY,CAAC3D,MAAM,EAAE;oBACxDa,MAAM,EAAE7M,mBAAmB,GAAG,aAAa,GAAG,SAAS;oBACvD0O,UAAU,EAAE;kBACd,CAAE;kBACFC,YAAY,EAAG7J,CAAC,IAAK;oBACnB,IAAI,CAAC9E,mBAAmB,EAAE;sBACxB8E,CAAC,CAAC8J,aAAa,CAAC9H,KAAK,CAAC2I,WAAW,GAAGC,YAAY,CAACC,YAAY,CAAC3C,OAAO;sBACrElI,CAAC,CAAC8J,aAAa,CAAC9H,KAAK,CAACsE,UAAU,GAAGsE,YAAY,CAACC,YAAY,CAAC3C,OAAO,GAAG,IAAI;oBAC7E;kBACF,CAAE;kBACF8B,YAAY,EAAGhK,CAAC,IAAK;oBACnBA,CAAC,CAAC8J,aAAa,CAAC9H,KAAK,CAAC2I,WAAW,GAAGC,YAAY,CAACC,YAAY,CAAC3D,MAAM;oBACpElH,CAAC,CAAC8J,aAAa,CAAC9H,KAAK,CAACsE,UAAU,GAAGsE,YAAY,CAACC,YAAY,CAACI,SAAS;kBACxE,CAAE;kBAAA1E,QAAA,gBACA5P,OAAA,CAACX,QAAQ;oBAACmS,IAAI,EAAE,EAAG;oBAAClG,KAAK,EAAE2I,YAAY,CAACC,YAAY,CAAC3C;kBAAQ;oBAAAzP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAChEjC,OAAA;oBAAA4P,QAAA,gBACE5P,OAAA;sBAAKqL,KAAK,EAAE;wBACViH,UAAU,EAAE,GAAG;wBACfhH,KAAK,EAAE2I,YAAY,CAACC,YAAY,CAACvL,IAAI;wBACrC+H,YAAY,EAAE;sBAChB,CAAE;sBAAAd,QAAA,EACCrL,mBAAmB,GAAG,cAAc,GAAG;oBAAe;sBAAAzC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD,CAAC,eACNjC,OAAA;sBAAKqL,KAAK,EAAE;wBACViG,QAAQ,EAAE,QAAQ;wBAClBhG,KAAK,EAAE2I,YAAY,CAACC,YAAY,CAACvD;sBACnC,CAAE;sBAAAf,QAAA,EAAC;oBAEH;sBAAA9N,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNjC,OAAA;oBACE6G,IAAI,EAAC,MAAM;oBACX8N,MAAM,EAAC,iBAAiB;oBACxBC,QAAQ,EAAExL,kBAAmB;oBAC7ByL,QAAQ,EAAEtQ,mBAAoB;oBAC9B8G,KAAK,EAAE;sBAAEuF,OAAO,EAAE;oBAAO;kBAAE;oBAAA9O,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC,EAEPwC,SAAS,iBACRzE,OAAA;kBACE8U,IAAI,EAAErQ,SAAU;kBAChB8E,MAAM,EAAC,QAAQ;kBACfwL,GAAG,EAAC,qBAAqB;kBACzB1J,KAAK,EAAE;oBACLuF,OAAO,EAAE,MAAM;oBACfO,UAAU,EAAE,QAAQ;oBACpBL,GAAG,EAAE,SAAS;oBACdT,OAAO,EAAE,MAAM;oBACfY,YAAY,EAAE,MAAM;oBACpBtB,UAAU,EAAE,SAAS;oBACrBrE,KAAK,EAAE,OAAO;oBACd0J,cAAc,EAAE,MAAM;oBACtB1C,UAAU,EAAE,GAAG;oBACfW,UAAU,EAAE;kBACd,CAAE;kBACFC,YAAY,EAAG7J,CAAC,IAAK;oBACnBA,CAAC,CAAC8J,aAAa,CAAC9H,KAAK,CAACsE,UAAU,GAAG,SAAS;oBAC5CtG,CAAC,CAAC8J,aAAa,CAAC9H,KAAK,CAAC+H,SAAS,GAAG,kBAAkB;kBACtD,CAAE;kBACFC,YAAY,EAAGhK,CAAC,IAAK;oBACnBA,CAAC,CAAC8J,aAAa,CAAC9H,KAAK,CAACsE,UAAU,GAAG,SAAS;oBAC5CtG,CAAC,CAAC8J,aAAa,CAAC9H,KAAK,CAAC+H,SAAS,GAAG,eAAe;kBACnD,CAAE;kBAAAxD,QAAA,gBAEF5P,OAAA,CAACnB,UAAU;oBAAC2S,IAAI,EAAE;kBAAG;oBAAA1P,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACxBjC,OAAA;oBAAA4P,QAAA,EAAM;kBAAW;oBAAA9N,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CACJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA4B,SAAS,KAAK,QAAQ,iBACrB7D,OAAA;QAAKqL,KAAK,EAAE;UAAEgF,OAAO,EAAE,MAAM;UAAE8B,QAAQ,EAAE,QAAQ;UAAE/B,MAAM,EAAE;QAAS,CAAE;QAAAR,QAAA,eACpE5P,OAAA;UAAKqL,KAAK,EAAE4J,QAAQ,CAAC,MAAM,CAAE;UAAArF,QAAA,gBAC3B5P,OAAA;YAAIqL,KAAK,EAAE;cACTmF,SAAS,EAAE,CAAC;cACZlF,KAAK,EAAE;YACT,CAAE;YAAAsE,QAAA,EAAC;UAAgB;YAAA9N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBjC,OAAA;YAAGqL,KAAK,EAAE;cACRoF,OAAO,EAAE,GAAG;cACZC,YAAY,EAAE,MAAM;cACpBpF,KAAK,EAAE;YACT,CAAE;YAAAsE,QAAA,EAAC;UAEH;YAAA9N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAGJjC,OAAA;YAAKqL,KAAK,EAAE;cACVyG,MAAM,EAAE,MAAM;cACduC,SAAS,EAAE,MAAM;cACjB3D,YAAY,EAAE,MAAM;cACpBL,OAAO,EAAE,MAAM;cACfxB,eAAe,EAAE,SAAS;cAC1B0B,MAAM,EAAE,mBAAmB;cAC3BU,YAAY,EAAE;YAChB,CAAE;YAAArB,QAAA,GAECvM,QAAQ,CAACuL,MAAM,KAAK,CAAC,gBACpB5O,OAAA;cAAKqL,KAAK,EAAE;gBACVyG,MAAM,EAAE,MAAM;gBACdlB,OAAO,EAAE,MAAM;gBACf8D,aAAa,EAAE,QAAQ;gBACvBvD,UAAU,EAAE,QAAQ;gBACpBD,cAAc,EAAE,QAAQ;gBACxB0C,SAAS,EAAE,QAAQ;gBACnBnD,OAAO,EAAE;cACX,CAAE;cAAAb,QAAA,gBACA5P,OAAA;gBAAKqL,KAAK,EAAE;kBAAEiG,QAAQ,EAAE,MAAM;kBAAEZ,YAAY,EAAE;gBAAO,CAAE;gBAAAd,QAAA,EAAC;cAAE;gBAAA9N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChEjC,OAAA;gBAAIqL,KAAK,EAAE;kBACT+E,MAAM,EAAE,CAAC;kBACT9E,KAAK,EAAE;gBACT,CAAE;gBAAAsE,QAAA,EAAC;cAAoB;gBAAA9N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5BjC,OAAA;gBAAGqL,KAAK,EAAE;kBACRC,KAAK,EAAE;gBACT,CAAE;gBAAAsE,QAAA,EAAC;cAA+C;gBAAA9N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,GAENoB,QAAQ,CAAC1B,GAAG,CAAC,CAACiF,GAAG,EAAEsO,GAAG,kBACpBlV,OAAA;cAEEqL,KAAK,EAAE;gBACL,IAAIzE,GAAG,CAAC0F,IAAI,KAAK,MAAM,GAAG2I,QAAQ,CAAC,gBAAgB,CAAC,GAAGA,QAAQ,CAAC,eAAe,CAAC,CAAC;gBACjFlD,SAAS,EAAE;cACb,CAAE;cAAAnC,QAAA,EAEDhJ,GAAG,CAAC0F,IAAI,KAAK,KAAK,gBACjBtM,OAAA,CAACmV,aAAa;gBAAAvF,QAAA,EAAEhJ,GAAG,CAAC2F;cAAO;gBAAAzK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAgB,CAAC,GAE5C2E,GAAG,CAAC2F;YACL,GAVI2I,GAAG;cAAApT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAWL,CACN,CACF,EACAwB,OAAO,iBACNzD,OAAA;cAAKqL,KAAK,EAAE4J,QAAQ,CAAC,eAAe,CAAE;cAAArF,QAAA,eACpC5P,OAAA;gBAAKqL,KAAK,EAAE;kBAAEuF,OAAO,EAAE,MAAM;kBAAEO,UAAU,EAAE,QAAQ;kBAAEL,GAAG,EAAE;gBAAM,CAAE;gBAAAlB,QAAA,gBAChE5P,OAAA;kBAAKqL,KAAK,EAAE;oBACVwG,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE,MAAM;oBACdb,YAAY,EAAE,KAAK;oBACnBpC,eAAe,EAAE,SAAS;oBAC1BkD,SAAS,EAAE;kBACb;gBAAE;kBAAAjQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACLjC,OAAA;kBAAKqL,KAAK,EAAE;oBACVwG,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE,MAAM;oBACdb,YAAY,EAAE,KAAK;oBACnBpC,eAAe,EAAE,SAAS;oBAC1BkD,SAAS,EAAE,iCAAiC;oBAC5CqD,cAAc,EAAE;kBAClB;gBAAE;kBAAAtT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACLjC,OAAA;kBAAKqL,KAAK,EAAE;oBACVwG,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE,MAAM;oBACdb,YAAY,EAAE,KAAK;oBACnBpC,eAAe,EAAE,SAAS;oBAC1BkD,SAAS,EAAE,iCAAiC;oBAC5CqD,cAAc,EAAE;kBAClB;gBAAE;kBAAAtT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,eACDjC,OAAA;cAAKqV,GAAG,EAAEhQ;YAAW;cAAAvD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eAGNjC,OAAA;YACEqL,KAAK,EAAE;cAAEuF,OAAO,EAAE,MAAM;cAAEE,GAAG,EAAE;YAAO,CAAE;YACxCwE,QAAQ,EAAEjM,CAAC,IAAI;cACbA,CAAC,CAACkM,cAAc,CAAC,CAAC;cAClBpJ,WAAW,CAAC,CAAC;YACf,CAAE;YAAAyD,QAAA,gBAEF5P,OAAA;cACE6G,IAAI,EAAC,MAAM;cACX2O,WAAW,EAAC,sBAAsB;cAClCnK,KAAK,EAAE4J,QAAQ,CAAC,YAAY,CAAE;cAC9BrC,KAAK,EAAEzP,KAAM;cACbyR,QAAQ,EAAEvL,CAAC,IAAIjG,QAAQ,CAACiG,CAAC,CAACE,MAAM,CAACqJ,KAAK,CAAE;cACxCiC,QAAQ,EAAEpR;YAAQ;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACFjC,OAAA;cACE6G,IAAI,EAAC,QAAQ;cACbwE,KAAK,EAAE;gBACL,GAAG4J,QAAQ,CAAC,eAAe,CAAC;gBAC5BQ,QAAQ,EAAE;cACZ,CAAE;cACFZ,QAAQ,EAAEpR,OAAO,IAAI,CAACN,KAAK,CAACiJ,IAAI,CAAC,CAAE;cAAAwD,QAAA,EAElCnM,OAAO,GAAG,YAAY,GAAG;YAAM;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA4B,SAAS,KAAK,KAAK,iBAClB7D,OAAA;QAAKqL,KAAK,EAAE;UAAEgF,OAAO,EAAE;QAAO,CAAE;QAAAT,QAAA,eAC9B5P,OAAA;UAAKqL,KAAK,EAAE4J,QAAQ,CAAC,MAAM,CAAE;UAAArF,QAAA,gBAE3B5P,OAAA;YAAKqL,KAAK,EAAE;cAAEuF,OAAO,EAAE,MAAM;cAAEM,cAAc,EAAE,eAAe;cAAEC,UAAU,EAAE,QAAQ;cAAET,YAAY,EAAE;YAAO,CAAE;YAAAd,QAAA,gBAC3G5P,OAAA;cAAA4P,QAAA,gBACE5P,OAAA;gBAAIqL,KAAK,EAAE;kBAAEmF,SAAS,EAAE,CAAC;kBAAEE,YAAY,EAAE;gBAAM,CAAE;gBAAAd,QAAA,EAAC;cAA6B;gBAAA9N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpFjC,OAAA;gBAAGqL,KAAK,EAAE;kBAAEoF,OAAO,EAAE,GAAG;kBAAEL,MAAM,EAAE;gBAAE,CAAE;gBAAAR,QAAA,EAAC;cAEvC;gBAAA9N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,EACL6D,gBAAgB,iBACf9F,OAAA;cACEqR,OAAO,EAAErG,mBAAoB;cAC7BK,KAAK,EAAE;gBACL,GAAG4J,QAAQ,CAAC,eAAe,CAAC;gBAC5BtF,UAAU,EAAE,SAAS;gBACrBiB,OAAO,EAAE,MAAM;gBACfO,UAAU,EAAE,QAAQ;gBACpBL,GAAG,EAAE,KAAK;gBACVQ,QAAQ,EAAE,MAAM;gBAChBjB,OAAO,EAAE,UAAU;gBACnBE,MAAM,EAAE,MAAM;gBACdU,YAAY,EAAE,KAAK;gBACnB3F,KAAK,EAAE,OAAO;gBACd8F,MAAM,EAAE,SAAS;gBACjB6B,UAAU,EAAE;cACd,CAAE;cACFC,YAAY,EAAG7J,CAAC,IAAKA,CAAC,CAACE,MAAM,CAAC8B,KAAK,CAACsE,UAAU,GAAG,SAAU;cAC3D0D,YAAY,EAAGhK,CAAC,IAAKA,CAAC,CAACE,MAAM,CAAC8B,KAAK,CAACsE,UAAU,GAAG,SAAU;cAAAC,QAAA,gBAE3D5P,OAAA,CAACH,WAAW;gBAAC2R,IAAI,EAAE;cAAG;gBAAA1P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,uBAE3B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNjC,OAAA;YAAKqL,KAAK,EAAE;cACVuF,OAAO,EAAE,MAAM;cACfE,GAAG,EAAE,KAAK;cACVJ,YAAY,EAAE,MAAM;cACpBgF,QAAQ,EAAE,MAAM;cAChBC,YAAY,EAAE,gBAAgB;cAC9BC,aAAa,EAAE;YACjB,CAAE;YAAAhG,QAAA,EACC,CAAC,KAAK,EAAE,GAAGiG,MAAM,CAACC,IAAI,CAACxP,iBAAiB,CAAC,CAAC,CAAC3E,GAAG,CAACoU,QAAQ,iBACtD/V,OAAA;cAEEqR,OAAO,EAAEA,CAAA,KAAM9L,mBAAmB,CAACwQ,QAAQ,CAAE;cAC7C1K,KAAK,EAAE;gBACLgF,OAAO,EAAE,UAAU;gBACnBY,YAAY,EAAE,MAAM;gBACpBV,MAAM,EAAEjL,gBAAgB,KAAKyQ,QAAQ,GACjC,MAAM,GACN,gBAAgB;gBACpBpG,UAAU,EAAErK,gBAAgB,KAAKyQ,QAAQ,GACrC9B,YAAY,CAACC,YAAY,CAAC3C,OAAO,GACjC,aAAa;gBACjBjG,KAAK,EAAEhG,gBAAgB,KAAKyQ,QAAQ,GAChC,OAAO,GACP,MAAM;gBACV3E,MAAM,EAAE,SAAS;gBACjBE,QAAQ,EAAE,MAAM;gBAChBgB,UAAU,EAAEhN,gBAAgB,KAAKyQ,QAAQ,GAAG,GAAG,GAAG,GAAG;gBACrD9C,UAAU,EAAE,eAAe;gBAC3BO,aAAa,EAAE;cACjB,CAAE;cACFN,YAAY,EAAG7J,CAAC,IAAK;gBACnB,IAAI/D,gBAAgB,KAAKyQ,QAAQ,EAAE;kBACjC1M,CAAC,CAACE,MAAM,CAAC8B,KAAK,CAACsE,UAAU,GAAG,SAAS;gBACvC;cACF,CAAE;cACF0D,YAAY,EAAGhK,CAAC,IAAK;gBACnB,IAAI/D,gBAAgB,KAAKyQ,QAAQ,EAAE;kBACjC1M,CAAC,CAACE,MAAM,CAAC8B,KAAK,CAACsE,UAAU,GAAG,aAAa;gBAC3C;cACF,CAAE;cAAAC,QAAA,EAEDmG,QAAQ,KAAK,KAAK,GAAG,QAAQ,GAAG,GAAGA,QAAQ,KAAK,OAAO,GAAG,IAAI,GAAGA,QAAQ,KAAK,UAAU,GAAG,IAAI,GAAGA,QAAQ,KAAK,UAAU,GAAG,IAAI,GAAGA,QAAQ,KAAK,SAAS,GAAG,IAAI,GAAGA,QAAQ,KAAK,QAAQ,GAAG,MAAM,GAAG,IAAI,IAAIA,QAAQ;YAAE,GA/BlNA,QAAQ;cAAAjU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgCP,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNjC,OAAA;YAAKqL,KAAK,EAAE;cACVuF,OAAO,EAAE,MAAM;cACfE,GAAG,EAAE,MAAM;cACXJ,YAAY,EAAE,MAAM;cACpBgF,QAAQ,EAAE,MAAM;cAChBvE,UAAU,EAAE;YACd,CAAE;YAAAvB,QAAA,gBAEA5P,OAAA;cAAKqL,KAAK,EAAE;gBAAE6D,QAAQ,EAAE,UAAU;gBAAEsF,IAAI,EAAE,CAAC;gBAAEiB,QAAQ,EAAE;cAAQ,CAAE;cAAA7F,QAAA,gBAC/D5P,OAAA;gBAAKqL,KAAK,EAAE;kBACV6D,QAAQ,EAAE,UAAU;kBACpB0C,IAAI,EAAE,MAAM;kBACZD,GAAG,EAAE,KAAK;kBACVyB,SAAS,EAAE,kBAAkB;kBAC7B9H,KAAK,EAAE;gBACT,CAAE;gBAAAsE,QAAA,eACA5P,OAAA,CAACZ,QAAQ;kBAACoS,IAAI,EAAE;gBAAG;kBAAA1P,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eACNjC,OAAA;gBACE6G,IAAI,EAAC,MAAM;gBACX2O,WAAW,EAAC,qBAAqB;gBACjCnK,KAAK,EAAE;kBACL,GAAG4J,QAAQ,CAAC,YAAY,CAAC;kBACzBe,WAAW,EAAE,MAAM;kBACnBnE,KAAK,EAAE;gBACT,CAAE;gBACFe,KAAK,EAAE7O,UAAW;gBAClB6Q,QAAQ,EAAGvL,CAAC,IAAKrF,aAAa,CAACqF,CAAC,CAACE,MAAM,CAACqJ,KAAK;cAAE;gBAAA9Q,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,EACD8B,UAAU,iBACT/D,OAAA;gBACEqL,KAAK,EAAE;kBACL6D,QAAQ,EAAE,UAAU;kBACpB+C,KAAK,EAAE,MAAM;kBACbN,GAAG,EAAE,KAAK;kBACVyB,SAAS,EAAE,kBAAkB;kBAC7BzD,UAAU,EAAE,MAAM;kBAClBY,MAAM,EAAE,MAAM;kBACdjF,KAAK,EAAE,MAAM;kBACb8F,MAAM,EAAE;gBACV,CAAE;gBACFC,OAAO,EAAEA,CAAA,KAAMrN,aAAa,CAAC,EAAE,CAAE;gBAAA4L,QAAA,eAEjC5P,OAAA,CAACiW,GAAG;kBAACzE,IAAI,EAAE;gBAAG;kBAAA1P,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNjC,OAAA;cACE4S,KAAK,EAAEpN,MAAO;cACdoP,QAAQ,EAAGvL,CAAC,IAAK5D,SAAS,CAAC4D,CAAC,CAACE,MAAM,CAACqJ,KAAK,CAAE;cAC3CvH,KAAK,EAAE;gBACL,GAAG4J,QAAQ,CAAC,YAAY,CAAC;gBACzBpD,KAAK,EAAE,MAAM;gBACb4D,QAAQ,EAAE;cACZ,CAAE;cAAA7F,QAAA,gBAEF5P,OAAA;gBAAQ4S,KAAK,EAAC,MAAM;gBAAAhD,QAAA,EAAC;cAAe;gBAAA9N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC7CjC,OAAA;gBAAQ4S,KAAK,EAAC,WAAW;gBAAAhD,QAAA,EAAC;cAAiB;gBAAA9N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAGL2D,eAAe,CAACgJ,MAAM,GAAG,CAAC,iBACzB5O,OAAA;YAAKqL,KAAK,EAAE;cACVqF,YAAY,EAAE,MAAM;cACpBL,OAAO,EAAE,MAAM;cACfY,YAAY,EAAE,MAAM;cACpBtB,UAAU,EAAE,SAAS;cACrBY,MAAM,EAAE;YACV,CAAE;YAAAX,QAAA,gBACA5P,OAAA;cAAIqL,KAAK,EAAE;gBACTuF,OAAO,EAAE,MAAM;gBACfO,UAAU,EAAE,QAAQ;gBACpBL,GAAG,EAAE,KAAK;gBACVQ,QAAQ,EAAE,MAAM;gBAChBZ,YAAY,EAAE,MAAM;gBACpBpF,KAAK,EAAE,MAAM;gBACb8E,MAAM,EAAE;cACV,CAAE;cAAAR,QAAA,gBACA5P,OAAA,CAACJ,OAAO;gBAAC0L,KAAK,EAAC;cAAM;gBAAAxJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oBAC1B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLjC,OAAA;cAAKqL,KAAK,EAAE;gBACVuF,OAAO,EAAE,MAAM;gBACfE,GAAG,EAAE,KAAK;gBACV4E,QAAQ,EAAE;cACZ,CAAE;cAAA9F,QAAA,EACChK,eAAe,CAACjE,GAAG,CAAC0I,OAAO,iBAC1BrK,OAAA;gBAEEqR,OAAO,EAAEA,CAAA,KAAMjH,kBAAkB,CAACC,OAAO,CAAE;gBAC3CgB,KAAK,EAAE;kBACLgF,OAAO,EAAE,UAAU;kBACnBY,YAAY,EAAE,MAAM;kBACpBV,MAAM,EAAE,aAAa0D,YAAY,CAACC,YAAY,CAAC3C,OAAO,EAAE;kBACxD5B,UAAU,EAAE,aAAa;kBACzBrE,KAAK,EAAE2I,YAAY,CAACC,YAAY,CAAC3C,OAAO;kBACxCH,MAAM,EAAE,SAAS;kBACjBE,QAAQ,EAAE,MAAM;kBAChB2B,UAAU,EAAE;gBACd,CAAE;gBACFC,YAAY,EAAG7J,CAAC,IAAK;kBACnBA,CAAC,CAACE,MAAM,CAAC8B,KAAK,CAACsE,UAAU,GAAGsE,YAAY,CAACC,YAAY,CAAC3C,OAAO;kBAC7DlI,CAAC,CAACE,MAAM,CAAC8B,KAAK,CAACC,KAAK,GAAG,OAAO;gBAChC,CAAE;gBACF+H,YAAY,EAAGhK,CAAC,IAAK;kBACnBA,CAAC,CAACE,MAAM,CAAC8B,KAAK,CAACsE,UAAU,GAAG,aAAa;kBACzCtG,CAAC,CAACE,MAAM,CAAC8B,KAAK,CAACC,KAAK,GAAG2I,YAAY,CAACC,YAAY,CAAC3C,OAAO;gBAC1D,CAAE;gBAAA3B,QAAA,EAEDvF;cAAO,GArBHA,OAAO;gBAAAvI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsBN,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDjC,OAAA;YAAKqL,KAAK,EAAE;cACVuF,OAAO,EAAE,MAAM;cACfC,mBAAmB,EAAE,uCAAuC;cAC5DC,GAAG,EAAE,MAAM;cACXN,SAAS,EAAE;YACb,CAAE;YAAAZ,QAAA,EACCrE,iBAAiB,CAAC5J,GAAG,CAAC,CAAC0I,OAAO,EAAE2G,KAAK,kBACpChR,OAAA;cAEEqL,KAAK,EAAE;gBACL,GAAG4J,QAAQ,CAAC,aAAa,CAAC;gBAC1B/F,QAAQ,EAAE,UAAU;gBACpBkE,SAAS,EAAE1N,iBAAiB,CAACqF,QAAQ,CAACV,OAAO,CAAC,GAAG,aAAa,GAAG,UAAU;gBAC3EkG,MAAM,EAAE7K,iBAAiB,CAACqF,QAAQ,CAACV,OAAO,CAAC,GACvC,aAAa4J,YAAY,CAACC,YAAY,CAAC3C,OAAO,EAAE,GAChD,aAAa0C,YAAY,CAACC,YAAY,CAAC3D,MAAM,EAAE;gBACnDZ,UAAU,EAAEsE,YAAY,CAACC,YAAY,CAAC5D,OAAO;gBAC7ChF,KAAK,EAAE2I,YAAY,CAACC,YAAY,CAACvL,IAAI;gBACrCoJ,SAAS,EAAE,oBAAoBf,KAAK,GAAG,GAAG,QAAQ;gBAClDqB,SAAS,EAAE,aAAa4B,YAAY,CAACC,YAAY,CAACC,MAAM;cAC1D,CAAE;cACF9C,OAAO,EAAEA,CAAA,KAAMjH,kBAAkB,CAACC,OAAO,CAAE;cAAAuF,QAAA,gBAG3C5P,OAAA;gBACEqR,OAAO,EAAGhI,CAAC,IAAKwB,cAAc,CAACR,OAAO,EAAEhB,CAAC,CAAE;gBAC3CgC,KAAK,EAAE;kBACL6D,QAAQ,EAAE,UAAU;kBACpByC,GAAG,EAAE,KAAK;kBACVM,KAAK,EAAE,KAAK;kBACZtC,UAAU,EAAE,MAAM;kBAClBY,MAAM,EAAE,MAAM;kBACda,MAAM,EAAE,SAAS;kBACjB9F,KAAK,EAAE5F,iBAAiB,CAACqF,QAAQ,CAACV,OAAO,CAAC,GAAG,SAAS,GAAG,MAAM;kBAC/D4I,UAAU,EAAE,eAAe;kBAC3B3B,QAAQ,EAAE;gBACZ,CAAE;gBAAA1B,QAAA,eAEF5P,OAAA,CAACL,OAAO;kBAACuW,IAAI,EAAExQ,iBAAiB,CAACqF,QAAQ,CAACV,OAAO,CAAC,GAAG,cAAc,GAAG;gBAAO;kBAAAvI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC,eAGTjC,OAAA;gBAAKqL,KAAK,EAAE;kBACVwG,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdb,YAAY,EAAE,KAAK;kBACnBtB,UAAU,EAAE,2BAA2BsE,YAAY,CAACC,YAAY,CAAC3C,OAAO,KAAK0C,YAAY,CAACC,YAAY,CAACiC,WAAW,GAAG;kBACrH7K,KAAK,EAAE,OAAO;kBACdsF,OAAO,EAAE,MAAM;kBACfO,UAAU,EAAE,QAAQ;kBACpBD,cAAc,EAAE,QAAQ;kBACxBI,QAAQ,EAAE,MAAM;kBAChBgB,UAAU,EAAE,GAAG;kBACf5B,YAAY,EAAE,MAAM;kBACpB2B,SAAS,EAAE,aAAa4B,YAAY,CAACC,YAAY,CAACC,MAAM;gBAC1D,CAAE;gBAAAvE,QAAA,EACCvF,OAAO,CAAC+L,MAAM,CAAC,CAAC;cAAC;gBAAAtU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eAGNjC,OAAA;gBAAKqL,KAAK,EAAE;kBACViH,UAAU,EAAE,GAAG;kBACfsB,SAAS,EAAE,QAAQ;kBACnBtC,QAAQ,EAAE,MAAM;kBAChBZ,YAAY,EAAE;gBAChB,CAAE;gBAAAd,QAAA,EACCvF;cAAO;gBAAAvI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGNjC,OAAA;gBAAKqL,KAAK,EAAE;kBACVuF,OAAO,EAAE,MAAM;kBACfM,cAAc,EAAE,eAAe;kBAC/BI,QAAQ,EAAE,MAAM;kBAChBb,OAAO,EAAE,GAAG;kBACZD,SAAS,EAAE;gBACb,CAAE;gBAAAZ,QAAA,gBACA5P,OAAA;kBAAA4P,QAAA,GAAM,eAAG,EAAC8D,IAAI,CAAC2C,KAAK,CAAC3C,IAAI,CAAC4C,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,EAAC,YAAU;gBAAA;kBAAAxU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/DjC,OAAA;kBAAA4P,QAAA,GAAM,SAAE,EAAC,CAAC8D,IAAI,CAAC4C,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAzU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,EAGL4T,MAAM,CAACW,OAAO,CAAClQ,iBAAiB,CAAC,CAAC3E,GAAG,CAAC,CAAC,CAACoU,QAAQ,EAAExP,SAAS,CAAC,KAAK;gBAChE,IAAIA,SAAS,CAACkF,IAAI,CAACjB,CAAC,IAAIA,CAAC,CAACnI,WAAW,CAAC,CAAC,KAAKgI,OAAO,CAAChI,WAAW,CAAC,CAAC,CAAC,EAAE;kBAClE,oBACErC,OAAA;oBAEEqL,KAAK,EAAE;sBACL6D,QAAQ,EAAE,UAAU;sBACpByC,GAAG,EAAE,KAAK;sBACVC,IAAI,EAAE,KAAK;sBACXjC,UAAU,EAAEsE,YAAY,CAACC,YAAY,CAAC3C,OAAO;sBAC7CjG,KAAK,EAAE,OAAO;sBACd+E,OAAO,EAAE,SAAS;sBAClBY,YAAY,EAAE,KAAK;sBACnBK,QAAQ,EAAE,MAAM;sBAChBgB,UAAU,EAAE;oBACd,CAAE;oBAAA1C,QAAA,EAEDmG;kBAAQ,GAbJA,QAAQ;oBAAAjU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAcV,CAAC;gBAEV;gBACA,OAAO,IAAI;cACb,CAAC,CAAC;YAAA,GAhGG+O,KAAK;cAAAlP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiGP,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EAGLsJ,iBAAiB,CAACqD,MAAM,KAAK,CAAC,iBAC7B5O,OAAA;YAAKqL,KAAK,EAAE;cACVuI,SAAS,EAAE,QAAQ;cACnBvD,OAAO,EAAE,MAAM;cACfI,OAAO,EAAE,GAAG;cACZnF,KAAK,EAAE;YACT,CAAE;YAAAsE,QAAA,gBACA5P,OAAA;cAAKqL,KAAK,EAAE;gBAAEiG,QAAQ,EAAE,MAAM;gBAAEZ,YAAY,EAAE;cAAO,CAAE;cAAAd,QAAA,EAAC;YAAE;cAAA9N,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChEjC,OAAA;cAAIqL,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAO,CAAE;cAAAsE,QAAA,EAAC;YAAkB;cAAA9N,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrDjC,OAAA;cAAGqL,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAO,CAAE;cAAAsE,QAAA,EAAC;YAA4C;cAAA9N,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA4B,SAAS,KAAK,SAAS,iBACtB7D,OAAA;QAAKqL,KAAK,EAAE;UAAEgF,OAAO,EAAE;QAAO,CAAE;QAAAT,QAAA,eAC9B5P,OAAA;UAAKqL,KAAK,EAAE4J,QAAQ,CAAC,MAAM,CAAE;UAAArF,QAAA,gBAC3B5P,OAAA;YAAIqL,KAAK,EAAE;cAAEmF,SAAS,EAAE;YAAE,CAAE;YAAAZ,QAAA,EAAC;UAAc;YAAA9N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChDjC,OAAA;YAAGqL,KAAK,EAAE;cAAEoF,OAAO,EAAE,GAAG;cAAEC,YAAY,EAAE;YAAO,CAAE;YAAAd,QAAA,EAAC;UAElD;YAAA9N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJjC,OAAA;YAAKqL,KAAK,EAAE;cACVuF,OAAO,EAAE,MAAM;cACfC,mBAAmB,EAAE,uCAAuC;cAC5DC,GAAG,EAAE;YACP,CAAE;YAAAlB,QAAA,EACCpJ,WAAW,CAAC7E,GAAG,CAAC,CAACoP,IAAI,EAAEC,KAAK,kBAC3BhR,OAAA;cAEEqL,KAAK,EAAE4J,QAAQ,CAAC,UAAU,CAAE;cAC5B5D,OAAO,EAAEA,CAAA,KAAMpF,YAAY,CAAC8E,IAAI,CAACrK,IAAI,CAAE;cAAAkJ,QAAA,gBAEvC5P,OAAA;gBAAA4P,QAAA,gBACE5P,OAAA;kBAAIqL,KAAK,EAAE;oBAAE+E,MAAM,EAAE;kBAAY,CAAE;kBAAAR,QAAA,EAAEmB,IAAI,CAAC3O;gBAAK;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrDjC,OAAA;kBAAGqL,KAAK,EAAE;oBACR+E,MAAM,EAAE,CAAC;oBACTkB,QAAQ,EAAE,MAAM;oBAChBb,OAAO,EAAE;kBACX,CAAE;kBAAAb,QAAA,EACCmB,IAAI,CAACtK;gBAAW;kBAAA3E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNjC,OAAA;gBAAKqL,KAAK,EAAE;kBAAEC,KAAK,EAAE;gBAAU,CAAE;gBAAAsE,QAAA,eAC/B5P,OAAA,CAACN,cAAc;kBAAC8R,IAAI,EAAE;gBAAG;kBAAA1P,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA,GAhBD+O,KAAK;cAAAlP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiBP,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA4B,SAAS,KAAK,QAAQ,iBACrB7D,OAAA,CAACpC,QAAQ;QAACuS,QAAQ,eAAEnQ,OAAA;UAAK0P,SAAS,EAAC,iBAAiB;UAACrE,KAAK,EAAE;YAAE+E,MAAM,EAAE;UAAY;QAAE;UAAAtO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAA2N,QAAA,eACtF5P,OAAA,CAACQ,UAAU;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACX,EAEA4B,SAAS,KAAK,WAAW,iBACxB7D,OAAA;QAAKqL,KAAK,EAAE;UAAEgF,OAAO,EAAE;QAAO,CAAE;QAAAT,QAAA,eAC9B5P,OAAA;UAAKqL,KAAK,EAAE4J,QAAQ,CAAC,MAAM,CAAE;UAAArF,QAAA,gBAC3B5P,OAAA;YAAIqL,KAAK,EAAE;cACTmF,SAAS,EAAE,CAAC;cACZlF,KAAK,EAAE;YACT,CAAE;YAAAsE,QAAA,EAAC;UAAS;YAAA9N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjBjC,OAAA;YAAGqL,KAAK,EAAE;cACRoF,OAAO,EAAE,GAAG;cACZC,YAAY,EAAE,MAAM;cACpBpF,KAAK,EAAE;YACT,CAAE;YAAAsE,QAAA,EAAC;UAEH;YAAA9N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJjC,OAAA;YAAKqL,KAAK,EAAE;cAAEqF,YAAY,EAAE;YAAO,CAAE;YAAAd,QAAA,eACnC5P,OAAA;cAAOqL,KAAK,EAAE;gBACZ,GAAG4J,QAAQ,CAAC,eAAe,CAAC;gBAC5BtF,UAAU,EAAE,SAAS;gBACrBrE,KAAK,EAAE,MAAM;gBACbiF,MAAM,EAAE,gBAAgB;gBACxBa,MAAM,EAAEzM,qBAAqB,GAAG,aAAa,GAAG;cAClD,CAAE;cAAAiL,QAAA,gBACA5P,OAAA,CAACX,QAAQ;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACX0C,qBAAqB,GAAG,cAAc,GAAG,iBAAiB,eAC3D3E,OAAA;gBACE6G,IAAI,EAAC,MAAM;gBACX8N,MAAM,EAAC,sBAAsB;gBAC7BC,QAAQ,EAAEzK,oBAAqB;gBAC/B0K,QAAQ,EAAElQ,qBAAsB;gBAChC0G,KAAK,EAAE;kBAAEuF,OAAO,EAAE;gBAAO;cAAE;gBAAA9O,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENjC,OAAA;YAAA4P,QAAA,gBACE5P,OAAA;cAAIqL,KAAK,EAAE;gBACTqF,YAAY,EAAE,MAAM;gBACpBpF,KAAK,EAAE;cACT,CAAE;cAAAsE,QAAA,EAAC;YAAc;cAAA9N,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACrB4C,aAAa,CAAC+J,MAAM,KAAK,CAAC,gBACzB5O,OAAA;cAAGqL,KAAK,EAAE;gBACRoF,OAAO,EAAE,GAAG;gBACZnF,KAAK,EAAE;cACT,CAAE;cAAAsE,QAAA,EAAC;YAAyB;cAAA9N,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,gBAEhCjC,OAAA;cAAKqL,KAAK,EAAE;gBACVwD,eAAe,EAAE,SAAS;gBAC1B0B,MAAM,EAAE,mBAAmB;gBAC3BU,YAAY,EAAE,KAAK;gBACnBZ,OAAO,EAAE;cACX,CAAE;cAAAT,QAAA,EACC/K,aAAa,CAAClD,GAAG,CAAC,CAAC2H,IAAI,EAAE4L,GAAG,KAAK;gBAChC,MAAM;kBAAE5M,IAAI,EAAE0B;gBAAQ,CAAC,GAAGhE,QAAQ,CAAC4D,OAAO,CAACC,IAAI,CAAC,WAAW,CAAC,CAACI,YAAY,CAAC,GAAG9F,IAAI,CAACuF,EAAE,IAAIJ,IAAI,CAACK,IAAI,EAAE,CAAC;gBACpG,oBACE3J,OAAA;kBAAeqL,KAAK,EAAE;oBACpBgF,OAAO,EAAE,MAAM;oBACfsF,YAAY,EAAE,gBAAgB;oBAC9B/E,OAAO,EAAE,MAAM;oBACfM,cAAc,EAAE,eAAe;oBAC/BC,UAAU,EAAE;kBACd,CAAE;kBAAAvB,QAAA,gBACA5P,OAAA;oBAAMqL,KAAK,EAAE;sBACXC,KAAK,EAAE;oBACT,CAAE;oBAAAsE,QAAA,EAAEtG,IAAI,CAACK;kBAAI;oBAAA7H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACrBjC,OAAA;oBACE8U,IAAI,EAAE9K,OAAO,CAACE,SAAU;oBACxBX,MAAM,EAAC,QAAQ;oBACfwL,GAAG,EAAC,qBAAqB;oBACzB1J,KAAK,EAAE;sBACLC,KAAK,EAAE,SAAS;sBAChB0J,cAAc,EAAE,MAAM;sBACtBpE,OAAO,EAAE,MAAM;sBACfO,UAAU,EAAE,QAAQ;sBACpBL,GAAG,EAAE;oBACP,CAAE;oBAAAlB,QAAA,gBAEF5P,OAAA,CAACN,cAAc;sBAAC8R,IAAI,EAAE;oBAAG;sBAAA1P,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,QAE9B;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA,GAxBIiT,GAAG;kBAAApT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyBR,CAAC;cAEV,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EACA4B,SAAS,KAAK,WAAW,iBACxB7D,OAAA,CAACpC,QAAQ;QAACuS,QAAQ,eAAEnQ,OAAA;UAAK0P,SAAS,EAAC,iBAAiB;UAACrE,KAAK,EAAE;YAAE+E,MAAM,EAAE;UAAY;QAAE;UAAAtO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAA2N,QAAA,eACtF5P,OAAA,CAACK,SAAS;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACX,EAEA4B,SAAS,KAAK,KAAK,iBAClB7D,OAAA,CAACpC,QAAQ;QAACuS,QAAQ,eAAEnQ,OAAA;UAAK0P,SAAS,EAAC,iBAAiB;UAACrE,KAAK,EAAE;YAAE+E,MAAM,EAAE;UAAY;QAAE;UAAAtO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAA2N,QAAA,eACtF5P,OAAA,CAACC,OAAO;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACX,EACA4B,SAAS,KAAK,OAAO,IAAI,CAAAM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiD,KAAK,MAAKhB,WAAW,iBACnDpG,OAAA;QAAKqL,KAAK,EAAE;UAAEgF,OAAO,EAAE;QAAO,CAAE;QAAAT,QAAA,eAC9B5P,OAAA;UAAKqL,KAAK,EAAE4J,QAAQ,CAAC,MAAM,CAAE;UAAArF,QAAA,gBAC3B5P,OAAA;YAAIqL,KAAK,EAAE;cACTmF,SAAS,EAAE,CAAC;cACZlF,KAAK,EAAE;YACT,CAAE;YAAAsE,QAAA,EAAC;UAAW;YAAA9N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnBjC,OAAA;YAAKqL,KAAK,EAAE;cACVuF,OAAO,EAAE,MAAM;cACfE,GAAG,EAAE,MAAM;cACXJ,YAAY,EAAE;YAChB,CAAE;YAAAd,QAAA,gBACA5P,OAAA;cACEqL,KAAK,EAAE;gBACL,GAAG4J,QAAQ,CAAC,eAAe,CAAC;gBAC5BtF,UAAU,EAAE5K,QAAQ,KAAK,OAAO,GAC9BkP,YAAY,CAACC,YAAY,CAAC3C,OAAO,GAAG,aAAa;gBACnDjG,KAAK,EAAEvG,QAAQ,KAAK,OAAO,GACzB,OAAO,GAAG,MAAM;gBAClBwL,MAAM,EAAE;cACV,CAAE;cACFc,OAAO,EAAEA,CAAA,KAAMrM,WAAW,CAAC,OAAO,CAAE;cAAA4K,QAAA,EACrC;YAED;cAAA9N,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjC,OAAA;cACEqL,KAAK,EAAE;gBACL,GAAG4J,QAAQ,CAAC,eAAe,CAAC;gBAC5BtF,UAAU,EAAE5K,QAAQ,KAAK,WAAW,GAClCkP,YAAY,CAACC,YAAY,CAAC3C,OAAO,GAAG,aAAa;gBACnDjG,KAAK,EAAEvG,QAAQ,KAAK,WAAW,GAC7B,OAAO,GAAG,MAAM;gBAClBwL,MAAM,EAAE;cACV,CAAE;cACFc,OAAO,EAAEA,CAAA,KAAMrM,WAAW,CAAC,WAAW,CAAE;cAAA4K,QAAA,EACzC;YAED;cAAA9N,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAEL8C,QAAQ,KAAK,OAAO,iBACnB/E,OAAA;YAAA4P,QAAA,gBACE5P,OAAA;cAAIqL,KAAK,EAAE;gBACTqF,YAAY,EAAE,MAAM;gBACpBpF,KAAK,EAAE;cACT,CAAE;cAAAsE,QAAA,EAAC;YAAS;cAAA9N,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjBjC,OAAA;cAAKqL,KAAK,EAAE;gBACVwD,eAAe,EAAE,SAAS;gBAC1B0B,MAAM,EAAE,mBAAmB;gBAC3BU,YAAY,EAAE,KAAK;gBACnBZ,OAAO,EAAE;cACX,CAAE;cAAAT,QAAA,EACC9K,QAAQ,CAACnD,GAAG,CAAC,CAACwC,IAAI,EAAE+Q,GAAG,kBACtBlV,OAAA;gBAAeqL,KAAK,EAAE;kBACpBgF,OAAO,EAAE,MAAM;kBACfsF,YAAY,EAAE,gBAAgB;kBAC9BrK,KAAK,EAAE;gBACT,CAAE;gBAAAsE,QAAA,EACCzL,IAAI,CAACiD;cAAK,GALH8N,GAAG;gBAAApT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMR,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEA8C,QAAQ,KAAK,WAAW,iBACvB/E,OAAA;YAAA4P,QAAA,gBACE5P,OAAA;cAAIqL,KAAK,EAAE;gBACTqF,YAAY,EAAE,MAAM;gBACpBpF,KAAK,EAAE;cACT,CAAE;cAAAsE,QAAA,EAAC;YAAa;cAAA9N,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrBjC,OAAA;cAAGqL,KAAK,EAAE;gBACRoF,OAAO,EAAE,GAAG;gBACZnF,KAAK,EAAE;cACT,CAAE;cAAAsE,QAAA,EAAC;YAA+B;cAAA9N,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGPjC,OAAA,CAACzB,YAAY;MACX0G,YAAY,EAAEA,YAAa;MAC3B+K,OAAO,EAAEA,CAAA,KAAM9K,eAAe,CAAC,IAAI;IAAE;MAAApD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACM,EAAA,CAt3DID,YAAY;EAAA,QAE4B3D,qBAAqB,EAC5CH,aAAa,EAC0CC,UAAU,EAC5DC,qBAAqB;AAAA;AAAA+X,GAAA,GAL3CnU,YAAY;AAu3DlB,eAAeA,YAAY;AAAC,IAAAnC,EAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAU,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAgV,GAAA;AAAAC,YAAA,CAAAvW,EAAA;AAAAuW,YAAA,CAAAtW,GAAA;AAAAsW,YAAA,CAAApW,GAAA;AAAAoW,YAAA,CAAAnW,GAAA;AAAAmW,YAAA,CAAAjW,GAAA;AAAAiW,YAAA,CAAAhW,GAAA;AAAAgW,YAAA,CAAA9V,GAAA;AAAA8V,YAAA,CAAApV,GAAA;AAAAoV,YAAA,CAAAlV,GAAA;AAAAkV,YAAA,CAAAjV,GAAA;AAAAiV,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}