{"ast": null, "code": "var _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$(),\n  _s5 = $RefreshSig$(),\n  _s6 = $RefreshSig$();\nimport { useState, useEffect, useCallback } from 'react';\n\n// Breakpoints for responsive design\nconst BREAKPOINTS = {\n  mobile: 480,\n  tablet: 768,\n  desktop: 1024,\n  large: 1200\n};\n\n// Custom hook for responsive behavior\nexport const useResponsive = () => {\n  _s();\n  const [screenSize, setScreenSize] = useState({\n    width: typeof window !== 'undefined' ? window.innerWidth : 1200,\n    height: typeof window !== 'undefined' ? window.innerHeight : 800\n  });\n  const [isMobile, setIsMobile] = useState(false);\n  const [isTablet, setIsTablet] = useState(false);\n  const [isDesktop, setIsDesktop] = useState(true);\n\n  // Throttled resize handler for better performance\n  const handleResize = useCallback(() => {\n    const width = window.innerWidth;\n    const height = window.innerHeight;\n    setScreenSize({\n      width,\n      height\n    });\n    setIsMobile(width <= BREAKPOINTS.mobile);\n    setIsTablet(width > BREAKPOINTS.mobile && width <= BREAKPOINTS.tablet);\n    setIsDesktop(width > BREAKPOINTS.tablet);\n  }, []);\n  useEffect(() => {\n    // Initial check\n    handleResize();\n\n    // Throttled event listener\n    let timeoutId = null;\n    const throttledResize = () => {\n      if (timeoutId) clearTimeout(timeoutId);\n      timeoutId = setTimeout(handleResize, 100);\n    };\n    window.addEventListener('resize', throttledResize);\n    return () => {\n      window.removeEventListener('resize', throttledResize);\n      if (timeoutId) clearTimeout(timeoutId);\n    };\n  }, [handleResize]);\n  return {\n    screenSize,\n    isMobile,\n    isTablet,\n    isDesktop,\n    breakpoints: BREAKPOINTS\n  };\n};\n\n// Hook for managing sidebar state with responsive behavior\n_s(useResponsive, \"cvmYYAvtJ213g1QPpaOG5ocVH4Y=\");\nexport const useSidebar = () => {\n  _s2();\n  const {\n    isMobile\n  } = useResponsive();\n  const [isOpen, setIsOpen] = useState(false);\n\n  // Auto-close sidebar on mobile when screen size changes\n  useEffect(() => {\n    if (isMobile && isOpen) {\n      setIsOpen(false);\n    }\n  }, [isMobile, isOpen]);\n  const toggle = useCallback(() => {\n    setIsOpen(prev => !prev);\n  }, []);\n  const close = useCallback(() => {\n    setIsOpen(false);\n  }, []);\n  const open = useCallback(() => {\n    setIsOpen(true);\n  }, []);\n  return {\n    isOpen,\n    toggle,\n    close,\n    open,\n    isMobile\n  };\n};\n\n// Hook for optimized animations\n_s2(useSidebar, \"vI4YilAhBRZx6RjDAMjFpEMf9SA=\", false, function () {\n  return [useResponsive];\n});\nexport const useOptimizedAnimation = (enabled = true) => {\n  _s3();\n  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);\n  useEffect(() => {\n    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');\n    setPrefersReducedMotion(mediaQuery.matches);\n    const handleChange = e => {\n      setPrefersReducedMotion(e.matches);\n    };\n    mediaQuery.addEventListener('change', handleChange);\n    return () => mediaQuery.removeEventListener('change', handleChange);\n  }, []);\n  const shouldAnimate = enabled && !prefersReducedMotion;\n  return {\n    shouldAnimate,\n    prefersReducedMotion,\n    animationClass: shouldAnimate ? 'smooth-transition' : '',\n    fastAnimationClass: shouldAnimate ? 'fast-transition' : ''\n  };\n};\n\n// Hook for performance monitoring\n_s3(useOptimizedAnimation, \"c2o+PeDo1dLruq/wbnW+Z6a6rIY=\");\nexport const usePerformanceMonitor = () => {\n  _s4();\n  const [metrics, setMetrics] = useState({\n    renderTime: 0,\n    componentCount: 0,\n    memoryUsage: 0\n  });\n  const startRender = useCallback(() => {\n    return performance.now();\n  }, []);\n  const endRender = useCallback(startTime => {\n    const renderTime = performance.now() - startTime;\n    setMetrics(prev => ({\n      ...prev,\n      renderTime: Math.round(renderTime * 100) / 100\n    }));\n  }, []);\n  const updateComponentCount = useCallback(count => {\n    setMetrics(prev => ({\n      ...prev,\n      componentCount: count\n    }));\n  }, []);\n  const getMemoryUsage = useCallback(() => {\n    if (performance.memory) {\n      const usage = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);\n      setMetrics(prev => ({\n        ...prev,\n        memoryUsage: usage\n      }));\n      return usage;\n    }\n    return 0;\n  }, []);\n  return {\n    metrics,\n    startRender,\n    endRender,\n    updateComponentCount,\n    getMemoryUsage\n  };\n};\n\n// Hook for debounced values (useful for search inputs)\n_s4(usePerformanceMonitor, \"O8xhT4LnUvqwcCy8tH3UdkAR/LE=\");\nexport const useDebounce = (value, delay) => {\n  _s5();\n  const [debouncedValue, setDebouncedValue] = useState(value);\n  useEffect(() => {\n    const handler = setTimeout(() => {\n      setDebouncedValue(value);\n    }, delay);\n    return () => {\n      clearTimeout(handler);\n    };\n  }, [value, delay]);\n  return debouncedValue;\n};\n\n// Hook for intersection observer (lazy loading)\n_s5(useDebounce, \"KDuPAtDOgxm8PU6legVJOb3oOmA=\");\nexport const useIntersectionObserver = (options = {}) => {\n  _s6();\n  const [isIntersecting, setIsIntersecting] = useState(false);\n  const [ref, setRef] = useState(null);\n  useEffect(() => {\n    if (!ref) return;\n    const observer = new IntersectionObserver(([entry]) => {\n      setIsIntersecting(entry.isIntersecting);\n    }, {\n      threshold: 0.1,\n      rootMargin: '50px',\n      ...options\n    });\n    observer.observe(ref);\n    return () => {\n      observer.disconnect();\n    };\n  }, [ref, options]);\n  return [setRef, isIntersecting];\n};\n_s6(useIntersectionObserver, \"T7uRkYwRm3xsuwoEz9f/kMCYTu8=\");\nexport default {\n  useResponsive,\n  useSidebar,\n  useOptimizedAnimation,\n  usePerformanceMonitor,\n  useDebounce,\n  useIntersectionObserver\n};", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "BREAKPOINTS", "mobile", "tablet", "desktop", "large", "useResponsive", "_s", "screenSize", "setScreenSize", "width", "window", "innerWidth", "height", "innerHeight", "isMobile", "setIsMobile", "isTablet", "setIsTablet", "isDesktop", "setIsDesktop", "handleResize", "timeoutId", "throttledResize", "clearTimeout", "setTimeout", "addEventListener", "removeEventListener", "breakpoints", "useSidebar", "_s2", "isOpen", "setIsOpen", "toggle", "prev", "close", "open", "useOptimizedAnimation", "enabled", "_s3", "prefersReducedMotion", "setPrefersReducedMotion", "mediaQuery", "matchMedia", "matches", "handleChange", "e", "shouldAnimate", "animationClass", "fastAnimationClass", "usePerformanceMonitor", "_s4", "metrics", "setMetrics", "renderTime", "componentCount", "memoryUsage", "startRender", "performance", "now", "endRender", "startTime", "Math", "round", "updateComponentCount", "count", "getMemoryUsage", "memory", "usage", "usedJSHeapSize", "useDebounce", "value", "delay", "_s5", "debounced<PERSON><PERSON><PERSON>", "setDebouncedValue", "handler", "useIntersectionObserver", "options", "_s6", "isIntersecting", "setIsIntersecting", "ref", "setRef", "observer", "IntersectionObserver", "entry", "threshold", "rootMargin", "observe", "disconnect"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/src/hooks/useResponsive.js"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\n\n// Breakpoints for responsive design\nconst BREAKPOINTS = {\n  mobile: 480,\n  tablet: 768,\n  desktop: 1024,\n  large: 1200\n};\n\n// Custom hook for responsive behavior\nexport const useResponsive = () => {\n  const [screenSize, setScreenSize] = useState({\n    width: typeof window !== 'undefined' ? window.innerWidth : 1200,\n    height: typeof window !== 'undefined' ? window.innerHeight : 800\n  });\n\n  const [isMobile, setIsMobile] = useState(false);\n  const [isTablet, setIsTablet] = useState(false);\n  const [isDesktop, setIsDesktop] = useState(true);\n\n  // Throttled resize handler for better performance\n  const handleResize = useCallback(() => {\n    const width = window.innerWidth;\n    const height = window.innerHeight;\n    \n    setScreenSize({ width, height });\n    setIsMobile(width <= BREAKPOINTS.mobile);\n    setIsTablet(width > BREAKPOINTS.mobile && width <= BREAKPOINTS.tablet);\n    setIsDesktop(width > BREAKPOINTS.tablet);\n  }, []);\n\n  useEffect(() => {\n    // Initial check\n    handleResize();\n\n    // Throttled event listener\n    let timeoutId = null;\n    const throttledResize = () => {\n      if (timeoutId) clearTimeout(timeoutId);\n      timeoutId = setTimeout(handleResize, 100);\n    };\n\n    window.addEventListener('resize', throttledResize);\n    return () => {\n      window.removeEventListener('resize', throttledResize);\n      if (timeoutId) clearTimeout(timeoutId);\n    };\n  }, [handleResize]);\n\n  return {\n    screenSize,\n    isMobile,\n    isTablet,\n    isDesktop,\n    breakpoints: BREAKPOINTS\n  };\n};\n\n// Hook for managing sidebar state with responsive behavior\nexport const useSidebar = () => {\n  const { isMobile } = useResponsive();\n  const [isOpen, setIsOpen] = useState(false);\n\n  // Auto-close sidebar on mobile when screen size changes\n  useEffect(() => {\n    if (isMobile && isOpen) {\n      setIsOpen(false);\n    }\n  }, [isMobile, isOpen]);\n\n  const toggle = useCallback(() => {\n    setIsOpen(prev => !prev);\n  }, []);\n\n  const close = useCallback(() => {\n    setIsOpen(false);\n  }, []);\n\n  const open = useCallback(() => {\n    setIsOpen(true);\n  }, []);\n\n  return {\n    isOpen,\n    toggle,\n    close,\n    open,\n    isMobile\n  };\n};\n\n// Hook for optimized animations\nexport const useOptimizedAnimation = (enabled = true) => {\n  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);\n\n  useEffect(() => {\n    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');\n    setPrefersReducedMotion(mediaQuery.matches);\n\n    const handleChange = (e) => {\n      setPrefersReducedMotion(e.matches);\n    };\n\n    mediaQuery.addEventListener('change', handleChange);\n    return () => mediaQuery.removeEventListener('change', handleChange);\n  }, []);\n\n  const shouldAnimate = enabled && !prefersReducedMotion;\n\n  return {\n    shouldAnimate,\n    prefersReducedMotion,\n    animationClass: shouldAnimate ? 'smooth-transition' : '',\n    fastAnimationClass: shouldAnimate ? 'fast-transition' : ''\n  };\n};\n\n// Hook for performance monitoring\nexport const usePerformanceMonitor = () => {\n  const [metrics, setMetrics] = useState({\n    renderTime: 0,\n    componentCount: 0,\n    memoryUsage: 0\n  });\n\n  const startRender = useCallback(() => {\n    return performance.now();\n  }, []);\n\n  const endRender = useCallback((startTime) => {\n    const renderTime = performance.now() - startTime;\n    setMetrics(prev => ({\n      ...prev,\n      renderTime: Math.round(renderTime * 100) / 100\n    }));\n  }, []);\n\n  const updateComponentCount = useCallback((count) => {\n    setMetrics(prev => ({\n      ...prev,\n      componentCount: count\n    }));\n  }, []);\n\n  const getMemoryUsage = useCallback(() => {\n    if (performance.memory) {\n      const usage = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);\n      setMetrics(prev => ({\n        ...prev,\n        memoryUsage: usage\n      }));\n      return usage;\n    }\n    return 0;\n  }, []);\n\n  return {\n    metrics,\n    startRender,\n    endRender,\n    updateComponentCount,\n    getMemoryUsage\n  };\n};\n\n// Hook for debounced values (useful for search inputs)\nexport const useDebounce = (value, delay) => {\n  const [debouncedValue, setDebouncedValue] = useState(value);\n\n  useEffect(() => {\n    const handler = setTimeout(() => {\n      setDebouncedValue(value);\n    }, delay);\n\n    return () => {\n      clearTimeout(handler);\n    };\n  }, [value, delay]);\n\n  return debouncedValue;\n};\n\n// Hook for intersection observer (lazy loading)\nexport const useIntersectionObserver = (options = {}) => {\n  const [isIntersecting, setIsIntersecting] = useState(false);\n  const [ref, setRef] = useState(null);\n\n  useEffect(() => {\n    if (!ref) return;\n\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        setIsIntersecting(entry.isIntersecting);\n      },\n      {\n        threshold: 0.1,\n        rootMargin: '50px',\n        ...options\n      }\n    );\n\n    observer.observe(ref);\n\n    return () => {\n      observer.disconnect();\n    };\n  }, [ref, options]);\n\n  return [setRef, isIntersecting];\n};\n\nexport default {\n  useResponsive,\n  useSidebar,\n  useOptimizedAnimation,\n  usePerformanceMonitor,\n  useDebounce,\n  useIntersectionObserver\n};\n"], "mappings": ";;;;;;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;;AAExD;AACA,MAAMC,WAAW,GAAG;EAClBC,MAAM,EAAE,GAAG;EACXC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,IAAI;EACbC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,OAAO,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGX,QAAQ,CAAC;IAC3CY,KAAK,EAAE,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,CAACC,UAAU,GAAG,IAAI;IAC/DC,MAAM,EAAE,OAAOF,MAAM,KAAK,WAAW,GAAGA,MAAM,CAACG,WAAW,GAAG;EAC/D,CAAC,CAAC;EAEF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACqB,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACA,MAAMuB,YAAY,GAAGrB,WAAW,CAAC,MAAM;IACrC,MAAMU,KAAK,GAAGC,MAAM,CAACC,UAAU;IAC/B,MAAMC,MAAM,GAAGF,MAAM,CAACG,WAAW;IAEjCL,aAAa,CAAC;MAAEC,KAAK;MAAEG;IAAO,CAAC,CAAC;IAChCG,WAAW,CAACN,KAAK,IAAIT,WAAW,CAACC,MAAM,CAAC;IACxCgB,WAAW,CAACR,KAAK,GAAGT,WAAW,CAACC,MAAM,IAAIQ,KAAK,IAAIT,WAAW,CAACE,MAAM,CAAC;IACtEiB,YAAY,CAACV,KAAK,GAAGT,WAAW,CAACE,MAAM,CAAC;EAC1C,CAAC,EAAE,EAAE,CAAC;EAENJ,SAAS,CAAC,MAAM;IACd;IACAsB,YAAY,CAAC,CAAC;;IAEd;IACA,IAAIC,SAAS,GAAG,IAAI;IACpB,MAAMC,eAAe,GAAGA,CAAA,KAAM;MAC5B,IAAID,SAAS,EAAEE,YAAY,CAACF,SAAS,CAAC;MACtCA,SAAS,GAAGG,UAAU,CAACJ,YAAY,EAAE,GAAG,CAAC;IAC3C,CAAC;IAEDV,MAAM,CAACe,gBAAgB,CAAC,QAAQ,EAAEH,eAAe,CAAC;IAClD,OAAO,MAAM;MACXZ,MAAM,CAACgB,mBAAmB,CAAC,QAAQ,EAAEJ,eAAe,CAAC;MACrD,IAAID,SAAS,EAAEE,YAAY,CAACF,SAAS,CAAC;IACxC,CAAC;EACH,CAAC,EAAE,CAACD,YAAY,CAAC,CAAC;EAElB,OAAO;IACLb,UAAU;IACVO,QAAQ;IACRE,QAAQ;IACRE,SAAS;IACTS,WAAW,EAAE3B;EACf,CAAC;AACH,CAAC;;AAED;AAAAM,EAAA,CAhDaD,aAAa;AAiD1B,OAAO,MAAMuB,UAAU,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC9B,MAAM;IAAEf;EAAS,CAAC,GAAGT,aAAa,CAAC,CAAC;EACpC,MAAM,CAACyB,MAAM,EAAEC,SAAS,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;;EAE3C;EACAC,SAAS,CAAC,MAAM;IACd,IAAIgB,QAAQ,IAAIgB,MAAM,EAAE;MACtBC,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC,EAAE,CAACjB,QAAQ,EAAEgB,MAAM,CAAC,CAAC;EAEtB,MAAME,MAAM,GAAGjC,WAAW,CAAC,MAAM;IAC/BgC,SAAS,CAACE,IAAI,IAAI,CAACA,IAAI,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,KAAK,GAAGnC,WAAW,CAAC,MAAM;IAC9BgC,SAAS,CAAC,KAAK,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,IAAI,GAAGpC,WAAW,CAAC,MAAM;IAC7BgC,SAAS,CAAC,IAAI,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IACLD,MAAM;IACNE,MAAM;IACNE,KAAK;IACLC,IAAI;IACJrB;EACF,CAAC;AACH,CAAC;;AAED;AAAAe,GAAA,CAhCaD,UAAU;EAAA,QACAvB,aAAa;AAAA;AAgCpC,OAAO,MAAM+B,qBAAqB,GAAGA,CAACC,OAAO,GAAG,IAAI,KAAK;EAAAC,GAAA;EACvD,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAEvEC,SAAS,CAAC,MAAM;IACd,MAAM2C,UAAU,GAAG/B,MAAM,CAACgC,UAAU,CAAC,kCAAkC,CAAC;IACxEF,uBAAuB,CAACC,UAAU,CAACE,OAAO,CAAC;IAE3C,MAAMC,YAAY,GAAIC,CAAC,IAAK;MAC1BL,uBAAuB,CAACK,CAAC,CAACF,OAAO,CAAC;IACpC,CAAC;IAEDF,UAAU,CAAChB,gBAAgB,CAAC,QAAQ,EAAEmB,YAAY,CAAC;IACnD,OAAO,MAAMH,UAAU,CAACf,mBAAmB,CAAC,QAAQ,EAAEkB,YAAY,CAAC;EACrE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,aAAa,GAAGT,OAAO,IAAI,CAACE,oBAAoB;EAEtD,OAAO;IACLO,aAAa;IACbP,oBAAoB;IACpBQ,cAAc,EAAED,aAAa,GAAG,mBAAmB,GAAG,EAAE;IACxDE,kBAAkB,EAAEF,aAAa,GAAG,iBAAiB,GAAG;EAC1D,CAAC;AACH,CAAC;;AAED;AAAAR,GAAA,CAzBaF,qBAAqB;AA0BlC,OAAO,MAAMa,qBAAqB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACzC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGvD,QAAQ,CAAC;IACrCwD,UAAU,EAAE,CAAC;IACbC,cAAc,EAAE,CAAC;IACjBC,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAGzD,WAAW,CAAC,MAAM;IACpC,OAAO0D,WAAW,CAACC,GAAG,CAAC,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,SAAS,GAAG5D,WAAW,CAAE6D,SAAS,IAAK;IAC3C,MAAMP,UAAU,GAAGI,WAAW,CAACC,GAAG,CAAC,CAAC,GAAGE,SAAS;IAChDR,UAAU,CAACnB,IAAI,KAAK;MAClB,GAAGA,IAAI;MACPoB,UAAU,EAAEQ,IAAI,CAACC,KAAK,CAACT,UAAU,GAAG,GAAG,CAAC,GAAG;IAC7C,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,oBAAoB,GAAGhE,WAAW,CAAEiE,KAAK,IAAK;IAClDZ,UAAU,CAACnB,IAAI,KAAK;MAClB,GAAGA,IAAI;MACPqB,cAAc,EAAEU;IAClB,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,cAAc,GAAGlE,WAAW,CAAC,MAAM;IACvC,IAAI0D,WAAW,CAACS,MAAM,EAAE;MACtB,MAAMC,KAAK,GAAGN,IAAI,CAACC,KAAK,CAACL,WAAW,CAACS,MAAM,CAACE,cAAc,GAAG,IAAI,GAAG,IAAI,CAAC;MACzEhB,UAAU,CAACnB,IAAI,KAAK;QAClB,GAAGA,IAAI;QACPsB,WAAW,EAAEY;MACf,CAAC,CAAC,CAAC;MACH,OAAOA,KAAK;IACd;IACA,OAAO,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IACLhB,OAAO;IACPK,WAAW;IACXG,SAAS;IACTI,oBAAoB;IACpBE;EACF,CAAC;AACH,CAAC;;AAED;AAAAf,GAAA,CA/CaD,qBAAqB;AAgDlC,OAAO,MAAMoB,WAAW,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;EAAAC,GAAA;EAC3C,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG7E,QAAQ,CAACyE,KAAK,CAAC;EAE3DxE,SAAS,CAAC,MAAM;IACd,MAAM6E,OAAO,GAAGnD,UAAU,CAAC,MAAM;MAC/BkD,iBAAiB,CAACJ,KAAK,CAAC;IAC1B,CAAC,EAAEC,KAAK,CAAC;IAET,OAAO,MAAM;MACXhD,YAAY,CAACoD,OAAO,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,CAACL,KAAK,EAAEC,KAAK,CAAC,CAAC;EAElB,OAAOE,cAAc;AACvB,CAAC;;AAED;AAAAD,GAAA,CAhBaH,WAAW;AAiBxB,OAAO,MAAMO,uBAAuB,GAAGA,CAACC,OAAO,GAAG,CAAC,CAAC,KAAK;EAAAC,GAAA;EACvD,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACoF,GAAG,EAAEC,MAAM,CAAC,GAAGrF,QAAQ,CAAC,IAAI,CAAC;EAEpCC,SAAS,CAAC,MAAM;IACd,IAAI,CAACmF,GAAG,EAAE;IAEV,MAAME,QAAQ,GAAG,IAAIC,oBAAoB,CACvC,CAAC,CAACC,KAAK,CAAC,KAAK;MACXL,iBAAiB,CAACK,KAAK,CAACN,cAAc,CAAC;IACzC,CAAC,EACD;MACEO,SAAS,EAAE,GAAG;MACdC,UAAU,EAAE,MAAM;MAClB,GAAGV;IACL,CACF,CAAC;IAEDM,QAAQ,CAACK,OAAO,CAACP,GAAG,CAAC;IAErB,OAAO,MAAM;MACXE,QAAQ,CAACM,UAAU,CAAC,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,CAACR,GAAG,EAAEJ,OAAO,CAAC,CAAC;EAElB,OAAO,CAACK,MAAM,EAAEH,cAAc,CAAC;AACjC,CAAC;AAACD,GAAA,CA1BWF,uBAAuB;AA4BpC,eAAe;EACbvE,aAAa;EACbuB,UAAU;EACVQ,qBAAqB;EACrBa,qBAAqB;EACrBoB,WAAW;EACXO;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}