import React, { memo, useCallback } from 'react';
import { FiChevronDown, FiChevronRight } from 'react-icons/fi';
import '../components.css';

const Sidebar = memo(({ 
  isOpen, 
  onClose, 
  activeTab, 
  onTabChange, 
  sidebarItems, 
  expandedMenus, 
  onToggleMenu,
  theme 
}) => {
  const handleItemClick = useCallback((tab) => {
    onTabChange(tab);
    onClose();
  }, [onTabChange, onClose]);

  const handleMenuToggle = useCallback((menuTitle, e) => {
    e.stopPropagation();
    onToggleMenu(menuTitle);
  }, [onToggleMenu]);

  return (
    <>
      <aside 
        className={`sidebar-fixed ${isOpen ? 'sidebar-open' : ''}`}
        style={{
          backgroundColor: theme.surface,
          borderRight: `1px solid ${theme.border}`,
          boxShadow: `2px 0 10px ${theme.shadow}`
        }}
      >
        <div style={{ padding: '16px' }}>
          {sidebarItems.map((item, index) => (
            <div key={index}>
              <div
                className={`sidebar-item ${activeTab === item.tab ? 'sidebar-item-active' : ''}`}
                style={{
                  color: activeTab === item.tab ? 'white' : theme.text,
                  background: activeTab === item.tab ? theme.primary : theme.surface,
                  border: `1px solid ${activeTab === item.tab ? theme.primary : theme.border}`
                }}
                onClick={() => handleItemClick(item.tab)}
                onMouseEnter={(e) => {
                  if (activeTab !== item.tab) {
                    e.target.style.background = 'rgba(0, 0, 0, 0.05)';
                    e.target.style.borderLeft = `3px solid ${theme.primary}`;
                  }
                }}
                onMouseLeave={(e) => {
                  if (activeTab !== item.tab) {
                    e.target.style.background = theme.surface;
                    e.target.style.borderLeft = '3px solid transparent';
                  }
                }}
              >
                <div style={{ marginRight: '12px', fontSize: '18px' }}>
                  {item.icon}
                </div>
                <span style={{ flex: 1, fontWeight: 500 }}>
                  {item.title}
                </span>
                {item.subItems && item.subItems.length > 0 && (
                  <div 
                    onClick={(e) => handleMenuToggle(item.title, e)}
                    style={{
                      padding: '4px',
                      borderRadius: '4px',
                      transition: 'all 0.2s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.target.style.background = 'rgba(0, 0, 0, 0.1)';
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.background = 'transparent';
                    }}
                  >
                    {expandedMenus[item.title] ? (
                      <FiChevronDown size={16} />
                    ) : (
                      <FiChevronRight size={16} />
                    )}
                  </div>
                )}
              </div>

              {item.subItems && item.subItems.length > 0 && expandedMenus[item.title] && (
                <div className="sidebar-submenu" style={{ marginLeft: '32px' }}>
                  {item.subItems.map((subItem, subIndex) => (
                    <div
                      key={subIndex}
                      className="sidebar-item"
                      style={{
                        padding: '8px 16px 8px 32px',
                        fontSize: '14px',
                        opacity: 0.9,
                        color: theme.textLight,
                        background: theme.surface
                      }}
                      onClick={() => handleItemClick(item.tab)}
                      onMouseEnter={(e) => {
                        e.target.style.background = 'rgba(0, 0, 0, 0.03)';
                        e.target.style.paddingLeft = '36px';
                        e.target.style.opacity = '1';
                        e.target.style.color = theme.primary;
                      }}
                      onMouseLeave={(e) => {
                        e.target.style.background = theme.surface;
                        e.target.style.paddingLeft = '32px';
                        e.target.style.opacity = '0.9';
                        e.target.style.color = theme.textLight;
                      }}
                    >
                      {subItem.title}
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Sidebar Footer */}
        <div style={{
          marginTop: 'auto',
          padding: '16px',
          borderTop: `1px solid ${theme.border}`,
          textAlign: 'center'
        }}>
          <div style={{
            fontSize: '12px',
            color: theme.textLight,
            opacity: 0.7
          }}>
            EDU NOVA v2.0
          </div>
          <div style={{
            fontSize: '10px',
            color: theme.textLight,
            opacity: 0.5,
            marginTop: '4px'
          }}>
            AI-Powered Learning
          </div>
        </div>
      </aside>

      {/* Mobile Overlay */}
      {isOpen && (
        <div 
          className="sidebar-overlay"
          onClick={onClose}
          style={{ display: window.innerWidth < 768 ? 'block' : 'none' }}
        />
      )}
    </>
  );
});

Sidebar.displayName = 'Sidebar';

export default Sidebar;
