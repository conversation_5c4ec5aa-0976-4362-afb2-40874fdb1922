/* Performance-optimized component styles */

/* Layout Components */
.app-container {
  min-height: 100vh;
  transition: all 0.3s ease;
  position: relative;
}

.navbar-fixed {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 64px;
  display: flex;
  align-items: center;
  padding: 0 24px;
  z-index: 1000;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-fixed {
  position: fixed;
  top: 64px;
  left: 0;
  bottom: 0;
  width: 280px;
  transform: translateX(-100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 900;
  overflow-y: auto;
  will-change: transform;
}

.sidebar-open {
  transform: translateX(0);
}

.sidebar-overlay {
  position: fixed;
  top: 64px;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 800;
  opacity: 0;
  animation: fadeIn 0.3s ease forwards;
}

.main-content {
  padding-top: 80px;
  padding-bottom: 40px;
  transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: calc(100vh - 120px);
  will-change: margin-left;
}

.main-content-with-sidebar {
  margin-left: 280px;
}

/* Sidebar Components */
.sidebar-item {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  cursor: pointer;
  border-radius: 8px;
  margin: 4px 8px;
  transition: all 0.2s ease;
  will-change: transform, background-color;
}

.sidebar-item:hover {
  transform: translateX(4px);
}

.sidebar-item-active {
  font-weight: 500;
}

.sidebar-submenu {
  margin-left: 32px;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

/* Button Components */
.btn-primary {
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 500;
  font-size: 16px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s ease;
  will-change: transform, box-shadow;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
}

.btn-primary:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.btn-secondary {
  background: transparent;
  border: 2px solid;
  border-radius: 8px;
  padding: 10px 22px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

/* Card Components */
.card {
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  transition: all 0.3s ease;
  will-change: transform, box-shadow;
}

.card-hover:hover {
  transform: translateY(-4px);
}

.company-card {
  border-radius: 12px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, box-shadow;
}

.company-card:hover {
  transform: translateY(-4px);
  animation: pulse 0.6s ease;
}

.quiz-card {
  border-radius: 12px;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  will-change: transform, box-shadow;
}

.quiz-card:hover {
  transform: translateY(-4px);
}

/* Input Components */
.input-field {
  width: 100%;
  padding: 14px 16px;
  border-radius: 8px;
  border: 1.5px solid;
  font-size: 16px;
  transition: all 0.3s ease;
  will-change: border-color, box-shadow;
}

.input-field:focus {
  outline: none;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 16px;
  z-index: 1;
}

.search-input {
  padding-left: 48px;
}

/* Chat Components */
.chat-container {
  height: calc(100vh - 200px);
  max-height: 800px;
  min-height: 400px;
  overflow-y: auto;
  padding: 24px;
  display: flex;
  flex-direction: column;
  scroll-behavior: smooth;
}

.chat-messages {
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
}

.chat-bubble {
  padding: 12px 16px;
  border-radius: 18px;
  max-width: 80%;
  margin-bottom: 8px;
  animation: fadeIn 0.3s ease-out;
  word-wrap: break-word;
}

.chat-bubble-user {
  margin-left: auto;
  border-bottom-right-radius: 4px;
}

.chat-bubble-bot {
  margin-right: auto;
  border-bottom-left-radius: 4px;
}

/* Dashboard Components */
.dashboard-container {
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.dashboard-hero {
  border-radius: 25px;
  padding: 48px;
  margin-bottom: 32px;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(20px);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  border-radius: 20px;
  padding: 32px;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  will-change: transform, box-shadow;
}

.stat-card:hover {
  transform: translateY(-10px) scale(1.02);
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.action-card {
  border-radius: 20px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  will-change: transform, box-shadow;
}

.action-card:hover {
  transform: translateY(-8px) scale(1.05);
}

/* Notification Component */
.notification {
  position: fixed;
  bottom: 24px;
  right: 24px;
  padding: 16px 24px;
  border-radius: 8px;
  z-index: 2000;
  animation: slideIn 0.3s ease-out;
  color: white;
  font-weight: 500;
  max-width: 400px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.notification-success {
  background: linear-gradient(135deg, #10b981, #059669);
}

.notification-error {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.notification-info {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

/* Loading Components */
.loading-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  border-radius: 50%;
  animation: rotate 1s linear infinite;
}

.loading-dots {
  display: flex;
  gap: 8px;
  align-items: center;
}

.loading-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: bounce 1.4s infinite ease-in-out;
}

.loading-dot:nth-child(1) { animation-delay: 0s; }
.loading-dot:nth-child(2) { animation-delay: 0.2s; }
.loading-dot:nth-child(3) { animation-delay: 0.4s; }

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar-fixed {
    width: 100%;
  }
  
  .main-content-with-sidebar {
    margin-left: 0;
  }
  
  .dashboard-hero {
    padding: 24px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .action-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .chat-container {
    height: calc(100vh - 160px);
    padding: 16px;
  }
  
  .notification {
    bottom: 16px;
    right: 16px;
    left: 16px;
    max-width: none;
  }
}

@media (max-width: 480px) {
  .navbar-fixed {
    padding: 0 16px;
  }
  
  .dashboard-container {
    padding: 16px;
  }
  
  .dashboard-hero {
    padding: 16px;
  }
  
  .stat-card {
    padding: 20px;
  }
  
  .action-card {
    padding: 16px;
  }
  
  .chat-container {
    padding: 12px;
  }
}
