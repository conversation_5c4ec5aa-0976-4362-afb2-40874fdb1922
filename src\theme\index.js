// Optimized theme configuration
export const lightTheme = {
  primary: '#7C3AED',
  primaryLight: '#8B5CF6',
  primaryDark: '#6D28D9',
  secondary: '#EC4899',
  accent: '#F59E0B',
  
  // Surfaces
  background: '#FFFFFF',
  surface: '#F8FAFC',
  surfaceLight: '#FFFFFF',
  surfaceDark: '#F1F5F9',
  
  // Text colors
  text: '#1E293B',
  textLight: '#64748B',
  textMuted: '#94A3B8',
  textInverse: '#FFFFFF',
  
  // Borders and dividers
  border: '#E2E8F0',
  borderLight: '#F1F5F9',
  borderDark: '#CBD5E1',
  
  // Status colors
  success: '#10B981',
  warning: '#F59E0B',
  error: '#EF4444',
  info: '#3B82F6',
  
  // Shadows
  shadow: 'rgba(0, 0, 0, 0.1)',
  shadowLight: 'rgba(0, 0, 0, 0.05)',
  shadowDark: 'rgba(0, 0, 0, 0.15)',
  
  // Gradients
  gradients: {
    primary: 'linear-gradient(135deg, #7C3AED 0%, #8B5CF6 100%)',
    secondary: 'linear-gradient(135deg, #EC4899 0%, #F472B6 100%)',
    accent: 'linear-gradient(135deg, #F59E0B 0%, #FBBF24 100%)',
    success: 'linear-gradient(135deg, #10B981 0%, #34D399 100%)',
    error: 'linear-gradient(135deg, #EF4444 0%, #F87171 100%)',
    info: 'linear-gradient(135deg, #3B82F6 0%, #60A5FA 100%)',
    glass: 'linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.1))'
  }
};

export const darkTheme = {
  primary: '#8B5CF6',
  primaryLight: '#A78BFA',
  primaryDark: '#7C3AED',
  secondary: '#F472B6',
  accent: '#FBBF24',
  
  // Surfaces
  background: '#0F172A',
  surface: '#1E293B',
  surfaceLight: '#334155',
  surfaceDark: '#0F172A',
  
  // Text colors
  text: '#F8FAFC',
  textLight: '#CBD5E1',
  textMuted: '#94A3B8',
  textInverse: '#1E293B',
  
  // Borders and dividers
  border: '#334155',
  borderLight: '#475569',
  borderDark: '#1E293B',
  
  // Status colors
  success: '#34D399',
  warning: '#FBBF24',
  error: '#F87171',
  info: '#60A5FA',
  
  // Shadows
  shadow: 'rgba(0, 0, 0, 0.3)',
  shadowLight: 'rgba(0, 0, 0, 0.2)',
  shadowDark: 'rgba(0, 0, 0, 0.4)',
  
  // Gradients
  gradients: {
    primary: 'linear-gradient(135deg, #8B5CF6 0%, #A78BFA 100%)',
    secondary: 'linear-gradient(135deg, #F472B6 0%, #FBBF24 100%)',
    accent: 'linear-gradient(135deg, #FBBF24 0%, #FCD34D 100%)',
    success: 'linear-gradient(135deg, #34D399 0%, #6EE7B7 100%)',
    error: 'linear-gradient(135deg, #F87171 0%, #FCA5A5 100%)',
    info: 'linear-gradient(135deg, #60A5FA 0%, #93C5FD 100%)',
    glass: 'linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))'
  }
};

// Responsive breakpoints
export const breakpoints = {
  mobile: '480px',
  tablet: '768px',
  desktop: '1024px',
  large: '1200px',
  xlarge: '1400px'
};

// Animation configurations
export const animations = {
  fast: '0.15s',
  normal: '0.3s',
  slow: '0.5s',
  
  easing: {
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
    smooth: 'cubic-bezier(0.4, 0, 0.2, 1)',
    bounce: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)'
  }
};

// Z-index scale
export const zIndex = {
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  modalBackdrop: 1040,
  modal: 1050,
  popover: 1060,
  tooltip: 1070,
  notification: 2000
};

// Component sizes
export const sizes = {
  navbar: {
    height: '64px'
  },
  sidebar: {
    width: '280px',
    collapsedWidth: '64px'
  },
  button: {
    small: {
      padding: '8px 16px',
      fontSize: '14px'
    },
    medium: {
      padding: '12px 24px',
      fontSize: '16px'
    },
    large: {
      padding: '16px 32px',
      fontSize: '18px'
    }
  },
  input: {
    small: {
      padding: '8px 12px',
      fontSize: '14px'
    },
    medium: {
      padding: '12px 16px',
      fontSize: '16px'
    },
    large: {
      padding: '16px 20px',
      fontSize: '18px'
    }
  }
};

// Utility functions
export const getTheme = (isDark = false) => isDark ? darkTheme : lightTheme;

export const createGlassEffect = (theme, opacity = 0.1) => ({
  background: `rgba(255, 255, 255, ${opacity})`,
  backdropFilter: 'blur(20px)',
  border: `1px solid rgba(255, 255, 255, ${opacity * 2})`,
  boxShadow: `0 8px 32px ${theme.shadow}`
});

export const createHoverEffect = (theme, scale = 1.02) => ({
  transform: `translateY(-2px) scale(${scale})`,
  boxShadow: `0 8px 25px ${theme.shadow}`
});

export const createFocusEffect = (theme) => ({
  outline: 'none',
  boxShadow: `0 0 0 3px ${theme.primary}40`
});

export default {
  lightTheme,
  darkTheme,
  breakpoints,
  animations,
  zIndex,
  sizes,
  getTheme,
  createGlassEffect,
  createHoverEffect,
  createFocusEffect
};
