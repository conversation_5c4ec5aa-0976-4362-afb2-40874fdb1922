// src/App.js

import React, { useState } from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import AuthPage from './AuthPage';
import EduAIChatBot from './EduAIChatBotOptimized';
import VerifyPage from './VerifyPage';
import './App.css';
import './components.css';

function App() {
const [isAuthenticated, setIsAuthenticated] = useState(false);

return (
<BrowserRouter>
<Routes>
<Route
path="/"
element={
!isAuthenticated ? (
<AuthPage onAuthSuccess={() => setIsAuthenticated(true)} />
) : (
<EduAIChatBot />
)
}
/>
<Route path="/verify" element={<VerifyPage />} />
<Route path="/eduai-chatbot" element={<EduAIChatBot />} />
</Routes>
</BrowserRouter>
);
}

export default App;