import React, { useState, useEffect, useRef, useCallback, useMemo, Suspense } from "react";
import { getDoc, doc } from 'firebase/firestore';
import { auth, db } from './firebaseConfig';
import axios from "axios";
import { sidebarItems } from './sidebarItems';
import { onAuthStateChanged } from 'firebase/auth';
import { Navbar, Sidebar, Dashboard, Notification } from './components';
import { useResponsive, useSidebar, useOptimizedAnimation, usePerformanceMonitor } from './hooks/useResponsive';
import { getTheme } from './theme';
import {
  FiFileText, FiCode, FiHelpCircle, FiAward, FiBook, FiShield,
  FiSearch, FiUpload, FiBriefcase, FiBarChart2, FiLayers, FiCheckCircle,
  FiExternalLink, FiHeart, FiClock, FiRefreshCw, FiX
} from "react-icons/fi";
import { createClient } from '@supabase/supabase-js';
import './App.css';
import './components.css';

// Lazy load heavy components for better performance
const LazyFaq = React.lazy(() => import('./Faq'));
const LazyExams = React.lazy(() => import('./Exams'));
const LazyCoding = React.lazy(() => import('./Coding'));
const LazyMarkdown = React.lazy(() => import('react-markdown'));

// Enhanced sidebar items with icons
const updatedSidebarItems = sidebarItems.map(item => {
  const iconMap = {
    "resume": <FiFileText />,
    "dsa": <FiCode />,
    "coding": <FiLayers />,
    "resources": <FiBriefcase />,
    "quizzes": <FiCheckCircle />,
    "aptitude": <FiBarChart2 />,
    "academics": <FiBook />,
    "faq": <FiHelpCircle />,
    "admin": <FiShield />
  };

  return {
    ...item,
    icon: iconMap[item.tab] || iconMap[item.title.toLowerCase()] || <FiAward />
  };
});

const EduAIChatBot = () => {
  // Performance monitoring
  const { metrics, startRender, endRender } = usePerformanceMonitor();
  const { isMobile } = useResponsive();
  const { isOpen: sidebarOpen, toggle: toggleSidebar, close: closeSidebar } = useSidebar();
  const { shouldAnimate } = useOptimizedAnimation();

  // Optimized state declarations with proper initial values
  const [input, setInput] = useState("");
  const [messages, setMessages] = useState([]);
  const [userId, setUserId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [knowledge, setKnowledge] = useState("");
  const [activeTab, setActiveTab] = useState("dashboard");
  const [searchTerm, setSearchTerm] = useState("");
  const [expandedMenus, setExpandedMenus] = useState({});
  const [user, setUser] = useState(null);
  const [isDarkMode, setIsDarkMode] = useState(false);

  const [resumeUploadLoading, setResumeUploadLoading] = useState(false);
  const [resumeUrl, setResumeUrl] = useState(null);
  const [resourceUploadLoading, setResourceUploadLoading] = useState(false);
  const [userResources] = useState([]);
  const [allUsers] = useState([]);
  const [adminTab, setAdminTab] = useState('users');
  const [notification, setNotification] = useState(null);
  const [activityLog, setActivityLog] = useState([]);
  const chatEndRef = useRef(null);

  // Enhanced DSA section states
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [favoriteCompanies, setFavoriteCompanies] = useState([]);
  const [recentCompanies, setRecentCompanies] = useState([]);
  const [showRevertButton, setShowRevertButton] = useState(true);

  // Memoized configurations for better performance
  const supabase = useMemo(() => {
    const SUPABASE_URL = 'https://gziaptswfepiveyylven.supabase.co';
    const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imd6aWFwdHN3ZmVwaXZleXlsdmVuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU2NzczNTYsImV4cCI6MjA2MTI1MzM1Nn0.wmqXZGffrox8E_PuCwbzh4xJEffsvFmZCVcF6WFAX6Q';
    return createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
  }, []);

  const API_KEY = "AIzaSyC6kHWto78QdqHz7Uu9RzEXb443ZO7tG5M";
  const ADMIN_EMAIL = '<EMAIL>';

  // Memoized theme
  const theme = useMemo(() => getTheme(isDarkMode), [isDarkMode]);

  // Company categories for enhanced DSA section
  const companyCategories = {
    'FAANG': ['Facebook', 'Apple', 'Amazon', 'Netflix', 'Google'],
    'Big Tech': ['Microsoft', 'Adobe', 'Salesforce', 'Oracle', 'IBM', 'Intel', 'Nvidia'],
    'Startups': ['Stripe', 'Airbnb', 'Uber', 'Lyft', 'DoorDash', 'Instacart', 'Coinbase'],
    'Finance': ['Goldman Sachs', 'JPMorgan', 'Morgan Stanley', 'BlackRock', 'Citadel', 'Two Sigma'],
    'Consulting': ['McKinsey', 'BCG', 'Bain', 'Deloitte', 'Accenture', 'PwC'],
    'E-commerce': ['Amazon', 'eBay', 'Shopify', 'Etsy', 'Wayfair', 'Booking.com'],
    'Gaming': ['Blizzard', 'Epic Games', 'Riot Games', 'Unity', 'Roblox'],
    'Indian': ['TCS', 'Infosys', 'Wipro', 'HCL', 'Flipkart', 'Paytm', 'Zomato', 'Swiggy']
  };

  // Complete list of companies
  const companies = [
     "Accenture", "Accolite", "Adobe", "Affirm", "Agoda", "Airbnb", "Airtel",
    "Akamar", "Akuna Capital", "Alibaba", "Altimetrik", "Amazon", "AMD",
    "Amdocs", "American Express", "Anduril", "Apple", "Arista Networks",
    "Arcesium", "Atlassian", "Attentive", "athenahealth", "Autodesk",
    "Avito", "Baidu", "Barclays", "BitGo", "BlackRock", "Blizzard",
    "Block", "Bloomberg", "BNY Mellon", "Boft", "Booking.com", "Bos",
    "BP", "ByteDance", "Cadence", "Capgemini", "Capital One", "CARS24",
    "carwale", "Cashfree", "Chewy", "Cisco", "Citadel", "Citrix",
    "Cloudera", "Cloudflare", "Cognizant", "Coinbase", "Commvault",
    "Confluent", "Coupang", "Coursera", "CrowdStrike", "Cruise",
    "Curefit", "Databricks", "Datadog", "DE Shaw", "Deloitte", "Dell",
    "Deliveroo", "Derantior", "Deutsche Bank", "Devflev", "Directi",
    "Disney", "Docusign", "DoorDash", "Dream11", "Dropbox", "DRW",
    "Dunzo", "eBay", "EPAM Systems", "Epic Systems", "Expedia",
    "FactSet", "Flexport", "Flipkart", "Freshworks", "GE Healthcare",
    "Geico", "Goldman Sachs", "Google", "Grab", "Grammarly", "Graviton",
    "Groww", "GSN Games", "Hashedin", "HCL", "HPE", "Hubspot",
    "Hudson River Trading", "Huawei", "IBM", "IMC", "Indeed", "Infosys",
    "InMobi", "Intel", "Intuit", "JPMorgan", "Jane Street",
    "Josh Technology", "Jump Trading", "Juspay", "Karat", "KLA",
    "LinkedIn", "LiveRamp", "Lowe's", "Lucid", "Lyft", "MakeMyTrip",
    "Mastercard", "MathWorks", "Media.net", "Meesho", "Mercari", "Meta",
    "Microsoft", "Millennium", "Mitsogo", "Moloco", "MongoDB",
    "Morgan Stanley", "Moveworks", "Myntra", "Nagarro", "NetApp",
    "Netflix", "Nextdoor", "Nielsen", "Nike", "Niantic", "Nordstrom",
    "Nutanix", "Nvidia", "Okta", "OKX", "OpenAI", "OpenText", "Oracle",
    "Otter.ai", "Oyo", "Ozon", "Palantir Technologies", "Palo Alto Networks",
    "PayPal", "Paytm", "Persistent Systems", "PhonePe", "Pinterest",
    "Pocket Gems", "Point72", "Pure Storage", "Qualcomm", "Quora",
    "Rakuten", "Razorpay", "RBC", "Reddit", "Revolut", "Robinhood",
    "Roblox", "Rubrik", "Salesforce", "Samsung", "SAP", "ServiceNow",
    "Shopify", "Siemens", "Sigmoid", "SIG", "Snowflake", "Snap", "Sofi",
    "Splunk", "Spotify", "Sprinklr", "Squarepoint Capital", "Stripe",
    "Swiggy", "TCS", "Tekion", "Tencent", "Tesla", "ThoughtSpot",
    "ThoughtWorks", "TikTok", "Tinkoff", "Trilogy", "Turing", "Turo",
    "Twilio", "Twitch", "Two Sigma", "Uber", "UiPath", "UKG",
    "Veeva Systems", "Verily", "Verkada", "Virtu Financial", "Visa",
    "VK", "VMware", "Walmart Labs", "WarnerMedia", "Wayfair",
    "Wells Fargo", "Wipro", "Wix", "Workday", "X", "Yahoo", "Yandex",
    "Yelp", "Zalando", "Zenefits", "Zepto", "Zeta", "Zillow", "Zoho",
    "Zomato", "ZScaler", "Zopsmart"
  ];

  // Quiz buttons data
  const quizButtons = [
    {
      title: "OP and CN Quiz",
      description: "Test your knowledge of Operating System and Computer Networks",
      link: "https://opcn.netlify.app",
    },
    {
      title: "OOPs and DBMS Quiz",
      description: "Challenge yourself with oops and dbms",
      link: "https://oopsanddbms.netlify.app/",
    },
    {
      title: "System Design Quiz",
      description: "Test your system design knowledge",
      link: "https://system-design041.netlify.app",
    },
    {
      title: "Quantitative Aptitude and Reasoning Quiz",
      description: "Practice common quant and reasoning questions",
      link: "https://quantandreasoning.netlify.app",
    },
    {
      title: "Cloud & DevOps Quiz",
      description: "Test your knowledge of Cloud and DevOps concepts",
      link: "https://cloud-devops.netlify.app",
    },
    {
      title: "DSA Quiz",
      description: "Data Structures and Algorithms quiz",
      link: "https://dsa041.netlify.app",
    },
    {
      title: "Operating System & Computer Networks Quiz",
      description: "Quiz on OS and Computer Networks",
      link: "https://opcn.netlify.app",
    },
     {
      title: "Web Development Quiz",
      description: "Quiz on Web Development topics",
      link: "https://web-dev041.netlify.app",

    },
  ];

  // Optimized callback functions with useCallback
  const showNotification = useCallback((msg, type = 'info') => {
    setNotification({ msg, type });
  }, []);

  const logActivity = useCallback((activity) => {
    setActivityLog(prev => [{
      activity,
      timestamp: new Date().toISOString(),
      user: user?.email || 'Anonymous'
    }, ...prev.slice(0, 99)]); // Keep only last 100 activities
  }, [user?.email]);

  const handleTabChange = useCallback((tab) => {
    const renderStart = startRender();
    setActiveTab(tab);
    logActivity(`Navigated to ${tab}`);
    endRender(renderStart);
  }, [startRender, endRender, logActivity]);

  const handleMenuToggle = useCallback((menuTitle) => {
    setExpandedMenus(prev => ({
      ...prev,
      [menuTitle]: !prev[menuTitle]
    }));
  }, []);

  const handleLogout = useCallback(async () => {
    try {
      await supabase.auth.signOut();
      setUser(null);
      showNotification('Logged out successfully!', 'success');
      logActivity('User logged out');
    } catch (error) {
      showNotification('Logout failed', 'error');
    }
  }, [supabase.auth, showNotification, logActivity]);

  // Fetch user profile
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (user) {
        setUserId(user.uid);
      } else {
        console.log('User is not authenticated');
        setLoading(false);
      }
    });
    return () => unsubscribe();
  }, []);

  useEffect(() => {
    if (userId) {
      const fetchUserProfile = async () => {
        const userRef = doc(db, "users", userId);
        const userDoc = await getDoc(userRef);

        if (userDoc.exists()) {
          const userData = userDoc.data();
          // Profile pic functionality can be added later
          console.log("User data loaded:", userData.dp);
        } else {
          console.log("No such user!");
        }
        setLoading(false);
      };
      fetchUserProfile();
    }
  }, [userId]);

  // Fetch training data
  useEffect(() => {
    fetch("/training-data.txt")
      .then((res) => res.text())
      .then((data) => setKnowledge(data))
      .catch((err) => console.error("Failed to load training data:", err));
  }, []);

  // Supabase auth state
  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user || null);
    });
    const { data: listener } = supabase.auth.onAuthStateChange((_event, session) => {
      setUser(session?.user || null);
    });
    return () => {
      listener?.subscription.unsubscribe();
    };
  }, [supabase.auth]);

  // Handle resume upload
  const handleResumeUpload = async (e) => {
    const file = e.target.files[0];
    if (!file || !user) return;
    setResumeUploadLoading(true);
    const filePath = `${user.id}/${file.name}`;
    const { error } = await supabase.storage.from('resumes').upload(filePath, file, { upsert: true });
    if (!error) {
      const { data: urlData } = supabase.storage.from('resumes').getPublicUrl(filePath);
      setResumeUrl(urlData.publicUrl);
      showNotification('Resume uploaded successfully!', 'success');
      logActivity('Uploaded a resume');
    } else {
      showNotification('Resume upload failed.', 'error');
    }
    setResumeUploadLoading(false);
  };

  // Handle resource upload
  const handleResourceUpload = async (e) => {
    const file = e.target.files[0];
    if (!file || !user) return;
    setResourceUploadLoading(true);
    const filePath = `${user.id}/${file.name}`;
    const { error } = await supabase.storage.from('resources').upload(filePath, file, { upsert: true });
    if (!error) {
      showNotification('Resource uploaded!', 'success');
      logActivity(`Uploaded resource: ${file.name}`);
    } else {
      showNotification('Resource upload failed.', 'error');
    }
    setResourceUploadLoading(false);
  };

  // Optimized company handlers with useCallback
  const handleCompanyClick = useCallback((company) => {
    // Add to recent companies
    setRecentCompanies(prev => {
      const filtered = prev.filter(c => c !== company);
      return [company, ...filtered].slice(0, 5); // Keep only 5 recent
    });

    logActivity(`Viewed ${company} DSA questions`);

    if (company.toLowerCase() === 'microsoft') {
      window.open('/company-dsa/Microsoft_questions.html', '_blank');
      return;
    }
    const formattedCompany = company.replace(/\s+/g, '');
    window.open(`/company-dsa/${formattedCompany}.html`, '_blank');
  }, [logActivity]);

  // Toggle favorite company
  const toggleFavorite = useCallback((company, e) => {
    e.stopPropagation(); // Prevent company click
    setFavoriteCompanies(prev => {
      if (prev.includes(company)) {
        return prev.filter(c => c !== company);
      } else {
        return [...prev, company];
      }
    });
  }, []);

  // Revert header color changes
  const revertHeaderChanges = () => {
    setShowRevertButton(false);
    showNotification('Header text color reverted to theme default!', 'success');

    // Actually revert the header text color by updating the DOM
    const eduNovaElement = document.querySelector('[data-header-title]');
    const subtitleElement = document.querySelector('[data-header-subtitle]');

    if (eduNovaElement) {
      eduNovaElement.style.color = '#333';
    }
    if (subtitleElement) {
      subtitleElement.style.color = '#333';
    }
  };

  // Memoized filtered companies for better performance
  const filteredCompanies = useMemo(() => {
    let filtered = companies;

    // Filter by category
    if (selectedCategory !== 'all') {
      const categoryCompanies = companyCategories[selectedCategory] || [];
      filtered = filtered.filter(company =>
        categoryCompanies.some(catCompany =>
          company.toLowerCase().includes(catCompany.toLowerCase()) ||
          catCompany.toLowerCase().includes(company.toLowerCase())
        )
      );
    }

    // Filter by search term
    filtered = filtered.filter(company =>
      company.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // Sort companies
    if (sortBy === 'name') {
      filtered.sort();
    } else if (sortBy === 'favorites') {
      filtered.sort((a, b) => {
        const aFav = favoriteCompanies.includes(a);
        const bFav = favoriteCompanies.includes(b);
        if (aFav && !bFav) return -1;
        if (!aFav && bFav) return 1;
        return a.localeCompare(b);
      });
    }

    return filtered;
  }, [companies, selectedCategory, searchTerm, sortBy, favoriteCompanies, companyCategories]);

  // Optimized quiz link handler
  const openQuizLink = useCallback((url) => {
    window.open(url, "_blank");
    logActivity(`Opened quiz: ${url}`);
  }, [logActivity]);

  // Optimized send message function with useCallback
  const sendMessage = useCallback(async () => {
    if (!input.trim()) return;

    const userMessage = { role: "user", content: input };
    setMessages((prev) => [...prev, userMessage]);
    const currentInput = input;
    setInput("");
    setLoading(true);

    try {
      const prompt = `You are a resume assistant. Help users improve their resumes, provide suggestions, and answer career-related questions. Use the following knowledge if it helps. If it's not relevant, use your own intelligence.\n\nKnowledge:\n${knowledge}\n\nQuestion: ${currentInput}`;

      const res = await axios.post(
        `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEY}`,
        {
          contents: [
            {
              parts: [{ text: prompt }],
            },
          ],
        },
        {
          headers: {
            "Content-Type": "application/json",
          },
          timeout: 30000 // 30 second timeout
        }
      );

      const botReply =
        res.data.candidates?.[0]?.content?.parts?.[0]?.text ||
        "⚠ No response received.";
      const botMessage = { role: "bot", content: botReply };
      setMessages((prev) => [...prev, botMessage]);
      logActivity(`Chat message sent: ${currentInput.substring(0, 50)}...`);
    } catch (error) {
      console.error("Gemini API Error:", error);
      setMessages((prev) => [
        ...prev,
        { role: "bot", content: "❌ Error: " + error.message },
      ]);
      showNotification('Failed to send message', 'error');
    } finally {
      setLoading(false);
    }
  }, [input, knowledge, API_KEY, logActivity, showNotification]);

  // Authentication functionality can be added later if needed

  // Auto-scroll chat
  useEffect(() => {
    if (chatEndRef.current) chatEndRef.current.scrollIntoView({ behavior: 'smooth' });
  }, [messages, loading]);

  // Chart data
  const getLast7Days = () => {
    const days = [];
    for (let i = 6; i >= 0; i--) {
      const d = new Date();
      d.setDate(d.getDate() - i);
      days.push(d.toLocaleDateString());
    }
    return days;
  };

  const chartLabels = getLast7Days();
  const chartData = {
    labels: chartLabels,
    datasets: [
      {
        label: 'Resource Uploads',
        data: chartLabels.map(day => activityLog.filter(a => a.type === 'activity' && a.msg.startsWith('Uploaded resource') && new Date(a.date).toLocaleDateString() === day).length),
        backgroundColor: '#3182ce',
      },
      {
        label: 'Coding Practice',
        data: chartLabels.map(day => activityLog.filter(a => a.type === 'activity' && a.msg === 'Clicked coding practice link' && new Date(a.date).toLocaleDateString() === day).length),
        backgroundColor: '#805ad5',
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: { position: 'top' },
      tooltip: { enabled: true },
    },
    scales: {
      y: { beginAtZero: true, ticks: { stepSize: 1 } },
    },
  };

  // Render optimized component structure
  return (
    <div className={`app-container ${shouldAnimate ? 'smooth-transition' : ''}`} style={{ backgroundColor: theme.background, color: theme.text }}>
      {/* Performance-optimized Navbar */}
      <Navbar
        sidebarOpen={sidebarOpen}
        onToggleSidebar={toggleSidebar}
        user={user}
        onLogout={handleLogout}
        adminEmail={ADMIN_EMAIL}
      />

      {/* Performance-optimized Sidebar */}
      <Sidebar
        isOpen={sidebarOpen}
        onClose={closeSidebar}
        activeTab={activeTab}
        onTabChange={handleTabChange}
        sidebarItems={updatedSidebarItems}
        expandedMenus={expandedMenus}
        onToggleMenu={handleMenuToggle}
        theme={theme}
      />

      {/* Main Content with optimized layout */}
      <main className={`main-content ${sidebarOpen && !isMobile ? 'main-content-with-sidebar' : ''}`}>
        {/* Dashboard Content */}
        {activeTab === "dashboard" && (
          <Suspense fallback={<div className="loading-spinner" style={{ margin: '50px auto' }} />}>
            <Dashboard user={user} onTabChange={handleTabChange} />
          </Suspense>
        )}

        {/* Quizzes Section with optimized layout */}
        {activeTab === "quizzes" && (
          <div style={{ padding: '24px', backgroundColor: theme.background }}>
            <div className="card" style={{ backgroundColor: theme.surface, border: `1px solid ${theme.border}` }}>
              <h2 style={{ marginTop: 0, color: theme.text }}>Career Quizzes</h2>
              <p style={{ opacity: 0.8, marginBottom: '24px', color: theme.textLight }}>
                Test your knowledge with our career-focused quizzes!
              </p>

              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
                gap: '16px'
              }}>
                {quizButtons.map((quiz, index) => (
                  <div
                    key={index}
                    className="quiz-card hover-lift"
                    style={{
                      backgroundColor: theme.surface,
                      border: `1px solid ${theme.border}`,
                      borderRadius: '12px',
                      padding: '20px',
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      cursor: 'pointer'
                    }}
                    onClick={() => openQuizLink(quiz.link)}
                  >
                    <div>
                      <h3 style={{ margin: '0 0 8px 0', color: theme.text }}>{quiz.title}</h3>
                      <p style={{
                        margin: 0,
                        fontSize: '14px',
                        opacity: 0.8,
                        color: theme.textLight
                      }}>
                        {quiz.description}
                      </p>
                    </div>
                    <div style={{ color: theme.primary }}>
                      <FiExternalLink size={20} />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* DSA Section with optimized company grid */}
        {activeTab === "dsa" && (
          <div style={{
            padding: '24px',
            backgroundColor: theme.background,
            minHeight: '100vh',
            position: 'relative',
            overflow: 'hidden'
          }}>
            {/* Animated Background Elements */}
            <div style={{
              position: 'absolute',
              top: '10%',
              left: '5%',
              width: '300px',
              height: '300px',
              background: 'linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))',
              borderRadius: '50%',
              animation: 'float 6s ease-in-out infinite',
              zIndex: 0
            }} />
            <div style={{
              position: 'absolute',
              top: '60%',
              right: '10%',
              width: '200px',
              height: '200px',
              background: 'linear-gradient(45deg, rgba(255,255,255,0.08), rgba(255,255,255,0.03))',
              borderRadius: '50%',
              animation: 'float 8s ease-in-out infinite reverse',
              zIndex: 0
            }} />
            <div style={{
              position: 'absolute',
              bottom: '20%',
              left: '15%',
              width: '150px',
              height: '150px',
              background: 'linear-gradient(45deg, rgba(255,255,255,0.06), rgba(255,255,255,0.02))',
              borderRadius: '50%',
              animation: 'float 10s ease-in-out infinite',
              zIndex: 0
            }} />

            <div style={{ maxWidth: '1400px', margin: '0 auto', position: 'relative', zIndex: 1 }}>

              {/* Hero Section */}
              <div style={{
                background: 'linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.1))',
                backdropFilter: 'blur(20px)',
                borderRadius: '25px',
                padding: '3rem',
                marginBottom: '2rem',
                color: 'white',
                position: 'relative',
                overflow: 'hidden',
                border: '1px solid rgba(255,255,255,0.2)',
                boxShadow: '0 25px 50px rgba(0,0,0,0.2)'
              }}>
                {/* Animated particles */}
                <div style={{
                  position: 'absolute',
                  top: '20px',
                  right: '20px',
                  fontSize: '2rem',
                  animation: 'bounce 2s infinite'
                }}>🚀</div>
                <div style={{
                  position: 'absolute',
                  bottom: '20px',
                  left: '20px',
                  fontSize: '1.5rem',
                  animation: 'bounce 3s infinite'
                }}>⭐</div>
                <div style={{
                  position: 'absolute',
                  top: '50%',
                  right: '10%',
                  fontSize: '1.2rem',
                  animation: 'bounce 4s infinite'
                }}>💎</div>

                <div style={{ display: 'flex', alignItems: 'center', gap: '2rem', position: 'relative', zIndex: 1 }}>
                  <div style={{
                    width: '120px',
                    height: '120px',
                    borderRadius: '50%',
                    background: 'linear-gradient(135deg, #ff6b6b, #feca57)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '3rem',
                    fontWeight: 'bold',
                    border: '4px solid rgba(255, 255, 255, 0.3)',
                    boxShadow: '0 15px 35px rgba(0,0,0,0.2)',
                    animation: 'pulse 3s infinite'
                  }}>
                    {user ? user.email[0].toUpperCase() : '👤'}
                  </div>
                  <div>
                    <h1 style={{
                      margin: 0,
                      fontSize: '3.5rem',
                      fontWeight: 800,
                      marginBottom: '1rem',
                      background: 'linear-gradient(45deg, #fff, #f0f0f0)',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      textShadow: '0 2px 4px rgba(0,0,0,0.3)'
                    }}>
                      Hey {user ? user.email.split('@')[0] : 'Champion'}! 🎯
                    </h1>
                    <p style={{
                      margin: 0,
                      fontSize: '1.3rem',
                      opacity: 0.95,
                      fontWeight: 500
                    }}>
                      Time to level up your skills and dominate your goals! 💪✨
                    </p>
                    <div style={{
                      marginTop: '1rem',
                      display: 'flex',
                      gap: '1rem'
                    }}>
                      <div style={{
                        background: 'rgba(255,255,255,0.2)',
                        padding: '0.5rem 1rem',
                        borderRadius: '20px',
                        fontSize: '0.9rem',
                        fontWeight: 600
                      }}>
                        🔥 12 Day Streak
                      </div>
                      <div style={{
                        background: 'rgba(255,255,255,0.2)',
                        padding: '0.5rem 1rem',
                        borderRadius: '20px',
                        fontSize: '0.9rem',
                        fontWeight: 600
                      }}>
                        🏆 Level 15
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Gaming-Style Stats Cards */}
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                gap: '1.5rem',
                marginBottom: '2rem'
              }}>
                {[
                  {
                    title: 'FIRE STREAK',
                    value: '12',
                    unit: 'DAYS',
                    icon: '🔥',
                    color: '#ff4757',
                    bgGradient: 'linear-gradient(135deg, #ff4757, #ff6b7a)',
                    glowColor: '#ff4757',
                    description: 'Unstoppable momentum!'
                  },
                  {
                    title: 'SKILL POINTS',
                    value: '2,847',
                    unit: 'XP',
                    icon: '⚡',
                    color: '#3742fa',
                    bgGradient: 'linear-gradient(135deg, #3742fa, #5352ed)',
                    glowColor: '#3742fa',
                    description: 'Level up achieved!'
                  },
                  {
                    title: 'POWER LEVEL',
                    value: '47',
                    unit: 'HOURS',
                    icon: '💪',
                    color: '#2ed573',
                    bgGradient: 'linear-gradient(135deg, #2ed573, #7bed9f)',
                    glowColor: '#2ed573',
                    description: 'Training complete!'
                  },
                  {
                    title: 'ACHIEVEMENTS',
                    value: '15',
                    unit: 'UNLOCKED',
                    icon: '🏆',
                    color: '#ffa502',
                    bgGradient: 'linear-gradient(135deg, #ffa502, #ffb142)',
                    glowColor: '#ffa502',
                    description: 'Champion status!'
                  }
                ].map((stat, index) => (
                  <div key={index} style={{
                    background: 'linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.1))',
                    backdropFilter: 'blur(20px)',
                    borderRadius: '20px',
                    padding: '2rem',
                    position: 'relative',
                    overflow: 'hidden',
                    border: '1px solid rgba(255,255,255,0.2)',
                    cursor: 'pointer',
                    transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
                    boxShadow: `0 15px 35px rgba(0,0,0,0.1), 0 0 0 1px ${stat.glowColor}20`
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'translateY(-10px) scale(1.02)';
                    e.currentTarget.style.boxShadow = `0 25px 50px rgba(0,0,0,0.2), 0 0 30px ${stat.glowColor}40`;
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'translateY(0) scale(1)';
                    e.currentTarget.style.boxShadow = `0 15px 35px rgba(0,0,0,0.1), 0 0 0 1px ${stat.glowColor}20`;
                  }}>

                    {/* Animated background pattern */}
                    <div style={{
                      position: 'absolute',
                      top: '-50%',
                      left: '-50%',
                      width: '200%',
                      height: '200%',
                      background: `conic-gradient(from 0deg, transparent, ${stat.color}20, transparent)`,
                      animation: 'rotate 20s linear infinite',
                      opacity: 0.3
                    }} />

                    {/* Glow effect */}
                    <div style={{
                      position: 'absolute',
                      top: '10px',
                      right: '10px',
                      width: '80px',
                      height: '80px',
                      background: stat.bgGradient,
                      borderRadius: '50%',
                      opacity: 0.2,
                      filter: 'blur(20px)'
                    }} />

                    <div style={{ position: 'relative', zIndex: 1 }}>
                      {/* Header */}
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        marginBottom: '1.5rem'
                      }}>
                        <div style={{
                          fontSize: '0.8rem',
                          fontWeight: 800,
                          color: 'rgba(255,255,255,0.8)',
                          letterSpacing: '2px'
                        }}>
                          {stat.title}
                        </div>
                        <div style={{
                          fontSize: '2rem',
                          filter: 'drop-shadow(0 0 10px currentColor)'
                        }}>
                          {stat.icon}
                        </div>
                      </div>

                      {/* Main Value */}
                      <div style={{
                        display: 'flex',
                        alignItems: 'baseline',
                        gap: '0.5rem',
                        marginBottom: '1rem'
                      }}>
                        <div style={{
                          fontSize: '3rem',
                          fontWeight: 900,
                          color: 'white',
                          textShadow: `0 0 20px ${stat.color}`,
                          lineHeight: 1
                        }}>
                          {stat.value}
                        </div>
                        <div style={{
                          fontSize: '0.9rem',
                          fontWeight: 600,
                          color: stat.color,
                          textTransform: 'uppercase',
                          letterSpacing: '1px'
                        }}>
                          {stat.unit}
                        </div>
                      </div>

                      {/* Description */}
                      <div style={{
                        fontSize: '0.9rem',
                        color: 'rgba(255,255,255,0.9)',
                        fontWeight: 500,
                        fontStyle: 'italic'
                      }}>
                        {stat.description}
                      </div>

                      {/* Progress bar */}
                      <div style={{
                        marginTop: '1rem',
                        height: '4px',
                        background: 'rgba(255,255,255,0.2)',
                        borderRadius: '2px',
                        overflow: 'hidden'
                      }}>
                        <div style={{
                          height: '100%',
                          width: `${Math.min(100, (index + 1) * 25)}%`,
                          background: stat.bgGradient,
                          borderRadius: '2px',
                          animation: 'slideIn 2s ease-out'
                        }} />
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Epic Action Center */}
              <div style={{
                background: 'linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.1))',
                backdropFilter: 'blur(20px)',
                borderRadius: '25px',
                padding: '2rem',
                marginBottom: '2rem',
                border: '1px solid rgba(255,255,255,0.2)',
                boxShadow: '0 25px 50px rgba(0,0,0,0.2)',
                position: 'relative',
                overflow: 'hidden'
              }}>
                <div style={{
                  position: 'absolute',
                  top: '-100px',
                  right: '-100px',
                  width: '300px',
                  height: '300px',
                  background: 'conic-gradient(from 0deg, #ff4757, #3742fa, #2ed573, #ffa502, #ff4757)',
                  borderRadius: '50%',
                  opacity: 0.1,
                  animation: 'rotate 30s linear infinite'
                }} />

                <h2 style={{
                  margin: '0 0 2rem 0',
                  fontSize: '2rem',
                  fontWeight: 800,
                  color: 'white',
                  textAlign: 'center',
                  textShadow: '0 2px 4px rgba(0,0,0,0.3)'
                }}>
                  🎮 MISSION CONTROL CENTER 🎮
                </h2>

                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                  gap: '1.5rem'
                }}>
                  {[
                    {
                      icon: '🎯',
                      title: 'BATTLE MODE',
                      subtitle: 'Take Quiz Challenge',
                      desc: 'Test your skills in epic battles!',
                      action: () => setActiveTab('quizzes'),
                      color: '#ff4757',
                      bgGradient: 'linear-gradient(135deg, #ff4757, #ff6b7a)'
                    },
                    {
                      icon: '⚔️',
                      title: 'CODE ARENA',
                      subtitle: 'DSA Combat Zone',
                      desc: 'Sharpen your coding weapons!',
                      action: () => setActiveTab('dsa'),
                      color: '#3742fa',
                      bgGradient: 'linear-gradient(135deg, #3742fa, #5352ed)'
                    },
                    {
                      icon: '📜',
                      title: 'SCROLL REVIEW',
                      subtitle: 'Resume Enhancement',
                      desc: 'Upgrade your legendary resume!',
                      action: () => setActiveTab('resume'),
                      color: '#2ed573',
                      bgGradient: 'linear-gradient(135deg, #2ed573, #7bed9f)'
                    },
                    {
                      icon: '�',
                      title: 'KNOWLEDGE VAULT',
                      subtitle: 'Study Materials',
                      desc: 'Access ancient wisdom scrolls!',
                      action: () => setActiveTab('resources'),
                      color: '#ffa502',
                      bgGradient: 'linear-gradient(135deg, #ffa502, #ffb142)'
                    }
                  ].map((action, index) => (
                    <div key={index}
                      onClick={action.action}
                      style={{
                        background: 'linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.05))',
                        backdropFilter: 'blur(15px)',
                        borderRadius: '20px',
                        padding: '1.5rem',
                        cursor: 'pointer',
                        transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
                        border: `2px solid ${action.color}30`,
                        position: 'relative',
                        overflow: 'hidden',
                        boxShadow: `0 10px 30px rgba(0,0,0,0.1), 0 0 0 1px ${action.color}20`
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'translateY(-8px) scale(1.05)';
                        e.currentTarget.style.boxShadow = `0 20px 40px rgba(0,0,0,0.2), 0 0 30px ${action.color}40`;
                        e.currentTarget.style.borderColor = action.color + '80';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'translateY(0) scale(1)';
                        e.currentTarget.style.boxShadow = `0 10px 30px rgba(0,0,0,0.1), 0 0 0 1px ${action.color}20`;
                        e.currentTarget.style.borderColor = action.color + '30';
                      }}
                    >
                      {/* Animated glow */}
                      <div style={{
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        width: '100px',
                        height: '100px',
                        background: action.bgGradient,
                        borderRadius: '50%',
                        transform: 'translate(-50%, -50%)',
                        opacity: 0.1,
                        filter: 'blur(30px)',
                        animation: 'pulse 3s ease-in-out infinite'
                      }} />

                      <div style={{ position: 'relative', zIndex: 1, textAlign: 'center' }}>
                        <div style={{
                          fontSize: '3rem',
                          marginBottom: '1rem',
                          filter: 'drop-shadow(0 0 10px currentColor)',
                          animation: 'bounce 2s ease-in-out infinite'
                        }}>
                          {action.icon}
                        </div>

                        <div style={{
                          fontSize: '1.1rem',
                          fontWeight: 800,
                          color: 'white',
                          marginBottom: '0.5rem',
                          textShadow: `0 0 10px ${action.color}`,
                          letterSpacing: '1px'
                        }}>
                          {action.title}
                        </div>

                        <div style={{
                          fontSize: '0.8rem',
                          color: action.color,
                          fontWeight: 600,
                          marginBottom: '0.75rem',
                          textTransform: 'uppercase',
                          letterSpacing: '0.5px'
                        }}>
                          {action.subtitle}
                        </div>

                        <div style={{
                          fontSize: '0.85rem',
                          color: 'rgba(255,255,255,0.8)',
                          fontStyle: 'italic',
                          lineHeight: 1.4
                        }}>
                          {action.desc}
                        </div>

                        {/* Power level indicator */}
                        <div style={{
                          marginTop: '1rem',
                          height: '3px',
                          background: 'rgba(255,255,255,0.2)',
                          borderRadius: '2px',
                          overflow: 'hidden'
                        }}>
                          <div style={{
                            height: '100%',
                            width: `${75 + index * 5}%`,
                            background: action.bgGradient,
                            borderRadius: '2px',
                            animation: 'slideIn 1.5s ease-out'
                          }} />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Recent Activity & Resume Management */}
              <div style={{
                display: 'grid',
                gridTemplateColumns: '1fr 1fr',
                gap: '2rem'
              }}>

                {/* Recent Activity */}
                <div style={{
                  background: globalStyles.currentTheme.surface,
                  borderRadius: '16px',
                  padding: '1.5rem',
                  boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,
                  border: `1px solid ${globalStyles.currentTheme.border}`
                }}>
                  <h3 style={{
                    margin: '0 0 1.5rem 0',
                    fontSize: '1.3rem',
                    fontWeight: 600,
                    color: globalStyles.currentTheme.text
                  }}>
                    📈 Recent Activity
                  </h3>

                  <div style={{
                    maxHeight: '250px',
                    overflowY: 'auto'
                  }}>
                    {activityLog.slice(0, 5).map((log, index) => (
                      <div key={index} style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '1rem',
                        padding: '0.75rem',
                        borderRadius: '8px',
                        marginBottom: '0.5rem',
                        background: index % 2 === 0 ? globalStyles.currentTheme.secondary : 'transparent',
                        transition: 'all 0.3s ease'
                      }}>
                        <div style={{
                          width: '10px',
                          height: '10px',
                          borderRadius: '50%',
                          background: log.type === 'login' ? '#4ECDC4' : '#45B7D1',
                          flexShrink: 0
                        }} />
                        <div style={{ flex: 1 }}>
                          <div style={{
                            fontSize: '0.9rem',
                            fontWeight: 500,
                            color: globalStyles.currentTheme.text,
                            marginBottom: '0.2rem'
                          }}>
                            {log.msg}
                          </div>
                          <div style={{
                            fontSize: '0.8rem',
                            color: globalStyles.currentTheme.textLight
                          }}>
                            {new Date(log.date).toLocaleString()}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Resume Management */}
                <div style={{
                  background: globalStyles.currentTheme.surface,
                  borderRadius: '16px',
                  padding: '1.5rem',
                  boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,
                  border: `1px solid ${globalStyles.currentTheme.border}`
                }}>
                  <h3 style={{
                    margin: '0 0 1.5rem 0',
                    fontSize: '1.3rem',
                    fontWeight: 600,
                    color: globalStyles.currentTheme.text
                  }}>
                    📄 Resume Management
                  </h3>

                  <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                    <label style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.75rem',
                      padding: '1rem',
                      borderRadius: '12px',
                      background: globalStyles.currentTheme.secondary,
                      border: `2px dashed ${globalStyles.currentTheme.border}`,
                      cursor: resumeUploadLoading ? 'not-allowed' : 'pointer',
                      transition: 'all 0.3s ease'
                    }}
                    onMouseEnter={(e) => {
                      if (!resumeUploadLoading) {
                        e.currentTarget.style.borderColor = globalStyles.currentTheme.primary;
                        e.currentTarget.style.background = globalStyles.currentTheme.primary + '10';
                      }
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.borderColor = globalStyles.currentTheme.border;
                      e.currentTarget.style.background = globalStyles.currentTheme.secondary;
                    }}>
                      <FiUpload size={20} color={globalStyles.currentTheme.primary} />
                      <div>
                        <div style={{
                          fontWeight: 600,
                          color: globalStyles.currentTheme.text,
                          marginBottom: '0.2rem'
                        }}>
                          {resumeUploadLoading ? 'Uploading...' : 'Upload Resume'}
                        </div>
                        <div style={{
                          fontSize: '0.8rem',
                          color: globalStyles.currentTheme.textLight
                        }}>
                          PDF files only
                        </div>
                      </div>
                      <input
                        type="file"
                        accept="application/pdf"
                        onChange={handleResumeUpload}
                        disabled={resumeUploadLoading}
                        style={{ display: 'none' }}
                      />
                    </label>

                    {resumeUrl && (
                      <a
                        href={resumeUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '0.75rem',
                          padding: '1rem',
                          borderRadius: '12px',
                          background: '#4ECDC4',
                          color: 'white',
                          textDecoration: 'none',
                          fontWeight: 600,
                          transition: 'all 0.3s ease'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.background = '#3DBDB6';
                          e.currentTarget.style.transform = 'translateY(-2px)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.background = '#4ECDC4';
                          e.currentTarget.style.transform = 'translateY(0)';
                        }}
                      >
                        <FiFileText size={20} />
                        <span>View Resume</span>
                      </a>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Chat Interface */}
        {activeTab === "resume" && (
          <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
            <div style={getStyle('card')}>
              <h2 style={{
                marginTop: 0,
                color: '#333'
              }}>Career Assistant</h2>
              <p style={{
                opacity: 0.8,
                marginBottom: '24px',
                color: '#666'
              }}>
                Get personalized resume advice and career guidance
              </p>

              {/* Chat messages */}
              <div style={{
                height: '50vh',
                overflowY: 'auto',
                marginBottom: '24px',
                padding: '16px',
                backgroundColor: '#f5f5f5',
                border: '1px solid #e0e0e0',
                borderRadius: '8px'
              }}>

                {messages.length === 0 ? (
                  <div style={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    textAlign: 'center',
                    opacity: 0.7
                  }}>
                    <div style={{ fontSize: '48px', marginBottom: '16px' }}>💬</div>
                    <h3 style={{
                      margin: 0,
                      color: '#333'
                    }}>Start a conversation</h3>
                    <p style={{
                      color: '#666'
                    }}>Ask about resumes, interviews, or career advice</p>
                  </div>
                ) : (
                  messages.map((msg, idx) => (
                    <div
                      key={idx}
                      style={{
                        ...(msg.role === 'user' ? getStyle('chatBubbleUser') : getStyle('chatBubbleBot')),
                        animation: 'fadeIn 0.3s ease'
                      }}
                    >
                      {msg.role === 'bot' ? (
                        <ReactMarkdown>{msg.content}</ReactMarkdown>
                      ) : (
                        msg.content
                      )}
                    </div>
                  ))
                )}
                {loading && (
                  <div style={getStyle('chatBubbleBot')}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <div style={{
                        width: '10px',
                        height: '10px',
                        borderRadius: '50%',
                        backgroundColor: '#1976d2',
                        animation: 'pulse 1.4s infinite ease-in-out'
                      }} />
                      <div style={{
                        width: '10px',
                        height: '10px',
                        borderRadius: '50%',
                        backgroundColor: '#1976d2',
                        animation: 'pulse 1.4s infinite ease-in-out',
                        animationDelay: '0.2s'
                      }} />
                      <div style={{
                        width: '10px',
                        height: '10px',
                        borderRadius: '50%',
                        backgroundColor: '#1976d2',
                        animation: 'pulse 1.4s infinite ease-in-out',
                        animationDelay: '0.4s'
                      }} />
                    </div>
                  </div>
                )}
                <div ref={chatEndRef} />
              </div>

              {/* Input area */}
              <form
                style={{ display: 'flex', gap: '12px' }}
                onSubmit={e => {
                  e.preventDefault();
                  sendMessage();
                }}
              >
                <input
                  type="text"
                  placeholder="Type your message..."
                  style={getStyle('inputField')}
                  value={input}
                  onChange={e => setInput(e.target.value)}
                  disabled={loading}
                />
                <button
                  type="submit"
                  style={{
                    ...getStyle('buttonPrimary'),
                    minWidth: '100px'
                  }}
                  disabled={loading || !input.trim()}
                >
                  {loading ? 'Sending...' : 'Send'}
                </button>
              </form>
            </div>
          </div>
        )}

        {/* Enhanced DSA Company Questions */}
        {activeTab === "dsa" && (
          <div style={{ padding: '24px' }}>
            <div style={getStyle('card')}>
              {/* Header with revert button */}
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
                <div>
                  <h2 style={{ marginTop: 0, marginBottom: '8px' }}>🚀 Company Wise DSA Questions</h2>
                  <p style={{ opacity: 0.8, margin: 0 }}>
                    Explore DSA questions from top companies with enhanced filtering and favorites
                  </p>
                </div>
                {showRevertButton && (
                  <button
                    onClick={revertHeaderChanges}
                    style={{
                      ...getStyle('buttonPrimary'),
                      background: '#ff6b6b',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      fontSize: '14px',
                      padding: '8px 16px',
                      border: 'none',
                      borderRadius: '8px',
                      color: 'white',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease'
                    }}
                    onMouseEnter={(e) => e.target.style.background = '#ff5252'}
                    onMouseLeave={(e) => e.target.style.background = '#ff6b6b'}
                  >
                    <FiRefreshCw size={16} />
                    Revert Header Color
                  </button>
                )}
              </div>

              {/* Category Tabs */}
              <div style={{
                display: 'flex',
                gap: '8px',
                marginBottom: '20px',
                flexWrap: 'wrap',
                borderBottom: '1px solid #eee',
                paddingBottom: '16px'
              }}>
                {['all', ...Object.keys(companyCategories)].map(category => (
                  <button
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    style={{
                      padding: '8px 16px',
                      borderRadius: '20px',
                      border: selectedCategory === category
                        ? 'none'
                        : '1px solid #ddd',
                      background: selectedCategory === category
                        ? globalStyles.currentTheme.primary
                        : 'transparent',
                      color: selectedCategory === category
                        ? 'white'
                        : '#666',
                      cursor: 'pointer',
                      fontSize: '14px',
                      fontWeight: selectedCategory === category ? 600 : 400,
                      transition: 'all 0.3s ease',
                      textTransform: 'capitalize'
                    }}
                    onMouseEnter={(e) => {
                      if (selectedCategory !== category) {
                        e.target.style.background = '#f5f5f5';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (selectedCategory !== category) {
                        e.target.style.background = 'transparent';
                      }
                    }}
                  >
                    {category === 'all' ? '🌟 All' : `${category === 'FAANG' ? '🔥' : category === 'Big Tech' ? '💻' : category === 'Startups' ? '🚀' : category === 'Finance' ? '💰' : category === 'Indian' ? '🇮🇳' : '🏢'} ${category}`}
                  </button>
                ))}
              </div>

              {/* Controls Row */}
              <div style={{
                display: 'flex',
                gap: '16px',
                marginBottom: '24px',
                flexWrap: 'wrap',
                alignItems: 'center'
              }}>
                {/* Search box */}
                <div style={{ position: 'relative', flex: 1, minWidth: '300px' }}>
                  <div style={{
                    position: 'absolute',
                    left: '16px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    color: '#666'
                  }}>
                    <FiSearch size={20} />
                  </div>
                  <input
                    type="text"
                    placeholder="Search companies..."
                    style={{
                      ...getStyle('inputField'),
                      paddingLeft: '48px',
                      width: '100%'
                    }}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                  {searchTerm && (
                    <button
                      style={{
                        position: 'absolute',
                        right: '16px',
                        top: '50%',
                        transform: 'translateY(-50%)',
                        background: 'none',
                        border: 'none',
                        color: '#666',
                        cursor: 'pointer'
                      }}
                      onClick={() => setSearchTerm("")}
                    >
                      <FiX size={20} />
                    </button>
                  )}
                </div>

                {/* Sort dropdown */}
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  style={{
                    ...getStyle('inputField'),
                    width: 'auto',
                    minWidth: '150px'
                  }}
                >
                  <option value="name">📝 Sort by Name</option>
                  <option value="favorites">⭐ Favorites First</option>
                </select>
              </div>

              {/* Recent Companies */}
              {recentCompanies.length > 0 && (
                <div style={{
                  marginBottom: '24px',
                  padding: '16px',
                  borderRadius: '12px',
                  background: '#f8f9fa',
                  border: '1px solid #e9ecef'
                }}>
                  <h3 style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    fontSize: '16px',
                    marginBottom: '12px',
                    color: '#333',
                    margin: '0 0 12px 0'
                  }}>
                    <FiClock color="#666" /> Recently Viewed
                  </h3>
                  <div style={{
                    display: 'flex',
                    gap: '8px',
                    flexWrap: 'wrap'
                  }}>
                    {recentCompanies.map(company => (
                      <button
                        key={company}
                        onClick={() => handleCompanyClick(company)}
                        style={{
                          padding: '6px 12px',
                          borderRadius: '16px',
                          border: `1px solid ${globalStyles.currentTheme.primary}`,
                          background: 'transparent',
                          color: globalStyles.currentTheme.primary,
                          cursor: 'pointer',
                          fontSize: '12px',
                          transition: 'all 0.3s ease'
                        }}
                        onMouseEnter={(e) => {
                          e.target.style.background = globalStyles.currentTheme.primary;
                          e.target.style.color = 'white';
                        }}
                        onMouseLeave={(e) => {
                          e.target.style.background = 'transparent';
                          e.target.style.color = globalStyles.currentTheme.primary;
                        }}
                      >
                        {company}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Companies grid */}
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fill, minmax(220px, 1fr))',
                gap: '16px',
                marginTop: '24px'
              }}>
                {filteredCompanies.map((company, index) => (
                  <div
                    key={index}
                    style={{
                      ...getStyle('companyCard'),
                      position: 'relative',
                      transform: favoriteCompanies.includes(company) ? 'scale(1.02)' : 'scale(1)',
                      border: favoriteCompanies.includes(company)
                        ? `2px solid ${globalStyles.currentTheme.primary}`
                        : `1px solid ${globalStyles.currentTheme.border}`,
                      background: globalStyles.currentTheme.surface,
                      color: globalStyles.currentTheme.text,
                      animation: `fadeIn 0.3s ease ${index * 0.1}s both`,
                      boxShadow: `0 4px 6px ${globalStyles.currentTheme.shadow}`
                    }}
                    onClick={() => handleCompanyClick(company)}
                  >
                    {/* Favorite button */}
                    <button
                      onClick={(e) => toggleFavorite(company, e)}
                      style={{
                        position: 'absolute',
                        top: '8px',
                        right: '8px',
                        background: 'none',
                        border: 'none',
                        cursor: 'pointer',
                        color: favoriteCompanies.includes(company) ? '#ff6b6b' : '#ccc',
                        transition: 'all 0.3s ease',
                        fontSize: '18px'
                      }}
                    >
                      <FiHeart fill={favoriteCompanies.includes(company) ? 'currentColor' : 'none'} />
                    </button>

                    {/* Company initial with gradient */}
                    <div style={{
                      width: '56px',
                      height: '56px',
                      borderRadius: '50%',
                      background: `linear-gradient(135deg, ${globalStyles.currentTheme.primary}, ${globalStyles.currentTheme.primaryDark})`,
                      color: 'white',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '24px',
                      fontWeight: 700,
                      marginBottom: '12px',
                      boxShadow: `0 4px 8px ${globalStyles.currentTheme.shadow}`
                    }}>
                      {company.charAt(0)}
                    </div>

                    {/* Company name */}
                    <div style={{
                      fontWeight: 600,
                      textAlign: 'center',
                      fontSize: '14px',
                      marginBottom: '8px'
                    }}>
                      {company}
                    </div>

                    {/* Mock stats */}
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      fontSize: '12px',
                      opacity: 0.7,
                      marginTop: '8px'
                    }}>
                      <span>📊 {Math.floor(Math.random() * 50) + 10} Questions</span>
                      <span>⭐ {(Math.random() * 2 + 3).toFixed(1)}</span>
                    </div>

                    {/* Category badge */}
                    {Object.entries(companyCategories).map(([category, companies]) => {
                      if (companies.some(c => c.toLowerCase() === company.toLowerCase())) {
                        return (
                          <div
                            key={category}
                            style={{
                              position: 'absolute',
                              top: '8px',
                              left: '8px',
                              background: globalStyles.currentTheme.primary,
                              color: 'white',
                              padding: '2px 6px',
                              borderRadius: '8px',
                              fontSize: '10px',
                              fontWeight: 600
                            }}
                          >
                            {category}
                          </div>
                        );
                      }
                      return null;
                    })}
                  </div>
                ))}
              </div>

              {/* No results message */}
              {filteredCompanies.length === 0 && (
                <div style={{
                  textAlign: 'center',
                  padding: '40px',
                  opacity: 0.7,
                  color: '#666'
                }}>
                  <div style={{ fontSize: '48px', marginBottom: '16px' }}>🔍</div>
                  <h3 style={{ color: '#333' }}>No companies found</h3>
                  <p style={{ color: '#666' }}>Try adjusting your search or category filter</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Quizzes Section */}
        {activeTab === "quizzes" && (
          <div style={{ padding: '24px' }}>
            <div style={getStyle('card')}>
              <h2 style={{ marginTop: 0 }}>Career Quizzes</h2>
              <p style={{ opacity: 0.8, marginBottom: '24px' }}>
                Test your knowledge with our career-focused quizzes!
              </p>

              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
                gap: '16px'
              }}>
                {quizButtons.map((quiz, index) => (
                  <div
                    key={index}
                    style={getStyle('quizCard')}
                    onClick={() => openQuizLink(quiz.link)}
                  >
                    <div>
                      <h3 style={{ margin: '0 0 8px 0' }}>{quiz.title}</h3>
                      <p style={{
                        margin: 0,
                        fontSize: '14px',
                        opacity: 0.8
                      }}>
                        {quiz.description}
                      </p>
                    </div>
                    <div style={{ color: '#1976d2' }}>
                      <FiExternalLink size={20} />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Other tabs with lazy loading */}
        {activeTab === "coding" && (
          <Suspense fallback={<div className="loading-spinner" style={{ margin: '50px auto' }} />}>
            <LazyCoding />
          </Suspense>
        )}

        {activeTab === "resources" && (
          <div style={{ padding: '24px' }}>
            <div style={getStyle('card')}>
              <h2 style={{
                marginTop: 0,
                color: '#333'
              }}>Resources</h2>
              <p style={{
                opacity: 0.8,
                marginBottom: '24px',
                color: '#666'
              }}>
                Upload and manage your study materials and notes
              </p>

              <div style={{ marginBottom: '24px' }}>
                <label style={{
                  ...getStyle('buttonPrimary'),
                  background: '#f5f5f5',
                  color: '#333',
                  border: '1px solid #ddd',
                  cursor: resourceUploadLoading ? 'not-allowed' : 'pointer'
                }}>
                  <FiUpload />
                  {resourceUploadLoading ? 'Uploading...' : 'Upload Resource'}
                  <input
                    type="file"
                    accept=".pdf,.doc,.docx,.txt"
                    onChange={handleResourceUpload}
                    disabled={resourceUploadLoading}
                    style={{ display: 'none' }}
                  />
                </label>
              </div>

              <div>
                <h3 style={{
                  marginBottom: '16px',
                  color: '#333'
                }}>Your Resources</h3>
                {userResources.length === 0 ? (
                  <p style={{
                    opacity: 0.7,
                    color: '#666'
                  }}>No resources uploaded yet</p>
                ) : (
                  <div style={{
                    backgroundColor: '#f5f5f5',
                    border: '1px solid #e0e0e0',
                    borderRadius: '8px',
                    padding: '16px'
                  }}>
                    {userResources.map((file, idx) => {
                      const { data: urlData } = supabase.storage.from('resources').getPublicUrl(`${user.id}/${file.name}`);
                      return (
                        <div key={idx} style={{
                          padding: '12px',
                          borderBottom: '1px solid #eee',
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center'
                        }}>
                          <span style={{
                            color: '#333'
                          }}>{file.name}</span>
                          <a
                            href={urlData.publicUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            style={{
                              color: '#1976d2',
                              textDecoration: 'none',
                              display: 'flex',
                              alignItems: 'center',
                              gap: '4px'
                            }}
                          >
                            <FiExternalLink size={16} />
                            Open
                          </a>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
        {activeTab === "academics" && (
          <Suspense fallback={<div className="loading-spinner" style={{ margin: '50px auto' }} />}>
            <LazyExams />
          </Suspense>
        )}

        {activeTab === "faq" && (
          <Suspense fallback={<div className="loading-spinner" style={{ margin: '50px auto' }} />}>
            <LazyFaq />
          </Suspense>
        )}
        {activeTab === "admin" && user?.email === ADMIN_EMAIL && (
          <div style={{ padding: '24px' }}>
            <div style={getStyle('card')}>
              <h2 style={{
                marginTop: 0,
                color: '#333'
              }}>Admin Panel</h2>
              <div style={{
                display: 'flex',
                gap: '16px',
                marginBottom: '24px'
              }}>
                <button
                  style={{
                    ...getStyle('buttonPrimary'),
                    background: adminTab === 'users' ?
                      globalStyles.currentTheme.primary : 'transparent',
                    color: adminTab === 'users' ?
                      'white' : '#333',
                    border: '1px solid #ddd'
                  }}
                  onClick={() => setAdminTab('users')}
                >
                  Users
                </button>
                <button
                  style={{
                    ...getStyle('buttonPrimary'),
                    background: adminTab === 'resources' ?
                      globalStyles.currentTheme.primary : 'transparent',
                    color: adminTab === 'resources' ?
                      'white' : '#333',
                    border: '1px solid #ddd'
                  }}
                  onClick={() => setAdminTab('resources')}
                >
                  Resources
                </button>
              </div>

              {adminTab === 'users' && (
                <div>
                  <h3 style={{
                    marginBottom: '16px',
                    color: '#333'
                  }}>All Users</h3>
                  <div style={{
                    backgroundColor: '#f5f5f5',
                    border: '1px solid #e0e0e0',
                    borderRadius: '8px',
                    padding: '16px'
                  }}>
                    {allUsers.map((user, idx) => (
                      <div key={idx} style={{
                        padding: '12px',
                        borderBottom: '1px solid #eee',
                        color: '#333'
                      }}>
                        {user.email}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {adminTab === 'resources' && (
                <div>
                  <h3 style={{
                    marginBottom: '16px',
                    color: '#333'
                  }}>All Resources</h3>
                  <p style={{
                    opacity: 0.7,
                    color: '#666'
                  }}>Resource management coming soon</p>
                </div>
              )}
            </div>
          </div>
        )}
      </main>

      {/* Performance-optimized Notification */}
      <Notification
        notification={notification}
        onClose={() => setNotification(null)}
      />
    </div>
  );
};
export default EduAIChatBot;