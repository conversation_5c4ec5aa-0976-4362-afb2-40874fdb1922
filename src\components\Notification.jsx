import React, { memo, useEffect } from 'react';
import { FiCheckCircle, FiXCircle, FiInfo, FiX } from 'react-icons/fi';
import '../components.css';

const Notification = memo(({ notification, onClose }) => {
  useEffect(() => {
    if (notification) {
      const timer = setTimeout(() => {
        onClose();
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [notification, onClose]);

  if (!notification) return null;

  const getIcon = () => {
    switch (notification.type) {
      case 'success':
        return <FiCheckCircle size={20} />;
      case 'error':
        return <FiXCircle size={20} />;
      default:
        return <FiInfo size={20} />;
    }
  };

  const getClassName = () => {
    switch (notification.type) {
      case 'success':
        return 'notification notification-success';
      case 'error':
        return 'notification notification-error';
      default:
        return 'notification notification-info';
    }
  };

  return (
    <div className={getClassName()}>
      <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
        {getIcon()}
        <span style={{ flex: 1 }}>{notification.msg}</span>
        <button
          onClick={onClose}
          style={{
            background: 'none',
            border: 'none',
            color: 'white',
            cursor: 'pointer',
            padding: '4px',
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            opacity: 0.8,
            transition: 'opacity 0.2s ease'
          }}
          onMouseEnter={(e) => e.target.style.opacity = '1'}
          onMouseLeave={(e) => e.target.style.opacity = '0.8'}
        >
          <FiX size={16} />
        </button>
      </div>
    </div>
  );
});

Notification.displayName = 'Notification';

export default Notification;
