{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\quiz\\\\aich (4)\\\\aich (3)\\\\aich(6)\\\\aich(5)\\\\src\\\\EduAIChatBot.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef, useCallback, useMemo, Suspense } from \"react\";\nimport { getDoc, doc } from 'firebase/firestore';\nimport { auth, db } from './firebaseConfig';\nimport axios from \"axios\";\nimport { sidebarItems } from './sidebarItems';\nimport { onAuthStateChanged } from 'firebase/auth';\nimport { Navbar, Sidebar, Dashboard, Notification } from './components';\nimport { useResponsive, useSidebar, useOptimizedAnimation, usePerformanceMonitor } from './hooks/useResponsive';\nimport { getTheme } from './theme';\nimport { FiFileText, FiCode, FiHelpCircle, FiAward, FiBook, <PERSON><PERSON>ser, FiShield, FiSearch, FiUpload, FiBriefcase, FiBarChart2, FiLayers, FiCheckCircle, FiExternalLink, FiHeart, FiClock, FiRefreshCw } from \"react-icons/fi\";\nimport { createClient } from '@supabase/supabase-js';\nimport './App.css';\nimport './components.css';\n\n// Lazy load heavy components for better performance\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst LazyFaq = /*#__PURE__*/React.lazy(_c = () => import('./Faq'));\n_c2 = LazyFaq;\nconst LazyExams = /*#__PURE__*/React.lazy(_c3 = () => import('./Exams'));\n_c4 = LazyExams;\nconst LazyCoding = /*#__PURE__*/React.lazy(_c5 = () => import('./Coding'));\n\n// Chart.js lazy loading with proper registration\n_c6 = LazyCoding;\nconst LazyChart = /*#__PURE__*/React.lazy(_c7 = async () => {\n  const {\n    Bar\n  } = await import('react-chartjs-2');\n  const {\n    Chart,\n    BarElement,\n    CategoryScale,\n    LinearScale,\n    Tooltip,\n    Legend\n  } = await import('chart.js');\n  Chart.register(BarElement, CategoryScale, LinearScale, Tooltip, Legend);\n  return {\n    default: Bar\n  };\n});\n_c8 = LazyChart;\nconst LazyMarkdown = /*#__PURE__*/React.lazy(_c9 = () => import('react-markdown'));\n\n// Enhanced sidebar items with icons\n_c0 = LazyMarkdown;\nconst updatedSidebarItems = sidebarItems.map(item => {\n  const iconMap = {\n    \"resume\": /*#__PURE__*/_jsxDEV(FiFileText, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 15\n    }, this),\n    \"dsa\": /*#__PURE__*/_jsxDEV(FiCode, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 12\n    }, this),\n    \"coding\": /*#__PURE__*/_jsxDEV(FiLayers, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 15\n    }, this),\n    \"resources\": /*#__PURE__*/_jsxDEV(FiBriefcase, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 18\n    }, this),\n    \"quizzes\": /*#__PURE__*/_jsxDEV(FiCheckCircle, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 16\n    }, this),\n    \"aptitude\": /*#__PURE__*/_jsxDEV(FiBarChart2, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 17\n    }, this),\n    \"academics\": /*#__PURE__*/_jsxDEV(FiBook, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 18\n    }, this),\n    \"faq\": /*#__PURE__*/_jsxDEV(FiHelpCircle, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 12\n    }, this),\n    \"admin\": /*#__PURE__*/_jsxDEV(FiShield, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 14\n    }, this)\n  };\n  return {\n    ...item,\n    icon: iconMap[item.tab] || iconMap[item.title.toLowerCase()] || /*#__PURE__*/_jsxDEV(FiAward, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 69\n    }, this)\n  };\n});\nconst EduAIChatBot = () => {\n  _s();\n  // Performance monitoring\n  const {\n    metrics,\n    startRender,\n    endRender\n  } = usePerformanceMonitor();\n  const {\n    isMobile,\n    isTablet\n  } = useResponsive();\n  const {\n    isOpen: sidebarOpen,\n    toggle: toggleSidebar,\n    close: closeSidebar\n  } = useSidebar();\n  const {\n    shouldAnimate\n  } = useOptimizedAnimation();\n\n  // Optimized state declarations with proper initial values\n  const [input, setInput] = useState(\"\");\n  const [messages, setMessages] = useState([]);\n  const [userId, setUserId] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [knowledge, setKnowledge] = useState(\"\");\n  const [activeTab, setActiveTab] = useState(\"dashboard\");\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [expandedMenus, setExpandedMenus] = useState({});\n  const [user, setUser] = useState(null);\n  const [isDarkMode, setIsDarkMode] = useState(false);\n  const [resumeUploadLoading, setResumeUploadLoading] = useState(false);\n  const [resumeUrl, setResumeUrl] = useState(null);\n  const [resourceUploadLoading, setResourceUploadLoading] = useState(false);\n  const [userResources] = useState([]);\n  const ADMIN_EMAIL = '<EMAIL>';\n  const [allUsers] = useState([]);\n  const [adminTab, setAdminTab] = useState('users');\n  const [notification, setNotification] = useState(null);\n  const [activityLog, setActivityLog] = useState([]);\n  const chatEndRef = useRef(null);\n\n  // Enhanced DSA section states\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [sortBy, setSortBy] = useState('name');\n  const [favoriteCompanies, setFavoriteCompanies] = useState([]);\n  const [recentCompanies, setRecentCompanies] = useState([]);\n  const [showRevertButton, setShowRevertButton] = useState(true);\n\n  // API configurations\n  const API_KEY = \"AIzaSyC6kHWto78QdqHz7Uu9RzEXb443ZO7tG5M\";\n  const SUPABASE_URL = 'https://gziaptswfepiveyylven.supabase.co';\n  const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imd6aWFwdHN3ZmVwaXZleXlsdmVuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU2NzczNTYsImV4cCI6MjA2MTI1MzM1Nn0.wmqXZGffrox8E_PuCwbzh4xJEffsvFmZCVcF6WFAX6Q';\n  const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);\n\n  // Company categories for enhanced DSA section\n  const companyCategories = {\n    'FAANG': ['Facebook', 'Apple', 'Amazon', 'Netflix', 'Google'],\n    'Big Tech': ['Microsoft', 'Adobe', 'Salesforce', 'Oracle', 'IBM', 'Intel', 'Nvidia'],\n    'Startups': ['Stripe', 'Airbnb', 'Uber', 'Lyft', 'DoorDash', 'Instacart', 'Coinbase'],\n    'Finance': ['Goldman Sachs', 'JPMorgan', 'Morgan Stanley', 'BlackRock', 'Citadel', 'Two Sigma'],\n    'Consulting': ['McKinsey', 'BCG', 'Bain', 'Deloitte', 'Accenture', 'PwC'],\n    'E-commerce': ['Amazon', 'eBay', 'Shopify', 'Etsy', 'Wayfair', 'Booking.com'],\n    'Gaming': ['Blizzard', 'Epic Games', 'Riot Games', 'Unity', 'Roblox'],\n    'Indian': ['TCS', 'Infosys', 'Wipro', 'HCL', 'Flipkart', 'Paytm', 'Zomato', 'Swiggy']\n  };\n\n  // Complete list of companies\n  const companies = [\"Accenture\", \"Accolite\", \"Adobe\", \"Affirm\", \"Agoda\", \"Airbnb\", \"Airtel\", \"Akamar\", \"Akuna Capital\", \"Alibaba\", \"Altimetrik\", \"Amazon\", \"AMD\", \"Amdocs\", \"American Express\", \"Anduril\", \"Apple\", \"Arista Networks\", \"Arcesium\", \"Atlassian\", \"Attentive\", \"athenahealth\", \"Autodesk\", \"Avito\", \"Baidu\", \"Barclays\", \"BitGo\", \"BlackRock\", \"Blizzard\", \"Block\", \"Bloomberg\", \"BNY Mellon\", \"Boft\", \"Booking.com\", \"Bos\", \"BP\", \"ByteDance\", \"Cadence\", \"Capgemini\", \"Capital One\", \"CARS24\", \"carwale\", \"Cashfree\", \"Chewy\", \"Cisco\", \"Citadel\", \"Citrix\", \"Cloudera\", \"Cloudflare\", \"Cognizant\", \"Coinbase\", \"Commvault\", \"Confluent\", \"Coupang\", \"Coursera\", \"CrowdStrike\", \"Cruise\", \"Curefit\", \"Databricks\", \"Datadog\", \"DE Shaw\", \"Deloitte\", \"Dell\", \"Deliveroo\", \"Derantior\", \"Deutsche Bank\", \"Devflev\", \"Directi\", \"Disney\", \"Docusign\", \"DoorDash\", \"Dream11\", \"Dropbox\", \"DRW\", \"Dunzo\", \"eBay\", \"EPAM Systems\", \"Epic Systems\", \"Expedia\", \"FactSet\", \"Flexport\", \"Flipkart\", \"Freshworks\", \"GE Healthcare\", \"Geico\", \"Goldman Sachs\", \"Google\", \"Grab\", \"Grammarly\", \"Graviton\", \"Groww\", \"GSN Games\", \"Hashedin\", \"HCL\", \"HPE\", \"Hubspot\", \"Hudson River Trading\", \"Huawei\", \"IBM\", \"IMC\", \"Indeed\", \"Infosys\", \"InMobi\", \"Intel\", \"Intuit\", \"JPMorgan\", \"Jane Street\", \"Josh Technology\", \"Jump Trading\", \"Juspay\", \"Karat\", \"KLA\", \"LinkedIn\", \"LiveRamp\", \"Lowe's\", \"Lucid\", \"Lyft\", \"MakeMyTrip\", \"Mastercard\", \"MathWorks\", \"Media.net\", \"Meesho\", \"Mercari\", \"Meta\", \"Microsoft\", \"Millennium\", \"Mitsogo\", \"Moloco\", \"MongoDB\", \"Morgan Stanley\", \"Moveworks\", \"Myntra\", \"Nagarro\", \"NetApp\", \"Netflix\", \"Nextdoor\", \"Nielsen\", \"Nike\", \"Niantic\", \"Nordstrom\", \"Nutanix\", \"Nvidia\", \"Okta\", \"OKX\", \"OpenAI\", \"OpenText\", \"Oracle\", \"Otter.ai\", \"Oyo\", \"Ozon\", \"Palantir Technologies\", \"Palo Alto Networks\", \"PayPal\", \"Paytm\", \"Persistent Systems\", \"PhonePe\", \"Pinterest\", \"Pocket Gems\", \"Point72\", \"Pure Storage\", \"Qualcomm\", \"Quora\", \"Rakuten\", \"Razorpay\", \"RBC\", \"Reddit\", \"Revolut\", \"Robinhood\", \"Roblox\", \"Rubrik\", \"Salesforce\", \"Samsung\", \"SAP\", \"ServiceNow\", \"Shopify\", \"Siemens\", \"Sigmoid\", \"SIG\", \"Snowflake\", \"Snap\", \"Sofi\", \"Splunk\", \"Spotify\", \"Sprinklr\", \"Squarepoint Capital\", \"Stripe\", \"Swiggy\", \"TCS\", \"Tekion\", \"Tencent\", \"Tesla\", \"ThoughtSpot\", \"ThoughtWorks\", \"TikTok\", \"Tinkoff\", \"Trilogy\", \"Turing\", \"Turo\", \"Twilio\", \"Twitch\", \"Two Sigma\", \"Uber\", \"UiPath\", \"UKG\", \"Veeva Systems\", \"Verily\", \"Verkada\", \"Virtu Financial\", \"Visa\", \"VK\", \"VMware\", \"Walmart Labs\", \"WarnerMedia\", \"Wayfair\", \"Wells Fargo\", \"Wipro\", \"Wix\", \"Workday\", \"X\", \"Yahoo\", \"Yandex\", \"Yelp\", \"Zalando\", \"Zenefits\", \"Zepto\", \"Zeta\", \"Zillow\", \"Zoho\", \"Zomato\", \"ZScaler\", \"Zopsmart\"];\n\n  // Quiz buttons data\n  const quizButtons = [{\n    title: \"OP and CN Quiz\",\n    description: \"Test your knowledge of Operating System and Computer Networks\",\n    link: \"https://opcn.netlify.app\"\n  }, {\n    title: \"OOPs and DBMS Quiz\",\n    description: \"Challenge yourself with oops and dbms\",\n    link: \"https://oopsanddbms.netlify.app/\"\n  }, {\n    title: \"System Design Quiz\",\n    description: \"Test your system design knowledge\",\n    link: \"https://system-design041.netlify.app\"\n  }, {\n    title: \"Quantitative Aptitude and Reasoning Quiz\",\n    description: \"Practice common quant and reasoning questions\",\n    link: \"https://quantandreasoning.netlify.app\"\n  }, {\n    title: \"Cloud & DevOps Quiz\",\n    description: \"Test your knowledge of Cloud and DevOps concepts\",\n    link: \"https://cloud-devops.netlify.app\"\n  }, {\n    title: \"DSA Quiz\",\n    description: \"Data Structures and Algorithms quiz\",\n    link: \"https://dsa041.netlify.app\"\n  }, {\n    title: \"Operating System & Computer Networks Quiz\",\n    description: \"Quiz on OS and Computer Networks\",\n    link: \"https://opcn.netlify.app\"\n  }, {\n    title: \"Web Development Quiz\",\n    description: \"Quiz on Web Development topics\",\n    link: \"https://web-dev041.netlify.app\"\n  }];\n\n  // Use centralized styles\n  const styles = {\n    ...globalStyles,\n    appContainer: {\n      ...globalStyles.appContainer,\n      backgroundColor: globalStyles.currentTheme.background,\n      color: globalStyles.currentTheme.text\n    },\n    navbar: {\n      ...globalStyles.navbarFixed,\n      borderBottom: `1px solid ${globalStyles.currentTheme.border}`\n    },\n    sidebar: {\n      ...globalStyles.sidebarFixed,\n      backgroundColor: globalStyles.currentTheme.surface,\n      borderRight: `1px solid ${globalStyles.currentTheme.border}`,\n      transform: sidebarOpen ? 'translateX(0)' : 'translateX(-100%)'\n    },\n    sidebarItem: {\n      ...globalStyles.sidebarItemEdu,\n      color: globalStyles.currentTheme.text,\n      background: globalStyles.currentTheme.surface,\n      border: `1px solid ${globalStyles.currentTheme.border}`,\n      transition: 'all 0.3s ease',\n      '&:hover': {\n        background: globalStyles.currentTheme.primary,\n        color: 'white'\n      }\n    },\n    sidebarItemActive: {\n      ...globalStyles.sidebarItemActiveEdu,\n      color: 'white',\n      background: globalStyles.currentTheme.primary,\n      border: `1px solid ${globalStyles.currentTheme.primary}`\n    },\n    mainContent: {\n      ...globalStyles.mainContentEdu,\n      marginLeft: sidebarOpen ? '280px' : '0',\n      backgroundColor: globalStyles.currentTheme.background,\n      color: globalStyles.currentTheme.text,\n      minHeight: '100vh'\n    },\n    card: {\n      ...globalStyles.cardEdu,\n      backgroundColor: globalStyles.currentTheme.surface,\n      color: globalStyles.currentTheme.text,\n      border: `1px solid ${globalStyles.currentTheme.border}`,\n      boxShadow: `0 4px 6px ${globalStyles.currentTheme.shadow}`\n    },\n    buttonPrimary: {\n      ...globalStyles.buttonPrimary\n    },\n    inputField: {\n      ...globalStyles.inputField,\n      backgroundColor: '#fff',\n      color: '#333',\n      border: '1px solid #ddd',\n      '&:focus': {\n        borderColor: globalStyles.currentTheme.primary,\n        outline: 'none',\n        boxShadow: `0 0 0 2px ${globalStyles.currentTheme.primary}20`\n      }\n    },\n    chatBubbleUser: {\n      ...globalStyles.chatBubbleUser,\n      backgroundColor: globalStyles.currentTheme.primary,\n      color: 'white'\n    },\n    chatBubbleBot: {\n      ...globalStyles.chatBubbleBot,\n      backgroundColor: globalStyles.currentTheme.secondary,\n      color: globalStyles.currentTheme.text,\n      border: '1px solid transparent'\n    },\n    companyCard: {\n      ...globalStyles.companyCardEdu\n    },\n    quizCard: {\n      ...globalStyles.quizCardEdu,\n      backgroundColor: globalStyles.currentTheme.surface\n    },\n    notification: {\n      ...globalStyles.notification\n    }\n  };\n\n  // Helper function to apply styles with hover states\n  const getStyle = (styleName, hover = false) => {\n    const baseStyle = styles[styleName];\n    if (typeof baseStyle === 'function') return baseStyle();\n    if (hover && baseStyle['&:hover']) {\n      return {\n        ...baseStyle,\n        ...baseStyle['&:hover']\n      };\n    }\n    return baseStyle;\n  };\n\n  // Fetch user profile\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, user => {\n      if (user) {\n        setUserId(user.uid);\n      } else {\n        console.log('User is not authenticated');\n        setLoading(false);\n      }\n    });\n    return () => unsubscribe();\n  }, []);\n  useEffect(() => {\n    if (userId) {\n      const fetchUserProfile = async () => {\n        const userRef = doc(db, \"users\", userId);\n        const userDoc = await getDoc(userRef);\n        if (userDoc.exists()) {\n          const userData = userDoc.data();\n          // Profile pic functionality can be added later\n          console.log(\"User data loaded:\", userData.dp);\n        } else {\n          console.log(\"No such user!\");\n        }\n        setLoading(false);\n      };\n      fetchUserProfile();\n    }\n  }, [userId]);\n\n  // Fetch training data\n  useEffect(() => {\n    fetch(\"/training-data.txt\").then(res => res.text()).then(data => setKnowledge(data)).catch(err => console.error(\"Failed to load training data:\", err));\n  }, []);\n\n  // Supabase auth state\n  useEffect(() => {\n    supabase.auth.getSession().then(({\n      data: {\n        session\n      }\n    }) => {\n      setUser((session === null || session === void 0 ? void 0 : session.user) || null);\n    });\n    const {\n      data: listener\n    } = supabase.auth.onAuthStateChange((_event, session) => {\n      setUser((session === null || session === void 0 ? void 0 : session.user) || null);\n    });\n    return () => {\n      listener === null || listener === void 0 ? void 0 : listener.subscription.unsubscribe();\n    };\n  }, [supabase.auth]);\n\n  // Handle resume upload\n  const handleResumeUpload = async e => {\n    const file = e.target.files[0];\n    if (!file || !user) return;\n    setResumeUploadLoading(true);\n    const filePath = `${user.id}/${file.name}`;\n    const {\n      error\n    } = await supabase.storage.from('resumes').upload(filePath, file, {\n      upsert: true\n    });\n    if (!error) {\n      const {\n        data: urlData\n      } = supabase.storage.from('resumes').getPublicUrl(filePath);\n      setResumeUrl(urlData.publicUrl);\n      showNotification('Resume uploaded successfully!', 'success');\n      logActivity('Uploaded a resume');\n    } else {\n      showNotification('Resume upload failed.', 'error');\n    }\n    setResumeUploadLoading(false);\n  };\n\n  // Handle resource upload\n  const handleResourceUpload = async e => {\n    const file = e.target.files[0];\n    if (!file || !user) return;\n    setResourceUploadLoading(true);\n    const filePath = `${user.id}/${file.name}`;\n    const {\n      error\n    } = await supabase.storage.from('resources').upload(filePath, file, {\n      upsert: true\n    });\n    if (!error) {\n      showNotification('Resource uploaded!', 'success');\n      logActivity(`Uploaded resource: ${file.name}`);\n    } else {\n      showNotification('Resource upload failed.', 'error');\n    }\n    setResourceUploadLoading(false);\n  };\n\n  // Enhanced company click handler\n  const handleCompanyClick = company => {\n    // Add to recent companies\n    setRecentCompanies(prev => {\n      const filtered = prev.filter(c => c !== company);\n      return [company, ...filtered].slice(0, 5); // Keep only 5 recent\n    });\n    logActivity(`Viewed ${company} DSA questions`);\n    if (company.toLowerCase() === 'microsoft') {\n      window.location.href = '/company-dsa/Microsoft_questions.html';\n      return;\n    }\n    const formattedCompany = company.replace(/\\s+/g, '');\n    window.location.href = `/company-dsa/${formattedCompany}.html`;\n  };\n\n  // Toggle favorite company\n  const toggleFavorite = (company, e) => {\n    e.stopPropagation(); // Prevent company click\n    setFavoriteCompanies(prev => {\n      if (prev.includes(company)) {\n        return prev.filter(c => c !== company);\n      } else {\n        return [...prev, company];\n      }\n    });\n  };\n\n  // Revert header color changes\n  const revertHeaderChanges = () => {\n    setShowRevertButton(false);\n    showNotification('Header text color reverted to theme default!', 'success');\n\n    // Actually revert the header text color by updating the DOM\n    const eduNovaElement = document.querySelector('[data-header-title]');\n    const subtitleElement = document.querySelector('[data-header-subtitle]');\n    if (eduNovaElement) {\n      eduNovaElement.style.color = '#333';\n    }\n    if (subtitleElement) {\n      subtitleElement.style.color = '#333';\n    }\n  };\n\n  // Get filtered companies based on category and search\n  const getFilteredCompanies = () => {\n    let filtered = companies;\n\n    // Filter by category\n    if (selectedCategory !== 'all') {\n      const categoryCompanies = companyCategories[selectedCategory] || [];\n      filtered = filtered.filter(company => categoryCompanies.some(catCompany => company.toLowerCase().includes(catCompany.toLowerCase()) || catCompany.toLowerCase().includes(company.toLowerCase())));\n    }\n\n    // Filter by search term\n    filtered = filtered.filter(company => company.toLowerCase().includes(searchTerm.toLowerCase()));\n\n    // Sort companies\n    if (sortBy === 'name') {\n      filtered.sort();\n    } else if (sortBy === 'favorites') {\n      filtered.sort((a, b) => {\n        const aFav = favoriteCompanies.includes(a);\n        const bFav = favoriteCompanies.includes(b);\n        if (aFav && !bFav) return -1;\n        if (!aFav && bFav) return 1;\n        return a.localeCompare(b);\n      });\n    }\n    return filtered;\n  };\n\n  // Open quiz link\n  const openQuizLink = url => {\n    window.open(url, \"_blank\");\n  };\n\n  // Send message to chatbot\n  const sendMessage = async () => {\n    if (!input.trim()) return;\n    const userMessage = {\n      role: \"user\",\n      content: input\n    };\n    setMessages(prev => [...prev, userMessage]);\n    setInput(\"\");\n    setLoading(true);\n    try {\n      var _res$data$candidates, _res$data$candidates$, _res$data$candidates$2, _res$data$candidates$3, _res$data$candidates$4;\n      const prompt = `You are a resume assistant. Help users improve their resumes, provide suggestions, and answer career-related questions. Use the following knowledge if it helps. If it's not relevant, use your own intelligence.\\n\\nKnowledge:\\n${knowledge}\\n\\nQuestion: ${input}`;\n      const res = await axios.post(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEY}`, {\n        contents: [{\n          parts: [{\n            text: prompt\n          }]\n        }]\n      }, {\n        headers: {\n          \"Content-Type\": \"application/json\"\n        }\n      });\n      const botReply = ((_res$data$candidates = res.data.candidates) === null || _res$data$candidates === void 0 ? void 0 : (_res$data$candidates$ = _res$data$candidates[0]) === null || _res$data$candidates$ === void 0 ? void 0 : (_res$data$candidates$2 = _res$data$candidates$.content) === null || _res$data$candidates$2 === void 0 ? void 0 : (_res$data$candidates$3 = _res$data$candidates$2.parts) === null || _res$data$candidates$3 === void 0 ? void 0 : (_res$data$candidates$4 = _res$data$candidates$3[0]) === null || _res$data$candidates$4 === void 0 ? void 0 : _res$data$candidates$4.text) || \"⚠ No response received.\";\n      const botMessage = {\n        role: \"bot\",\n        content: botReply\n      };\n      setMessages(prev => [...prev, botMessage]);\n    } catch (error) {\n      console.error(\"Gemini API Error:\", error);\n      setMessages(prev => [...prev, {\n        role: \"bot\",\n        content: \"❌ Error: \" + error.message\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Authentication functionality can be added later if needed\n\n  // Handle logout\n  const handleLogout = async () => {\n    await supabase.auth.signOut();\n  };\n\n  // Show notification\n  const showNotification = (msg, type = 'info') => {\n    setNotification({\n      msg,\n      type\n    });\n    setTimeout(() => setNotification(null), 3000);\n  };\n\n  // Log activity\n  const logActivity = msg => {\n    setActivityLog(log => [{\n      type: 'activity',\n      date: new Date().toISOString(),\n      msg\n    }, ...log.slice(0, 19)]);\n  };\n\n  // Toggle menu\n  const toggleMenu = menu => {\n    setExpandedMenus(prev => ({\n      ...prev,\n      [menu]: !prev[menu]\n    }));\n  };\n\n  // Auto-scroll chat\n  useEffect(() => {\n    if (chatEndRef.current) chatEndRef.current.scrollIntoView({\n      behavior: 'smooth'\n    });\n  }, [messages, loading]);\n\n  // Chart data\n  const getLast7Days = () => {\n    const days = [];\n    for (let i = 6; i >= 0; i--) {\n      const d = new Date();\n      d.setDate(d.getDate() - i);\n      days.push(d.toLocaleDateString());\n    }\n    return days;\n  };\n  const chartLabels = getLast7Days();\n  const chartData = {\n    labels: chartLabels,\n    datasets: [{\n      label: 'Resource Uploads',\n      data: chartLabels.map(day => activityLog.filter(a => a.type === 'activity' && a.msg.startsWith('Uploaded resource') && new Date(a.date).toLocaleDateString() === day).length),\n      backgroundColor: '#3182ce'\n    }, {\n      label: 'Coding Practice',\n      data: chartLabels.map(day => activityLog.filter(a => a.type === 'activity' && a.msg === 'Clicked coding practice link' && new Date(a.date).toLocaleDateString() === day).length),\n      backgroundColor: '#805ad5'\n    }]\n  };\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'top'\n      },\n      tooltip: {\n        enabled: true\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          stepSize: 1\n        }\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: getStyle('appContainer'),\n    children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n      style: getStyle('navbar'),\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          background: 'none',\n          border: 'none',\n          color: 'white',\n          // Always white since navbar has gradient background\n          marginRight: '20px',\n          cursor: 'pointer',\n          padding: '8px',\n          borderRadius: '4px',\n          transition: 'all 0.2s ease'\n        },\n        onClick: () => setSidebarOpen(!sidebarOpen),\n        onMouseEnter: e => e.target.style.background = 'rgba(255, 255, 255, 0.1)',\n        onMouseLeave: e => e.target.style.background = 'none',\n        children: sidebarOpen ? /*#__PURE__*/_jsxDEV(FiX, {\n          size: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 606,\n          columnNumber: 26\n        }, this) : /*#__PURE__*/_jsxDEV(FiMenu, {\n          size: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 606,\n          columnNumber: 46\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 591,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: require('./eduai-logo.jpg'),\n          alt: \"EduAI Logo\",\n          style: {\n            height: '36px',\n            marginRight: '12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 610,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            \"data-header-title\": true,\n            style: {\n              fontWeight: 600,\n              fontSize: '18px',\n              color: 'white'\n            },\n            children: \"EDU NOVA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 616,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            \"data-header-subtitle\": true,\n            style: {\n              fontSize: '12px',\n              opacity: 0.8,\n              color: 'white'\n            },\n            children: \"AI POWERED LEARNING SYSTEM\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 622,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 615,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 609,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '16px'\n        },\n        children: user ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '40px',\n              height: '40px',\n              borderRadius: '50%',\n              background: 'rgba(255, 255, 255, 0.2)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              fontWeight: 600,\n              cursor: 'pointer',\n              color: 'white',\n              backdropFilter: 'blur(10px)'\n            },\n            children: user.email === ADMIN_EMAIL ? /*#__PURE__*/_jsxDEV(FiShield, {\n              size: 20,\n              color: \"#4caf50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 647,\n              columnNumber: 47\n            }, this) : user.email[0].toUpperCase()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 634,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            style: {\n              ...getStyle('buttonPrimary'),\n              background: 'rgba(255, 255, 255, 0.2)',\n              color: 'white',\n              border: '1px solid rgba(255, 255, 255, 0.3)',\n              backdropFilter: 'blur(10px)'\n            },\n            onClick: handleLogout,\n            children: [/*#__PURE__*/_jsxDEV(FiLogOut, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 659,\n              columnNumber: 17\n            }, this), \" Logout\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 649,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(\"button\", {\n          style: {\n            ...getStyle('buttonPrimary'),\n            background: 'rgba(255, 255, 255, 0.2)',\n            color: 'white',\n            border: '1px solid rgba(255, 255, 255, 0.3)',\n            backdropFilter: 'blur(10px)'\n          },\n          onClick: () => {\n            console.log('Login functionality to be implemented');\n          },\n          children: [/*#__PURE__*/_jsxDEV(FiLogIn, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 673,\n            columnNumber: 15\n          }, this), \" Login\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 663,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 631,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 590,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n      style: getStyle('sidebar'),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '16px'\n        },\n        children: updatedSidebarItems.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              ...getStyle('sidebarItem'),\n              ...(activeTab === item.tab ? getStyle('sidebarItemActive') : {}),\n              cursor: 'pointer',\n              transition: 'all 0.3s ease'\n            },\n            onClick: () => {\n              setActiveTab(item.tab);\n              setSidebarOpen(false);\n            },\n            onMouseEnter: e => {\n              if (activeTab !== item.tab) {\n                e.target.style.background = 'rgba(0, 0, 0, 0.05)';\n                e.target.style.transform = 'translateX(4px)';\n                e.target.style.borderLeft = `3px solid ${globalStyles.currentTheme.primary}`;\n              }\n            },\n            onMouseLeave: e => {\n              if (activeTab !== item.tab) {\n                e.target.style.background = globalStyles.currentTheme.surface;\n                e.target.style.transform = 'translateX(0)';\n                e.target.style.borderLeft = '3px solid transparent';\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginRight: '12px'\n              },\n              children: item.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 710,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                flex: 1\n              },\n              children: item.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 711,\n              columnNumber: 17\n            }, this), item.subItems.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              onClick: e => {\n                e.stopPropagation();\n                toggleMenu(item.title);\n              },\n              children: expandedMenus[item.title] ? /*#__PURE__*/_jsxDEV(FiChevronDown, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 717,\n                columnNumber: 50\n              }, this) : /*#__PURE__*/_jsxDEV(FiChevronRight, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 717,\n                columnNumber: 70\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 713,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 684,\n            columnNumber: 15\n          }, this), item.subItems.length > 0 && expandedMenus[item.title] && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginLeft: '32px'\n            },\n            children: item.subItems.map((subItem, subIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                ...getStyle('sidebarItem'),\n                padding: '8px 16px 8px 32px',\n                fontSize: '14px',\n                opacity: 0.9\n              },\n              onClick: () => {\n                setActiveTab(item.tab);\n                setSidebarOpen(false);\n              },\n              onMouseEnter: e => {\n                e.target.style.background = 'rgba(0, 0, 0, 0.03)';\n                e.target.style.paddingLeft = '36px';\n                e.target.style.opacity = '1';\n              },\n              onMouseLeave: e => {\n                e.target.style.background = globalStyles.currentTheme.surface;\n                e.target.style.paddingLeft = '32px';\n                e.target.style.opacity = '0.9';\n              },\n              children: subItem.title\n            }, subIndex, false, {\n              fileName: _jsxFileName,\n              lineNumber: 725,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 723,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 683,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 681,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 680,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      style: getStyle('mainContent'),\n      children: [sidebarOpen && window.innerWidth < 768 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'fixed',\n          top: '64px',\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0,0,0,0.5)',\n          zIndex: 800\n        },\n        onClick: () => setSidebarOpen(false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 762,\n        columnNumber: 11\n      }, this), activeTab === \"dashboard\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '1rem',\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          minHeight: '100vh',\n          position: 'relative',\n          overflow: 'hidden'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: '10%',\n            left: '5%',\n            width: '300px',\n            height: '300px',\n            background: 'linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))',\n            borderRadius: '50%',\n            animation: 'float 6s ease-in-out infinite',\n            zIndex: 0\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 786,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: '60%',\n            right: '10%',\n            width: '200px',\n            height: '200px',\n            background: 'linear-gradient(45deg, rgba(255,255,255,0.08), rgba(255,255,255,0.03))',\n            borderRadius: '50%',\n            animation: 'float 8s ease-in-out infinite reverse',\n            zIndex: 0\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 797,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            bottom: '20%',\n            left: '15%',\n            width: '150px',\n            height: '150px',\n            background: 'linear-gradient(45deg, rgba(255,255,255,0.06), rgba(255,255,255,0.02))',\n            borderRadius: '50%',\n            animation: 'float 10s ease-in-out infinite',\n            zIndex: 0\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 808,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            maxWidth: '1400px',\n            margin: '0 auto',\n            position: 'relative',\n            zIndex: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.1))',\n              backdropFilter: 'blur(20px)',\n              borderRadius: '25px',\n              padding: '3rem',\n              marginBottom: '2rem',\n              color: 'white',\n              position: 'relative',\n              overflow: 'hidden',\n              border: '1px solid rgba(255,255,255,0.2)',\n              boxShadow: '0 25px 50px rgba(0,0,0,0.2)'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'absolute',\n                top: '20px',\n                right: '20px',\n                fontSize: '2rem',\n                animation: 'bounce 2s infinite'\n              },\n              children: \"\\uD83D\\uDE80\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 836,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'absolute',\n                bottom: '20px',\n                left: '20px',\n                fontSize: '1.5rem',\n                animation: 'bounce 3s infinite'\n              },\n              children: \"\\u2B50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 843,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'absolute',\n                top: '50%',\n                right: '10%',\n                fontSize: '1.2rem',\n                animation: 'bounce 4s infinite'\n              },\n              children: \"\\uD83D\\uDC8E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 850,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '2rem',\n                position: 'relative',\n                zIndex: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '120px',\n                  height: '120px',\n                  borderRadius: '50%',\n                  background: 'linear-gradient(135deg, #ff6b6b, #feca57)',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  fontSize: '3rem',\n                  fontWeight: 'bold',\n                  border: '4px solid rgba(255, 255, 255, 0.3)',\n                  boxShadow: '0 15px 35px rgba(0,0,0,0.2)',\n                  animation: 'pulse 3s infinite'\n                },\n                children: user ? user.email[0].toUpperCase() : '👤'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 859,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '3.5rem',\n                    fontWeight: 800,\n                    marginBottom: '1rem',\n                    background: 'linear-gradient(45deg, #fff, #f0f0f0)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '0 2px 4px rgba(0,0,0,0.3)'\n                  },\n                  children: [\"Hey \", user ? user.email.split('@')[0] : 'Champion', \"! \\uD83C\\uDFAF\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 876,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '1.3rem',\n                    opacity: 0.95,\n                    fontWeight: 500\n                  },\n                  children: \"Time to level up your skills and dominate your goals! \\uD83D\\uDCAA\\u2728\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 888,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: '1rem',\n                    display: 'flex',\n                    gap: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      background: 'rgba(255,255,255,0.2)',\n                      padding: '0.5rem 1rem',\n                      borderRadius: '20px',\n                      fontSize: '0.9rem',\n                      fontWeight: 600\n                    },\n                    children: \"\\uD83D\\uDD25 12 Day Streak\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 901,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      background: 'rgba(255,255,255,0.2)',\n                      padding: '0.5rem 1rem',\n                      borderRadius: '20px',\n                      fontSize: '0.9rem',\n                      fontWeight: 600\n                    },\n                    children: \"\\uD83C\\uDFC6 Level 15\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 910,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 896,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 875,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 858,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 823,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n              gap: '1.5rem',\n              marginBottom: '2rem'\n            },\n            children: [{\n              title: 'FIRE STREAK',\n              value: '12',\n              unit: 'DAYS',\n              icon: '🔥',\n              color: '#ff4757',\n              bgGradient: 'linear-gradient(135deg, #ff4757, #ff6b7a)',\n              glowColor: '#ff4757',\n              description: 'Unstoppable momentum!'\n            }, {\n              title: 'SKILL POINTS',\n              value: '2,847',\n              unit: 'XP',\n              icon: '⚡',\n              color: '#3742fa',\n              bgGradient: 'linear-gradient(135deg, #3742fa, #5352ed)',\n              glowColor: '#3742fa',\n              description: 'Level up achieved!'\n            }, {\n              title: 'POWER LEVEL',\n              value: '47',\n              unit: 'HOURS',\n              icon: '💪',\n              color: '#2ed573',\n              bgGradient: 'linear-gradient(135deg, #2ed573, #7bed9f)',\n              glowColor: '#2ed573',\n              description: 'Training complete!'\n            }, {\n              title: 'ACHIEVEMENTS',\n              value: '15',\n              unit: 'UNLOCKED',\n              icon: '🏆',\n              color: '#ffa502',\n              bgGradient: 'linear-gradient(135deg, #ffa502, #ffb142)',\n              glowColor: '#ffa502',\n              description: 'Champion status!'\n            }].map((stat, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: 'linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.1))',\n                backdropFilter: 'blur(20px)',\n                borderRadius: '20px',\n                padding: '2rem',\n                position: 'relative',\n                overflow: 'hidden',\n                border: '1px solid rgba(255,255,255,0.2)',\n                cursor: 'pointer',\n                transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',\n                boxShadow: `0 15px 35px rgba(0,0,0,0.1), 0 0 0 1px ${stat.glowColor}20`\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.transform = 'translateY(-10px) scale(1.02)';\n                e.currentTarget.style.boxShadow = `0 25px 50px rgba(0,0,0,0.2), 0 0 30px ${stat.glowColor}40`;\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.transform = 'translateY(0) scale(1)';\n                e.currentTarget.style.boxShadow = `0 15px 35px rgba(0,0,0,0.1), 0 0 0 1px ${stat.glowColor}20`;\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: '-50%',\n                  left: '-50%',\n                  width: '200%',\n                  height: '200%',\n                  background: `conic-gradient(from 0deg, transparent, ${stat.color}20, transparent)`,\n                  animation: 'rotate 20s linear infinite',\n                  opacity: 0.3\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 995,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: '10px',\n                  right: '10px',\n                  width: '80px',\n                  height: '80px',\n                  background: stat.bgGradient,\n                  borderRadius: '50%',\n                  opacity: 0.2,\n                  filter: 'blur(20px)'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1007,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'relative',\n                  zIndex: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    marginBottom: '1.5rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.8rem',\n                      fontWeight: 800,\n                      color: 'rgba(255,255,255,0.8)',\n                      letterSpacing: '2px'\n                    },\n                    children: stat.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1027,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '2rem',\n                      filter: 'drop-shadow(0 0 10px currentColor)'\n                    },\n                    children: stat.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1035,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1021,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'baseline',\n                    gap: '0.5rem',\n                    marginBottom: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '3rem',\n                      fontWeight: 900,\n                      color: 'white',\n                      textShadow: `0 0 20px ${stat.color}`,\n                      lineHeight: 1\n                    },\n                    children: stat.value\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1050,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.9rem',\n                      fontWeight: 600,\n                      color: stat.color,\n                      textTransform: 'uppercase',\n                      letterSpacing: '1px'\n                    },\n                    children: stat.unit\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1059,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1044,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '0.9rem',\n                    color: 'rgba(255,255,255,0.9)',\n                    fontWeight: 500,\n                    fontStyle: 'italic'\n                  },\n                  children: stat.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1071,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: '1rem',\n                    height: '4px',\n                    background: 'rgba(255,255,255,0.2)',\n                    borderRadius: '2px',\n                    overflow: 'hidden'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      height: '100%',\n                      width: `${Math.min(100, (index + 1) * 25)}%`,\n                      background: stat.bgGradient,\n                      borderRadius: '2px',\n                      animation: 'slideIn 2s ease-out'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1088,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1081,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1019,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 973,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 925,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.1))',\n              backdropFilter: 'blur(20px)',\n              borderRadius: '25px',\n              padding: '2rem',\n              marginBottom: '2rem',\n              border: '1px solid rgba(255,255,255,0.2)',\n              boxShadow: '0 25px 50px rgba(0,0,0,0.2)',\n              position: 'relative',\n              overflow: 'hidden'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'absolute',\n                top: '-100px',\n                right: '-100px',\n                width: '300px',\n                height: '300px',\n                background: 'conic-gradient(from 0deg, #ff4757, #3742fa, #2ed573, #ffa502, #ff4757)',\n                borderRadius: '50%',\n                opacity: 0.1,\n                animation: 'rotate 30s linear infinite'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1113,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              style: {\n                margin: '0 0 2rem 0',\n                fontSize: '2rem',\n                fontWeight: 800,\n                color: 'white',\n                textAlign: 'center',\n                textShadow: '0 2px 4px rgba(0,0,0,0.3)'\n              },\n              children: \"\\uD83C\\uDFAE MISSION CONTROL CENTER \\uD83C\\uDFAE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1125,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                gap: '1.5rem'\n              },\n              children: [{\n                icon: '🎯',\n                title: 'BATTLE MODE',\n                subtitle: 'Take Quiz Challenge',\n                desc: 'Test your skills in epic battles!',\n                action: () => setActiveTab('quizzes'),\n                color: '#ff4757',\n                bgGradient: 'linear-gradient(135deg, #ff4757, #ff6b7a)'\n              }, {\n                icon: '⚔️',\n                title: 'CODE ARENA',\n                subtitle: 'DSA Combat Zone',\n                desc: 'Sharpen your coding weapons!',\n                action: () => setActiveTab('dsa'),\n                color: '#3742fa',\n                bgGradient: 'linear-gradient(135deg, #3742fa, #5352ed)'\n              }, {\n                icon: '📜',\n                title: 'SCROLL REVIEW',\n                subtitle: 'Resume Enhancement',\n                desc: 'Upgrade your legendary resume!',\n                action: () => setActiveTab('resume'),\n                color: '#2ed573',\n                bgGradient: 'linear-gradient(135deg, #2ed573, #7bed9f)'\n              }, {\n                icon: '�',\n                title: 'KNOWLEDGE VAULT',\n                subtitle: 'Study Materials',\n                desc: 'Access ancient wisdom scrolls!',\n                action: () => setActiveTab('resources'),\n                color: '#ffa502',\n                bgGradient: 'linear-gradient(135deg, #ffa502, #ffb142)'\n              }].map((action, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: action.action,\n                style: {\n                  background: 'linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.05))',\n                  backdropFilter: 'blur(15px)',\n                  borderRadius: '20px',\n                  padding: '1.5rem',\n                  cursor: 'pointer',\n                  transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',\n                  border: `2px solid ${action.color}30`,\n                  position: 'relative',\n                  overflow: 'hidden',\n                  boxShadow: `0 10px 30px rgba(0,0,0,0.1), 0 0 0 1px ${action.color}20`\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.transform = 'translateY(-8px) scale(1.05)';\n                  e.currentTarget.style.boxShadow = `0 20px 40px rgba(0,0,0,0.2), 0 0 30px ${action.color}40`;\n                  e.currentTarget.style.borderColor = action.color + '80';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.transform = 'translateY(0) scale(1)';\n                  e.currentTarget.style.boxShadow = `0 10px 30px rgba(0,0,0,0.1), 0 0 0 1px ${action.color}20`;\n                  e.currentTarget.style.borderColor = action.color + '30';\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'absolute',\n                    top: '50%',\n                    left: '50%',\n                    width: '100px',\n                    height: '100px',\n                    background: action.bgGradient,\n                    borderRadius: '50%',\n                    transform: 'translate(-50%, -50%)',\n                    opacity: 0.1,\n                    filter: 'blur(30px)',\n                    animation: 'pulse 3s ease-in-out infinite'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1205,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'relative',\n                    zIndex: 1,\n                    textAlign: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '3rem',\n                      marginBottom: '1rem',\n                      filter: 'drop-shadow(0 0 10px currentColor)',\n                      animation: 'bounce 2s ease-in-out infinite'\n                    },\n                    children: action.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1220,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '1.1rem',\n                      fontWeight: 800,\n                      color: 'white',\n                      marginBottom: '0.5rem',\n                      textShadow: `0 0 10px ${action.color}`,\n                      letterSpacing: '1px'\n                    },\n                    children: action.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1229,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.8rem',\n                      color: action.color,\n                      fontWeight: 600,\n                      marginBottom: '0.75rem',\n                      textTransform: 'uppercase',\n                      letterSpacing: '0.5px'\n                    },\n                    children: action.subtitle\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1240,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.85rem',\n                      color: 'rgba(255,255,255,0.8)',\n                      fontStyle: 'italic',\n                      lineHeight: 1.4\n                    },\n                    children: action.desc\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1251,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginTop: '1rem',\n                      height: '3px',\n                      background: 'rgba(255,255,255,0.2)',\n                      borderRadius: '2px',\n                      overflow: 'hidden'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        height: '100%',\n                        width: `${75 + index * 5}%`,\n                        background: action.bgGradient,\n                        borderRadius: '2px',\n                        animation: 'slideIn 1.5s ease-out'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1268,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1261,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1219,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1179,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1136,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1102,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: '1fr 1fr',\n              gap: '2rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: globalStyles.currentTheme.surface,\n                borderRadius: '16px',\n                padding: '1.5rem',\n                boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n                border: `1px solid ${globalStyles.currentTheme.border}`\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: '0 0 1.5rem 0',\n                  fontSize: '1.3rem',\n                  fontWeight: 600,\n                  color: globalStyles.currentTheme.text\n                },\n                children: \"\\uD83D\\uDCC8 Recent Activity\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1297,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  maxHeight: '250px',\n                  overflowY: 'auto'\n                },\n                children: activityLog.slice(0, 5).map((log, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '1rem',\n                    padding: '0.75rem',\n                    borderRadius: '8px',\n                    marginBottom: '0.5rem',\n                    background: index % 2 === 0 ? globalStyles.currentTheme.secondary : 'transparent',\n                    transition: 'all 0.3s ease'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '10px',\n                      height: '10px',\n                      borderRadius: '50%',\n                      background: log.type === 'login' ? '#4ECDC4' : '#45B7D1',\n                      flexShrink: 0\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1321,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '0.9rem',\n                        fontWeight: 500,\n                        color: globalStyles.currentTheme.text,\n                        marginBottom: '0.2rem'\n                      },\n                      children: log.msg\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1329,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '0.8rem',\n                        color: globalStyles.currentTheme.textLight\n                      },\n                      children: new Date(log.date).toLocaleString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1337,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1328,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1311,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1306,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1290,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: globalStyles.currentTheme.surface,\n                borderRadius: '16px',\n                padding: '1.5rem',\n                boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n                border: `1px solid ${globalStyles.currentTheme.border}`\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: '0 0 1.5rem 0',\n                  fontSize: '1.3rem',\n                  fontWeight: 600,\n                  color: globalStyles.currentTheme.text\n                },\n                children: \"\\uD83D\\uDCC4 Resume Management\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1357,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  gap: '1rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.75rem',\n                    padding: '1rem',\n                    borderRadius: '12px',\n                    background: globalStyles.currentTheme.secondary,\n                    border: `2px dashed ${globalStyles.currentTheme.border}`,\n                    cursor: resumeUploadLoading ? 'not-allowed' : 'pointer',\n                    transition: 'all 0.3s ease'\n                  },\n                  onMouseEnter: e => {\n                    if (!resumeUploadLoading) {\n                      e.currentTarget.style.borderColor = globalStyles.currentTheme.primary;\n                      e.currentTarget.style.background = globalStyles.currentTheme.primary + '10';\n                    }\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.borderColor = globalStyles.currentTheme.border;\n                    e.currentTarget.style.background = globalStyles.currentTheme.secondary;\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(FiUpload, {\n                    size: 20,\n                    color: globalStyles.currentTheme.primary\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1388,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontWeight: 600,\n                        color: globalStyles.currentTheme.text,\n                        marginBottom: '0.2rem'\n                      },\n                      children: resumeUploadLoading ? 'Uploading...' : 'Upload Resume'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1390,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '0.8rem',\n                        color: globalStyles.currentTheme.textLight\n                      },\n                      children: \"PDF files only\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1397,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1389,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"file\",\n                    accept: \"application/pdf\",\n                    onChange: handleResumeUpload,\n                    disabled: resumeUploadLoading,\n                    style: {\n                      display: 'none'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1404,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1367,\n                  columnNumber: 21\n                }, this), resumeUrl && /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: resumeUrl,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.75rem',\n                    padding: '1rem',\n                    borderRadius: '12px',\n                    background: '#4ECDC4',\n                    color: 'white',\n                    textDecoration: 'none',\n                    fontWeight: 600,\n                    transition: 'all 0.3s ease'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.background = '#3DBDB6';\n                    e.currentTarget.style.transform = 'translateY(-2px)';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.background = '#4ECDC4';\n                    e.currentTarget.style.transform = 'translateY(0)';\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(FiFileText, {\n                    size: 20\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1439,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"View Resume\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1440,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1414,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1366,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1350,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1283,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 820,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 778,\n        columnNumber: 11\n      }, this), activeTab === \"resume\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px',\n          maxWidth: '1200px',\n          margin: '0 auto'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: getStyle('card'),\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              marginTop: 0,\n              color: '#333'\n            },\n            children: \"Career Assistant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1454,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              opacity: 0.8,\n              marginBottom: '24px',\n              color: '#666'\n            },\n            children: \"Get personalized resume advice and career guidance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1458,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              height: '50vh',\n              overflowY: 'auto',\n              marginBottom: '24px',\n              padding: '16px',\n              backgroundColor: '#f5f5f5',\n              border: '1px solid #e0e0e0',\n              borderRadius: '8px'\n            },\n            children: [messages.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                height: '100%',\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                justifyContent: 'center',\n                textAlign: 'center',\n                opacity: 0.7\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '48px',\n                  marginBottom: '16px'\n                },\n                children: \"\\uD83D\\uDCAC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1487,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: 0,\n                  color: '#333'\n                },\n                children: \"Start a conversation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1488,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: '#666'\n                },\n                children: \"Ask about resumes, interviews, or career advice\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1492,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1478,\n              columnNumber: 19\n            }, this) : messages.map((msg, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                ...(msg.role === 'user' ? getStyle('chatBubbleUser') : getStyle('chatBubbleBot')),\n                animation: 'fadeIn 0.3s ease'\n              },\n              children: msg.role === 'bot' ? /*#__PURE__*/_jsxDEV(ReactMarkdown, {\n                children: msg.content\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1506,\n                columnNumber: 25\n              }, this) : msg.content\n            }, idx, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1498,\n              columnNumber: 21\n            }, this)), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: getStyle('chatBubbleBot'),\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '8px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '10px',\n                    height: '10px',\n                    borderRadius: '50%',\n                    backgroundColor: '#1976d2',\n                    animation: 'pulse 1.4s infinite ease-in-out'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1516,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '10px',\n                    height: '10px',\n                    borderRadius: '50%',\n                    backgroundColor: '#1976d2',\n                    animation: 'pulse 1.4s infinite ease-in-out',\n                    animationDelay: '0.2s'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1523,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '10px',\n                    height: '10px',\n                    borderRadius: '50%',\n                    backgroundColor: '#1976d2',\n                    animation: 'pulse 1.4s infinite ease-in-out',\n                    animationDelay: '0.4s'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1531,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1515,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1514,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              ref: chatEndRef\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1542,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1467,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            style: {\n              display: 'flex',\n              gap: '12px'\n            },\n            onSubmit: e => {\n              e.preventDefault();\n              sendMessage();\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Type your message...\",\n              style: getStyle('inputField'),\n              value: input,\n              onChange: e => setInput(e.target.value),\n              disabled: loading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1553,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              style: {\n                ...getStyle('buttonPrimary'),\n                minWidth: '100px'\n              },\n              disabled: loading || !input.trim(),\n              children: loading ? 'Sending...' : 'Send'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1561,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1546,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1453,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1452,\n        columnNumber: 11\n      }, this), activeTab === \"dsa\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: getStyle('card'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: '16px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                style: {\n                  marginTop: 0,\n                  marginBottom: '8px'\n                },\n                children: \"\\uD83D\\uDE80 Company Wise DSA Questions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1583,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  opacity: 0.8,\n                  margin: 0\n                },\n                children: \"Explore DSA questions from top companies with enhanced filtering and favorites\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1584,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1582,\n              columnNumber: 17\n            }, this), showRevertButton && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: revertHeaderChanges,\n              style: {\n                ...getStyle('buttonPrimary'),\n                background: '#ff6b6b',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '8px',\n                fontSize: '14px',\n                padding: '8px 16px',\n                border: 'none',\n                borderRadius: '8px',\n                color: 'white',\n                cursor: 'pointer',\n                transition: 'all 0.3s ease'\n              },\n              onMouseEnter: e => e.target.style.background = '#ff5252',\n              onMouseLeave: e => e.target.style.background = '#ff6b6b',\n              children: [/*#__PURE__*/_jsxDEV(FiRefreshCw, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1608,\n                columnNumber: 21\n              }, this), \"Revert Header Color\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1589,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1581,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '8px',\n              marginBottom: '20px',\n              flexWrap: 'wrap',\n              borderBottom: '1px solid #eee',\n              paddingBottom: '16px'\n            },\n            children: ['all', ...Object.keys(companyCategories)].map(category => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSelectedCategory(category),\n              style: {\n                padding: '8px 16px',\n                borderRadius: '20px',\n                border: selectedCategory === category ? 'none' : '1px solid #ddd',\n                background: selectedCategory === category ? globalStyles.currentTheme.primary : 'transparent',\n                color: selectedCategory === category ? 'white' : '#666',\n                cursor: 'pointer',\n                fontSize: '14px',\n                fontWeight: selectedCategory === category ? 600 : 400,\n                transition: 'all 0.3s ease',\n                textTransform: 'capitalize'\n              },\n              onMouseEnter: e => {\n                if (selectedCategory !== category) {\n                  e.target.style.background = '#f5f5f5';\n                }\n              },\n              onMouseLeave: e => {\n                if (selectedCategory !== category) {\n                  e.target.style.background = 'transparent';\n                }\n              },\n              children: category === 'all' ? '🌟 All' : `${category === 'FAANG' ? '🔥' : category === 'Big Tech' ? '💻' : category === 'Startups' ? '🚀' : category === 'Finance' ? '💰' : category === 'Indian' ? '🇮🇳' : '🏢'} ${category}`\n            }, category, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1624,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1615,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '16px',\n              marginBottom: '24px',\n              flexWrap: 'wrap',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'relative',\n                flex: 1,\n                minWidth: '300px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  left: '16px',\n                  top: '50%',\n                  transform: 'translateY(-50%)',\n                  color: '#666'\n                },\n                children: /*#__PURE__*/_jsxDEV(FiSearch, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1678,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1671,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search companies...\",\n                style: {\n                  ...getStyle('inputField'),\n                  paddingLeft: '48px',\n                  width: '100%'\n                },\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1680,\n                columnNumber: 19\n              }, this), searchTerm && /*#__PURE__*/_jsxDEV(\"button\", {\n                style: {\n                  position: 'absolute',\n                  right: '16px',\n                  top: '50%',\n                  transform: 'translateY(-50%)',\n                  background: 'none',\n                  border: 'none',\n                  color: '#666',\n                  cursor: 'pointer'\n                },\n                onClick: () => setSearchTerm(\"\"),\n                children: /*#__PURE__*/_jsxDEV(FiX, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1705,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1692,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1670,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: sortBy,\n              onChange: e => setSortBy(e.target.value),\n              style: {\n                ...getStyle('inputField'),\n                width: 'auto',\n                minWidth: '150px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"name\",\n                children: \"\\uD83D\\uDCDD Sort by Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1720,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"favorites\",\n                children: \"\\u2B50 Favorites First\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1721,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1711,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1662,\n            columnNumber: 15\n          }, this), recentCompanies.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '24px',\n              padding: '16px',\n              borderRadius: '12px',\n              background: '#f8f9fa',\n              border: '1px solid #e9ecef'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '8px',\n                fontSize: '16px',\n                marginBottom: '12px',\n                color: '#333',\n                margin: '0 0 12px 0'\n              },\n              children: [/*#__PURE__*/_jsxDEV(FiClock, {\n                color: \"#666\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1743,\n                columnNumber: 21\n              }, this), \" Recently Viewed\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1734,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                gap: '8px',\n                flexWrap: 'wrap'\n              },\n              children: recentCompanies.map(company => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleCompanyClick(company),\n                style: {\n                  padding: '6px 12px',\n                  borderRadius: '16px',\n                  border: `1px solid ${globalStyles.currentTheme.primary}`,\n                  background: 'transparent',\n                  color: globalStyles.currentTheme.primary,\n                  cursor: 'pointer',\n                  fontSize: '12px',\n                  transition: 'all 0.3s ease'\n                },\n                onMouseEnter: e => {\n                  e.target.style.background = globalStyles.currentTheme.primary;\n                  e.target.style.color = 'white';\n                },\n                onMouseLeave: e => {\n                  e.target.style.background = 'transparent';\n                  e.target.style.color = globalStyles.currentTheme.primary;\n                },\n                children: company\n              }, company, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1751,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1745,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1727,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fill, minmax(220px, 1fr))',\n              gap: '16px',\n              marginTop: '24px'\n            },\n            children: getFilteredCompanies().map((company, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                ...getStyle('companyCard'),\n                position: 'relative',\n                transform: favoriteCompanies.includes(company) ? 'scale(1.02)' : 'scale(1)',\n                border: favoriteCompanies.includes(company) ? `2px solid ${globalStyles.currentTheme.primary}` : `1px solid ${globalStyles.currentTheme.border}`,\n                background: globalStyles.currentTheme.surface,\n                color: globalStyles.currentTheme.text,\n                animation: `fadeIn 0.3s ease ${index * 0.1}s both`,\n                boxShadow: `0 4px 6px ${globalStyles.currentTheme.shadow}`\n              },\n              onClick: () => handleCompanyClick(company),\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: e => toggleFavorite(company, e),\n                style: {\n                  position: 'absolute',\n                  top: '8px',\n                  right: '8px',\n                  background: 'none',\n                  border: 'none',\n                  cursor: 'pointer',\n                  color: favoriteCompanies.includes(company) ? '#ff6b6b' : '#ccc',\n                  transition: 'all 0.3s ease',\n                  fontSize: '18px'\n                },\n                children: /*#__PURE__*/_jsxDEV(FiHeart, {\n                  fill: favoriteCompanies.includes(company) ? 'currentColor' : 'none'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1819,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1805,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '56px',\n                  height: '56px',\n                  borderRadius: '50%',\n                  background: `linear-gradient(135deg, ${globalStyles.currentTheme.primary}, ${globalStyles.currentTheme.primaryDark})`,\n                  color: 'white',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  fontSize: '24px',\n                  fontWeight: 700,\n                  marginBottom: '12px',\n                  boxShadow: `0 4px 8px ${globalStyles.currentTheme.shadow}`\n                },\n                children: company.charAt(0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1823,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontWeight: 600,\n                  textAlign: 'center',\n                  fontSize: '14px',\n                  marginBottom: '8px'\n                },\n                children: company\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1841,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  fontSize: '12px',\n                  opacity: 0.7,\n                  marginTop: '8px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"\\uD83D\\uDCCA \", Math.floor(Math.random() * 50) + 10, \" Questions\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1858,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"\\u2B50 \", (Math.random() * 2 + 3).toFixed(1)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1859,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1851,\n                columnNumber: 21\n              }, this), Object.entries(companyCategories).map(([category, companies]) => {\n                if (companies.some(c => c.toLowerCase() === company.toLowerCase())) {\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      position: 'absolute',\n                      top: '8px',\n                      left: '8px',\n                      background: globalStyles.currentTheme.primary,\n                      color: 'white',\n                      padding: '2px 6px',\n                      borderRadius: '8px',\n                      fontSize: '10px',\n                      fontWeight: 600\n                    },\n                    children: category\n                  }, category, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1866,\n                    columnNumber: 27\n                  }, this);\n                }\n                return null;\n              })]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1788,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1781,\n            columnNumber: 15\n          }, this), getFilteredCompanies().length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '40px',\n              opacity: 0.7,\n              color: '#666'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '48px',\n                marginBottom: '16px'\n              },\n              children: \"\\uD83D\\uDD0D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1898,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                color: '#333'\n              },\n              children: \"No companies found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1899,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#666'\n              },\n              children: \"Try adjusting your search or category filter\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1900,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1892,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1579,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1578,\n        columnNumber: 11\n      }, this), activeTab === \"quizzes\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: getStyle('card'),\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              marginTop: 0\n            },\n            children: \"Career Quizzes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1911,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              opacity: 0.8,\n              marginBottom: '24px'\n            },\n            children: \"Test your knowledge with our career-focused quizzes!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1912,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',\n              gap: '16px'\n            },\n            children: quizButtons.map((quiz, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: getStyle('quizCard'),\n              onClick: () => openQuizLink(quiz.link),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  style: {\n                    margin: '0 0 8px 0'\n                  },\n                  children: quiz.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1928,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '14px',\n                    opacity: 0.8\n                  },\n                  children: quiz.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1929,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1927,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#1976d2'\n                },\n                children: /*#__PURE__*/_jsxDEV(FiExternalLink, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1938,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1937,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1922,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1916,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1910,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1909,\n        columnNumber: 11\n      }, this), activeTab === \"coding\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Coding, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1950,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1949,\n        columnNumber: 11\n      }, this), activeTab === \"resources\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: getStyle('card'),\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              marginTop: 0,\n              color: '#333'\n            },\n            children: \"Resources\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1956,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              opacity: 0.8,\n              marginBottom: '24px',\n              color: '#666'\n            },\n            children: \"Upload and manage your study materials and notes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1960,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '24px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                ...getStyle('buttonPrimary'),\n                background: '#f5f5f5',\n                color: '#333',\n                border: '1px solid #ddd',\n                cursor: resourceUploadLoading ? 'not-allowed' : 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(FiUpload, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1976,\n                columnNumber: 19\n              }, this), resourceUploadLoading ? 'Uploading...' : 'Upload Resource', /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                accept: \".pdf,.doc,.docx,.txt\",\n                onChange: handleResourceUpload,\n                disabled: resourceUploadLoading,\n                style: {\n                  display: 'none'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1978,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1969,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1968,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                marginBottom: '16px',\n                color: '#333'\n              },\n              children: \"Your Resources\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1989,\n              columnNumber: 17\n            }, this), userResources.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                opacity: 0.7,\n                color: '#666'\n              },\n              children: \"No resources uploaded yet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1994,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                backgroundColor: '#f5f5f5',\n                border: '1px solid #e0e0e0',\n                borderRadius: '8px',\n                padding: '16px'\n              },\n              children: userResources.map((file, idx) => {\n                const {\n                  data: urlData\n                } = supabase.storage.from('resources').getPublicUrl(`${user.id}/${file.name}`);\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    padding: '12px',\n                    borderBottom: '1px solid #eee',\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: '#333'\n                    },\n                    children: file.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2015,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: urlData.publicUrl,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    style: {\n                      color: '#1976d2',\n                      textDecoration: 'none',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '4px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(FiExternalLink, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2030,\n                      columnNumber: 29\n                    }, this), \"Open\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2018,\n                    columnNumber: 27\n                  }, this)]\n                }, idx, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2008,\n                  columnNumber: 25\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1999,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1988,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1955,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1954,\n        columnNumber: 11\n      }, this), activeTab === \"academics\" && /*#__PURE__*/_jsxDEV(Exams, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2042,\n        columnNumber: 39\n      }, this), activeTab === \"faq\" && /*#__PURE__*/_jsxDEV(Faq, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2043,\n        columnNumber: 33\n      }, this), activeTab === \"admin\" && (user === null || user === void 0 ? void 0 : user.email) === ADMIN_EMAIL && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: getStyle('card'),\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              marginTop: 0,\n              color: '#333'\n            },\n            children: \"Admin Panel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2047,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '16px',\n              marginBottom: '24px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                ...getStyle('buttonPrimary'),\n                background: adminTab === 'users' ? globalStyles.currentTheme.primary : 'transparent',\n                color: adminTab === 'users' ? 'white' : '#333',\n                border: '1px solid #ddd'\n              },\n              onClick: () => setAdminTab('users'),\n              children: \"Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2056,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                ...getStyle('buttonPrimary'),\n                background: adminTab === 'resources' ? globalStyles.currentTheme.primary : 'transparent',\n                color: adminTab === 'resources' ? 'white' : '#333',\n                border: '1px solid #ddd'\n              },\n              onClick: () => setAdminTab('resources'),\n              children: \"Resources\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2069,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2051,\n            columnNumber: 15\n          }, this), adminTab === 'users' && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                marginBottom: '16px',\n                color: '#333'\n              },\n              children: \"All Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2086,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                backgroundColor: '#f5f5f5',\n                border: '1px solid #e0e0e0',\n                borderRadius: '8px',\n                padding: '16px'\n              },\n              children: allUsers.map((user, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '12px',\n                  borderBottom: '1px solid #eee',\n                  color: '#333'\n                },\n                children: user.email\n              }, idx, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2097,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2090,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2085,\n            columnNumber: 17\n          }, this), adminTab === 'resources' && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                marginBottom: '16px',\n                color: '#333'\n              },\n              children: \"All Resources\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2111,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                opacity: 0.7,\n                color: '#666'\n              },\n              children: \"Resource management coming soon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2115,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2110,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2046,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2045,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 759,\n      columnNumber: 7\n    }, this), notification && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...getStyle('notification'),\n        backgroundColor: notification.type === 'error' ? '#f44336' : notification.type === 'success' ? '#4caf50' : '#2196f3',\n        color: 'white',\n        border: 'none'\n      },\n      children: notification.msg\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2128,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @keyframes fadeIn {\n          from { opacity: 0; transform: translateY(10px); }\n          to { opacity: 1; transform: translateY(0); }\n        }\n        @keyframes slideIn {\n          from { transform: translateX(100%); }\n          to { transform: translateX(0); }\n        }\n        @keyframes pulse {\n          0%, 100% { opacity: 1; }\n          50% { opacity: 0.5; }\n        }\n        @keyframes bounce {\n          0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }\n          40%, 43% { transform: translate3d(0,-8px,0); }\n          70% { transform: translate3d(0,-4px,0); }\n          90% { transform: translate3d(0,-2px,0); }\n        }\n        @keyframes glow {\n          0%, 100% { box-shadow: 0 0 5px rgba(99, 102, 241, 0.3); }\n          50% { box-shadow: 0 0 20px rgba(99, 102, 241, 0.6); }\n        }\n        @keyframes shimmer {\n          0% { background-position: -200px 0; }\n          100% { background-position: calc(200px + 100%) 0; }\n        }\n\n        /* Enhanced hover effects */\n        .company-card:hover {\n          animation: bounce 0.6s ease;\n        }\n\n        .favorite-btn:hover {\n          animation: pulse 0.5s ease;\n        }\n\n        /* Smooth transitions for all interactive elements */\n        button, input, select {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        button:hover {\n          transform: translateY(-1px);\n        }\n\n        * {\n          box-sizing: border-box;\n        }\n        body {\n          margin: 0;\n          font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2140,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 588,\n    columnNumber: 5\n  }, this);\n};\n_s(EduAIChatBot, \"MSmmmuTzUQVwUUvf0Zrq6XMFMtU=\", false, function () {\n  return [usePerformanceMonitor, useResponsive, useSidebar, useOptimizedAnimation];\n});\n_c1 = EduAIChatBot;\nexport default EduAIChatBot;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1;\n$RefreshReg$(_c, \"LazyFaq$React.lazy\");\n$RefreshReg$(_c2, \"LazyFaq\");\n$RefreshReg$(_c3, \"LazyExams$React.lazy\");\n$RefreshReg$(_c4, \"LazyExams\");\n$RefreshReg$(_c5, \"LazyCoding$React.lazy\");\n$RefreshReg$(_c6, \"LazyCoding\");\n$RefreshReg$(_c7, \"LazyChart$React.lazy\");\n$RefreshReg$(_c8, \"LazyChart\");\n$RefreshReg$(_c9, \"LazyMarkdown$React.lazy\");\n$RefreshReg$(_c0, \"LazyMarkdown\");\n$RefreshReg$(_c1, \"EduAIChatBot\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "useMemo", "Suspense", "getDoc", "doc", "auth", "db", "axios", "sidebarItems", "onAuthStateChanged", "<PERSON><PERSON><PERSON>", "Sidebar", "Dashboard", "Notification", "useResponsive", "useSidebar", "useOptimizedAnimation", "usePerformanceMonitor", "getTheme", "FiFileText", "FiCode", "FiHelpCircle", "FiAward", "FiBook", "FiUser", "FiShield", "FiSearch", "FiUpload", "FiBriefcase", "FiBarChart2", "FiLayers", "FiCheckCircle", "FiExternalLink", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "FiRefreshCw", "createClient", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "LazyFaq", "lazy", "_c", "_c2", "LazyExams", "_c3", "_c4", "LazyCoding", "_c5", "_c6", "<PERSON><PERSON><PERSON><PERSON>", "_c7", "Bar", "Chart", "BarElement", "CategoryScale", "LinearScale", "<PERSON><PERSON><PERSON>", "Legend", "register", "default", "_c8", "LazyMarkdown", "_c9", "_c0", "updatedSidebarItems", "map", "item", "iconMap", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "icon", "tab", "title", "toLowerCase", "EduAIChatBot", "_s", "metrics", "startRender", "endRender", "isMobile", "isTablet", "isOpen", "sidebarOpen", "toggle", "toggleSidebar", "close", "closeSidebar", "shouldAnimate", "input", "setInput", "messages", "setMessages", "userId", "setUserId", "loading", "setLoading", "knowledge", "setKnowledge", "activeTab", "setActiveTab", "searchTerm", "setSearchTerm", "expandedMenus", "setExpandedMenus", "user", "setUser", "isDarkMode", "setIsDarkMode", "resumeUploadLoading", "setResumeUploadLoading", "resumeUrl", "setResumeUrl", "resourceUploadLoading", "setResourceUploadLoading", "userResources", "ADMIN_EMAIL", "allUsers", "adminTab", "setAdminTab", "notification", "setNotification", "activityLog", "setActivityLog", "chatEndRef", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "sortBy", "setSortBy", "favoriteCompanies", "setFavoriteCompanies", "recentCompanies", "setRecentCompanies", "showRevertButton", "setShowRevertButton", "API_KEY", "SUPABASE_URL", "SUPABASE_ANON_KEY", "supabase", "companyCategories", "companies", "quizButtons", "description", "link", "styles", "globalStyles", "appContainer", "backgroundColor", "currentTheme", "background", "color", "text", "navbar", "navbarFixed", "borderBottom", "border", "sidebar", "sidebarFixed", "surface", "borderRight", "transform", "sidebarItem", "sidebarItemEdu", "transition", "primary", "sidebarItemActive", "sidebarItemActiveEdu", "mainContent", "mainContentEdu", "marginLeft", "minHeight", "card", "cardEdu", "boxShadow", "shadow", "buttonPrimary", "inputField", "borderColor", "outline", "chatBubbleUser", "chatBubbleBot", "secondary", "companyCard", "companyCardEdu", "quizCard", "quizCardEdu", "getStyle", "styleName", "hover", "baseStyle", "unsubscribe", "uid", "console", "log", "fetchUserProfile", "userRef", "userDoc", "exists", "userData", "data", "dp", "fetch", "then", "res", "catch", "err", "error", "getSession", "session", "listener", "onAuthStateChange", "_event", "subscription", "handleResumeUpload", "e", "file", "target", "files", "filePath", "id", "name", "storage", "from", "upload", "upsert", "urlData", "getPublicUrl", "publicUrl", "showNotification", "logActivity", "handleResourceUpload", "handleCompanyClick", "company", "prev", "filtered", "filter", "c", "slice", "window", "location", "href", "formattedCompany", "replace", "toggleFavorite", "stopPropagation", "includes", "revertHeaderChanges", "eduNovaElement", "document", "querySelector", "subtitleElement", "style", "getFilteredCompanies", "categoryCompanies", "some", "catCompany", "sort", "a", "b", "aFav", "bFav", "localeCompare", "openQuizLink", "url", "open", "sendMessage", "trim", "userMessage", "role", "content", "_res$data$candidates", "_res$data$candidates$", "_res$data$candidates$2", "_res$data$candidates$3", "_res$data$candidates$4", "prompt", "post", "contents", "parts", "headers", "botReply", "candidates", "botMessage", "message", "handleLogout", "signOut", "msg", "type", "setTimeout", "date", "Date", "toISOString", "toggleMenu", "menu", "current", "scrollIntoView", "behavior", "getLast7Days", "days", "i", "d", "setDate", "getDate", "push", "toLocaleDateString", "chartLabels", "chartData", "labels", "datasets", "label", "day", "startsWith", "length", "chartOptions", "responsive", "plugins", "legend", "position", "tooltip", "enabled", "scales", "y", "beginAtZero", "ticks", "stepSize", "children", "marginRight", "cursor", "padding", "borderRadius", "onClick", "setSidebarOpen", "onMouseEnter", "onMouseLeave", "FiX", "size", "FiMenu", "flex", "display", "alignItems", "src", "require", "alt", "height", "fontWeight", "fontSize", "opacity", "gap", "width", "justifyContent", "<PERSON><PERSON>ilter", "email", "toUpperCase", "FiLogOut", "FiLogIn", "index", "borderLeft", "subItems", "FiChevronDown", "FiChevronRight", "subItem", "subIndex", "paddingLeft", "innerWidth", "top", "left", "right", "bottom", "zIndex", "overflow", "animation", "max<PERSON><PERSON><PERSON>", "margin", "marginBottom", "WebkitBackgroundClip", "WebkitTextFillColor", "textShadow", "split", "marginTop", "gridTemplateColumns", "value", "unit", "bgGradient", "glowColor", "stat", "currentTarget", "letterSpacing", "lineHeight", "textTransform", "fontStyle", "Math", "min", "textAlign", "subtitle", "desc", "action", "maxHeight", "overflowY", "flexShrink", "textLight", "toLocaleString", "flexDirection", "accept", "onChange", "disabled", "rel", "textDecoration", "idx", "ReactMarkdown", "animationDelay", "ref", "onSubmit", "preventDefault", "placeholder", "min<PERSON><PERSON><PERSON>", "flexWrap", "paddingBottom", "Object", "keys", "category", "fill", "primaryDark", "char<PERSON>t", "floor", "random", "toFixed", "entries", "quiz", "Coding", "<PERSON><PERSON>", "Faq", "_c1", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/src/EduAIChatBot.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback, useMemo, Suspense } from \"react\";\nimport { getDoc, doc } from 'firebase/firestore';\nimport { auth, db } from './firebaseConfig';\nimport axios from \"axios\";\nimport { sidebarItems } from './sidebarItems';\nimport { onAuthStateChanged } from 'firebase/auth';\nimport { Navbar, Sidebar, Dashboard, Notification } from './components';\nimport { useResponsive, useSidebar, useOptimizedAnimation, usePerformanceMonitor } from './hooks/useResponsive';\nimport { getTheme } from './theme';\nimport {\n  FiFileText, FiCode, FiHelpCircle, FiAward, FiBook, FiUser, FiShield,\n  FiSearch, FiUpload, FiBriefcase, FiBarChart2, FiLayers, FiCheckCircle,\n  FiExternalLink, FiHeart, FiClock, FiRefreshCw\n} from \"react-icons/fi\";\nimport { createClient } from '@supabase/supabase-js';\nimport './App.css';\nimport './components.css';\n\n// Lazy load heavy components for better performance\nconst LazyFaq = React.lazy(() => import('./Faq'));\nconst LazyExams = React.lazy(() => import('./Exams'));\nconst LazyCoding = React.lazy(() => import('./Coding'));\n\n// Chart.js lazy loading with proper registration\nconst LazyChart = React.lazy(async () => {\n  const { Bar } = await import('react-chartjs-2');\n  const { Chart, BarElement, CategoryScale, LinearScale, Tooltip, Legend } = await import('chart.js');\n  Chart.register(BarElement, CategoryScale, LinearScale, Tooltip, Legend);\n  return { default: Bar };\n});\n\nconst LazyMarkdown = React.lazy(() => import('react-markdown'));\n\n// Enhanced sidebar items with icons\nconst updatedSidebarItems = sidebarItems.map(item => {\n  const iconMap = {\n    \"resume\": <FiFileText />,\n    \"dsa\": <FiCode />,\n    \"coding\": <FiLayers />,\n    \"resources\": <FiBriefcase />,\n    \"quizzes\": <FiCheckCircle />,\n    \"aptitude\": <FiBarChart2 />,\n    \"academics\": <FiBook />,\n    \"faq\": <FiHelpCircle />,\n    \"admin\": <FiShield />\n  };\n\n  return {\n    ...item,\n    icon: iconMap[item.tab] || iconMap[item.title.toLowerCase()] || <FiAward />\n  };\n});\n\nconst EduAIChatBot = () => {\n  // Performance monitoring\n  const { metrics, startRender, endRender } = usePerformanceMonitor();\n  const { isMobile, isTablet } = useResponsive();\n  const { isOpen: sidebarOpen, toggle: toggleSidebar, close: closeSidebar } = useSidebar();\n  const { shouldAnimate } = useOptimizedAnimation();\n\n  // Optimized state declarations with proper initial values\n  const [input, setInput] = useState(\"\");\n  const [messages, setMessages] = useState([]);\n  const [userId, setUserId] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [knowledge, setKnowledge] = useState(\"\");\n  const [activeTab, setActiveTab] = useState(\"dashboard\");\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [expandedMenus, setExpandedMenus] = useState({});\n  const [user, setUser] = useState(null);\n  const [isDarkMode, setIsDarkMode] = useState(false);\n\n  const [resumeUploadLoading, setResumeUploadLoading] = useState(false);\n  const [resumeUrl, setResumeUrl] = useState(null);\n  const [resourceUploadLoading, setResourceUploadLoading] = useState(false);\n  const [userResources] = useState([]);\n  const ADMIN_EMAIL = '<EMAIL>';\n  const [allUsers] = useState([]);\n  const [adminTab, setAdminTab] = useState('users');\n  const [notification, setNotification] = useState(null);\n  const [activityLog, setActivityLog] = useState([]);\n  const chatEndRef = useRef(null);\n\n  // Enhanced DSA section states\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [sortBy, setSortBy] = useState('name');\n  const [favoriteCompanies, setFavoriteCompanies] = useState([]);\n  const [recentCompanies, setRecentCompanies] = useState([]);\n  const [showRevertButton, setShowRevertButton] = useState(true);\n\n  // API configurations\n  const API_KEY = \"AIzaSyC6kHWto78QdqHz7Uu9RzEXb443ZO7tG5M\";\n  const SUPABASE_URL = 'https://gziaptswfepiveyylven.supabase.co';\n  const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imd6aWFwdHN3ZmVwaXZleXlsdmVuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU2NzczNTYsImV4cCI6MjA2MTI1MzM1Nn0.wmqXZGffrox8E_PuCwbzh4xJEffsvFmZCVcF6WFAX6Q';\n  const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);\n\n  // Company categories for enhanced DSA section\n  const companyCategories = {\n    'FAANG': ['Facebook', 'Apple', 'Amazon', 'Netflix', 'Google'],\n    'Big Tech': ['Microsoft', 'Adobe', 'Salesforce', 'Oracle', 'IBM', 'Intel', 'Nvidia'],\n    'Startups': ['Stripe', 'Airbnb', 'Uber', 'Lyft', 'DoorDash', 'Instacart', 'Coinbase'],\n    'Finance': ['Goldman Sachs', 'JPMorgan', 'Morgan Stanley', 'BlackRock', 'Citadel', 'Two Sigma'],\n    'Consulting': ['McKinsey', 'BCG', 'Bain', 'Deloitte', 'Accenture', 'PwC'],\n    'E-commerce': ['Amazon', 'eBay', 'Shopify', 'Etsy', 'Wayfair', 'Booking.com'],\n    'Gaming': ['Blizzard', 'Epic Games', 'Riot Games', 'Unity', 'Roblox'],\n    'Indian': ['TCS', 'Infosys', 'Wipro', 'HCL', 'Flipkart', 'Paytm', 'Zomato', 'Swiggy']\n  };\n\n  // Complete list of companies\n  const companies = [\n     \"Accenture\", \"Accolite\", \"Adobe\", \"Affirm\", \"Agoda\", \"Airbnb\", \"Airtel\",\n    \"Akamar\", \"Akuna Capital\", \"Alibaba\", \"Altimetrik\", \"Amazon\", \"AMD\",\n    \"Amdocs\", \"American Express\", \"Anduril\", \"Apple\", \"Arista Networks\",\n    \"Arcesium\", \"Atlassian\", \"Attentive\", \"athenahealth\", \"Autodesk\",\n    \"Avito\", \"Baidu\", \"Barclays\", \"BitGo\", \"BlackRock\", \"Blizzard\",\n    \"Block\", \"Bloomberg\", \"BNY Mellon\", \"Boft\", \"Booking.com\", \"Bos\",\n    \"BP\", \"ByteDance\", \"Cadence\", \"Capgemini\", \"Capital One\", \"CARS24\",\n    \"carwale\", \"Cashfree\", \"Chewy\", \"Cisco\", \"Citadel\", \"Citrix\",\n    \"Cloudera\", \"Cloudflare\", \"Cognizant\", \"Coinbase\", \"Commvault\",\n    \"Confluent\", \"Coupang\", \"Coursera\", \"CrowdStrike\", \"Cruise\",\n    \"Curefit\", \"Databricks\", \"Datadog\", \"DE Shaw\", \"Deloitte\", \"Dell\",\n    \"Deliveroo\", \"Derantior\", \"Deutsche Bank\", \"Devflev\", \"Directi\",\n    \"Disney\", \"Docusign\", \"DoorDash\", \"Dream11\", \"Dropbox\", \"DRW\",\n    \"Dunzo\", \"eBay\", \"EPAM Systems\", \"Epic Systems\", \"Expedia\",\n    \"FactSet\", \"Flexport\", \"Flipkart\", \"Freshworks\", \"GE Healthcare\",\n    \"Geico\", \"Goldman Sachs\", \"Google\", \"Grab\", \"Grammarly\", \"Graviton\",\n    \"Groww\", \"GSN Games\", \"Hashedin\", \"HCL\", \"HPE\", \"Hubspot\",\n    \"Hudson River Trading\", \"Huawei\", \"IBM\", \"IMC\", \"Indeed\", \"Infosys\",\n    \"InMobi\", \"Intel\", \"Intuit\", \"JPMorgan\", \"Jane Street\",\n    \"Josh Technology\", \"Jump Trading\", \"Juspay\", \"Karat\", \"KLA\",\n    \"LinkedIn\", \"LiveRamp\", \"Lowe's\", \"Lucid\", \"Lyft\", \"MakeMyTrip\",\n    \"Mastercard\", \"MathWorks\", \"Media.net\", \"Meesho\", \"Mercari\", \"Meta\",\n    \"Microsoft\", \"Millennium\", \"Mitsogo\", \"Moloco\", \"MongoDB\",\n    \"Morgan Stanley\", \"Moveworks\", \"Myntra\", \"Nagarro\", \"NetApp\",\n    \"Netflix\", \"Nextdoor\", \"Nielsen\", \"Nike\", \"Niantic\", \"Nordstrom\",\n    \"Nutanix\", \"Nvidia\", \"Okta\", \"OKX\", \"OpenAI\", \"OpenText\", \"Oracle\",\n    \"Otter.ai\", \"Oyo\", \"Ozon\", \"Palantir Technologies\", \"Palo Alto Networks\",\n    \"PayPal\", \"Paytm\", \"Persistent Systems\", \"PhonePe\", \"Pinterest\",\n    \"Pocket Gems\", \"Point72\", \"Pure Storage\", \"Qualcomm\", \"Quora\",\n    \"Rakuten\", \"Razorpay\", \"RBC\", \"Reddit\", \"Revolut\", \"Robinhood\",\n    \"Roblox\", \"Rubrik\", \"Salesforce\", \"Samsung\", \"SAP\", \"ServiceNow\",\n    \"Shopify\", \"Siemens\", \"Sigmoid\", \"SIG\", \"Snowflake\", \"Snap\", \"Sofi\",\n    \"Splunk\", \"Spotify\", \"Sprinklr\", \"Squarepoint Capital\", \"Stripe\",\n    \"Swiggy\", \"TCS\", \"Tekion\", \"Tencent\", \"Tesla\", \"ThoughtSpot\",\n    \"ThoughtWorks\", \"TikTok\", \"Tinkoff\", \"Trilogy\", \"Turing\", \"Turo\",\n    \"Twilio\", \"Twitch\", \"Two Sigma\", \"Uber\", \"UiPath\", \"UKG\",\n    \"Veeva Systems\", \"Verily\", \"Verkada\", \"Virtu Financial\", \"Visa\",\n    \"VK\", \"VMware\", \"Walmart Labs\", \"WarnerMedia\", \"Wayfair\",\n    \"Wells Fargo\", \"Wipro\", \"Wix\", \"Workday\", \"X\", \"Yahoo\", \"Yandex\",\n    \"Yelp\", \"Zalando\", \"Zenefits\", \"Zepto\", \"Zeta\", \"Zillow\", \"Zoho\",\n    \"Zomato\", \"ZScaler\", \"Zopsmart\"\n  ];\n\n  // Quiz buttons data\n  const quizButtons = [\n    {\n      title: \"OP and CN Quiz\",\n      description: \"Test your knowledge of Operating System and Computer Networks\",\n      link: \"https://opcn.netlify.app\",\n    },\n    {\n      title: \"OOPs and DBMS Quiz\",\n      description: \"Challenge yourself with oops and dbms\",\n      link: \"https://oopsanddbms.netlify.app/\",\n    },\n    {\n      title: \"System Design Quiz\",\n      description: \"Test your system design knowledge\",\n      link: \"https://system-design041.netlify.app\",\n    },\n    {\n      title: \"Quantitative Aptitude and Reasoning Quiz\",\n      description: \"Practice common quant and reasoning questions\",\n      link: \"https://quantandreasoning.netlify.app\",\n    },\n    {\n      title: \"Cloud & DevOps Quiz\",\n      description: \"Test your knowledge of Cloud and DevOps concepts\",\n      link: \"https://cloud-devops.netlify.app\",\n    },\n    {\n      title: \"DSA Quiz\",\n      description: \"Data Structures and Algorithms quiz\",\n      link: \"https://dsa041.netlify.app\",\n    },\n    {\n      title: \"Operating System & Computer Networks Quiz\",\n      description: \"Quiz on OS and Computer Networks\",\n      link: \"https://opcn.netlify.app\",\n    },\n     {\n      title: \"Web Development Quiz\",\n      description: \"Quiz on Web Development topics\",\n      link: \"https://web-dev041.netlify.app\",\n\n    },\n  ];\n\n  // Use centralized styles\n  const styles = {\n    ...globalStyles,\n    appContainer: {\n      ...globalStyles.appContainer,\n      backgroundColor: globalStyles.currentTheme.background,\n      color: globalStyles.currentTheme.text,\n    },\n    navbar: {\n      ...globalStyles.navbarFixed,\n      borderBottom: `1px solid ${globalStyles.currentTheme.border}`\n    },\n    sidebar: {\n      ...globalStyles.sidebarFixed,\n      backgroundColor: globalStyles.currentTheme.surface,\n      borderRight: `1px solid ${globalStyles.currentTheme.border}`,\n      transform: sidebarOpen ? 'translateX(0)' : 'translateX(-100%)',\n    },\n    sidebarItem: {\n      ...globalStyles.sidebarItemEdu,\n      color: globalStyles.currentTheme.text,\n      background: globalStyles.currentTheme.surface,\n      border: `1px solid ${globalStyles.currentTheme.border}`,\n      transition: 'all 0.3s ease',\n      '&:hover': {\n        background: globalStyles.currentTheme.primary,\n        color: 'white'\n      }\n    },\n    sidebarItemActive: {\n      ...globalStyles.sidebarItemActiveEdu,\n      color: 'white',\n      background: globalStyles.currentTheme.primary,\n      border: `1px solid ${globalStyles.currentTheme.primary}`,\n    },\n    mainContent: {\n      ...globalStyles.mainContentEdu,\n      marginLeft: sidebarOpen ? '280px' : '0',\n      backgroundColor: globalStyles.currentTheme.background,\n      color: globalStyles.currentTheme.text,\n      minHeight: '100vh'\n    },\n    card: {\n      ...globalStyles.cardEdu,\n      backgroundColor: globalStyles.currentTheme.surface,\n      color: globalStyles.currentTheme.text,\n      border: `1px solid ${globalStyles.currentTheme.border}`,\n      boxShadow: `0 4px 6px ${globalStyles.currentTheme.shadow}`\n    },\n    buttonPrimary: {\n      ...globalStyles.buttonPrimary,\n    },\n    inputField: {\n      ...globalStyles.inputField,\n      backgroundColor: '#fff',\n      color: '#333',\n      border: '1px solid #ddd',\n      '&:focus': {\n        borderColor: globalStyles.currentTheme.primary,\n        outline: 'none',\n        boxShadow: `0 0 0 2px ${globalStyles.currentTheme.primary}20`\n      }\n    },\n    chatBubbleUser: {\n      ...globalStyles.chatBubbleUser,\n      backgroundColor: globalStyles.currentTheme.primary,\n      color: 'white'\n    },\n    chatBubbleBot: {\n      ...globalStyles.chatBubbleBot,\n      backgroundColor: globalStyles.currentTheme.secondary,\n      color: globalStyles.currentTheme.text,\n      border: '1px solid transparent'\n    },\n    companyCard: {\n      ...globalStyles.companyCardEdu,\n    },\n    quizCard: {\n      ...globalStyles.quizCardEdu,\n      backgroundColor: globalStyles.currentTheme.surface,\n    },\n    notification: {\n      ...globalStyles.notification,\n    }\n  };\n\n  // Helper function to apply styles with hover states\n  const getStyle = (styleName, hover = false) => {\n    const baseStyle = styles[styleName];\n    if (typeof baseStyle === 'function') return baseStyle();\n    if (hover && baseStyle['&:hover']) {\n      return { ...baseStyle, ...baseStyle['&:hover'] };\n    }\n    return baseStyle;\n  };\n\n  // Fetch user profile\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, (user) => {\n      if (user) {\n        setUserId(user.uid);\n      } else {\n        console.log('User is not authenticated');\n        setLoading(false);\n      }\n    });\n    return () => unsubscribe();\n  }, []);\n\n  useEffect(() => {\n    if (userId) {\n      const fetchUserProfile = async () => {\n        const userRef = doc(db, \"users\", userId);\n        const userDoc = await getDoc(userRef);\n\n        if (userDoc.exists()) {\n          const userData = userDoc.data();\n          // Profile pic functionality can be added later\n          console.log(\"User data loaded:\", userData.dp);\n        } else {\n          console.log(\"No such user!\");\n        }\n        setLoading(false);\n      };\n      fetchUserProfile();\n    }\n  }, [userId]);\n\n  // Fetch training data\n  useEffect(() => {\n    fetch(\"/training-data.txt\")\n      .then((res) => res.text())\n      .then((data) => setKnowledge(data))\n      .catch((err) => console.error(\"Failed to load training data:\", err));\n  }, []);\n\n  // Supabase auth state\n  useEffect(() => {\n    supabase.auth.getSession().then(({ data: { session } }) => {\n      setUser(session?.user || null);\n    });\n    const { data: listener } = supabase.auth.onAuthStateChange((_event, session) => {\n      setUser(session?.user || null);\n    });\n    return () => {\n      listener?.subscription.unsubscribe();\n    };\n  }, [supabase.auth]);\n\n  // Handle resume upload\n  const handleResumeUpload = async (e) => {\n    const file = e.target.files[0];\n    if (!file || !user) return;\n    setResumeUploadLoading(true);\n    const filePath = `${user.id}/${file.name}`;\n    const { error } = await supabase.storage.from('resumes').upload(filePath, file, { upsert: true });\n    if (!error) {\n      const { data: urlData } = supabase.storage.from('resumes').getPublicUrl(filePath);\n      setResumeUrl(urlData.publicUrl);\n      showNotification('Resume uploaded successfully!', 'success');\n      logActivity('Uploaded a resume');\n    } else {\n      showNotification('Resume upload failed.', 'error');\n    }\n    setResumeUploadLoading(false);\n  };\n\n  // Handle resource upload\n  const handleResourceUpload = async (e) => {\n    const file = e.target.files[0];\n    if (!file || !user) return;\n    setResourceUploadLoading(true);\n    const filePath = `${user.id}/${file.name}`;\n    const { error } = await supabase.storage.from('resources').upload(filePath, file, { upsert: true });\n    if (!error) {\n      showNotification('Resource uploaded!', 'success');\n      logActivity(`Uploaded resource: ${file.name}`);\n    } else {\n      showNotification('Resource upload failed.', 'error');\n    }\n    setResourceUploadLoading(false);\n  };\n\n  // Enhanced company click handler\n  const handleCompanyClick = (company) => {\n    // Add to recent companies\n    setRecentCompanies(prev => {\n      const filtered = prev.filter(c => c !== company);\n      return [company, ...filtered].slice(0, 5); // Keep only 5 recent\n    });\n\n    logActivity(`Viewed ${company} DSA questions`);\n\n    if (company.toLowerCase() === 'microsoft') {\n      window.location.href = '/company-dsa/Microsoft_questions.html';\n      return;\n    }\n    const formattedCompany = company.replace(/\\s+/g, '');\n    window.location.href = `/company-dsa/${formattedCompany}.html`;\n  };\n\n  // Toggle favorite company\n  const toggleFavorite = (company, e) => {\n    e.stopPropagation(); // Prevent company click\n    setFavoriteCompanies(prev => {\n      if (prev.includes(company)) {\n        return prev.filter(c => c !== company);\n      } else {\n        return [...prev, company];\n      }\n    });\n  };\n\n  // Revert header color changes\n  const revertHeaderChanges = () => {\n    setShowRevertButton(false);\n    showNotification('Header text color reverted to theme default!', 'success');\n\n    // Actually revert the header text color by updating the DOM\n    const eduNovaElement = document.querySelector('[data-header-title]');\n    const subtitleElement = document.querySelector('[data-header-subtitle]');\n\n    if (eduNovaElement) {\n      eduNovaElement.style.color = '#333';\n    }\n    if (subtitleElement) {\n      subtitleElement.style.color = '#333';\n    }\n  };\n\n  // Get filtered companies based on category and search\n  const getFilteredCompanies = () => {\n    let filtered = companies;\n\n    // Filter by category\n    if (selectedCategory !== 'all') {\n      const categoryCompanies = companyCategories[selectedCategory] || [];\n      filtered = filtered.filter(company =>\n        categoryCompanies.some(catCompany =>\n          company.toLowerCase().includes(catCompany.toLowerCase()) ||\n          catCompany.toLowerCase().includes(company.toLowerCase())\n        )\n      );\n    }\n\n    // Filter by search term\n    filtered = filtered.filter(company =>\n      company.toLowerCase().includes(searchTerm.toLowerCase())\n    );\n\n    // Sort companies\n    if (sortBy === 'name') {\n      filtered.sort();\n    } else if (sortBy === 'favorites') {\n      filtered.sort((a, b) => {\n        const aFav = favoriteCompanies.includes(a);\n        const bFav = favoriteCompanies.includes(b);\n        if (aFav && !bFav) return -1;\n        if (!aFav && bFav) return 1;\n        return a.localeCompare(b);\n      });\n    }\n\n    return filtered;\n  };\n\n  // Open quiz link\n  const openQuizLink = (url) => {\n    window.open(url, \"_blank\");\n  };\n\n  // Send message to chatbot\n  const sendMessage = async () => {\n    if (!input.trim()) return;\n\n    const userMessage = { role: \"user\", content: input };\n    setMessages((prev) => [...prev, userMessage]);\n    setInput(\"\");\n    setLoading(true);\n\n    try {\n      const prompt = `You are a resume assistant. Help users improve their resumes, provide suggestions, and answer career-related questions. Use the following knowledge if it helps. If it's not relevant, use your own intelligence.\\n\\nKnowledge:\\n${knowledge}\\n\\nQuestion: ${input}`;\n\n      const res = await axios.post(\n        `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEY}`,\n        {\n          contents: [\n            {\n              parts: [{ text: prompt }],\n            },\n          ],\n        },\n        {\n          headers: {\n            \"Content-Type\": \"application/json\",\n          },\n        }\n      );\n\n      const botReply =\n        res.data.candidates?.[0]?.content?.parts?.[0]?.text ||\n        \"⚠ No response received.\";\n      const botMessage = { role: \"bot\", content: botReply };\n      setMessages((prev) => [...prev, botMessage]);\n    } catch (error) {\n      console.error(\"Gemini API Error:\", error);\n      setMessages((prev) => [\n        ...prev,\n        { role: \"bot\", content: \"❌ Error: \" + error.message },\n      ]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Authentication functionality can be added later if needed\n\n  // Handle logout\n  const handleLogout = async () => {\n    await supabase.auth.signOut();\n  };\n\n  // Show notification\n  const showNotification = (msg, type = 'info') => {\n    setNotification({ msg, type });\n    setTimeout(() => setNotification(null), 3000);\n  };\n\n  // Log activity\n  const logActivity = (msg) => {\n    setActivityLog(log => [\n      { type: 'activity', date: new Date().toISOString(), msg },\n      ...log.slice(0, 19)\n    ]);\n  };\n\n  // Toggle menu\n  const toggleMenu = (menu) => {\n    setExpandedMenus(prev => ({\n      ...prev,\n      [menu]: !prev[menu]\n    }));\n  };\n\n  // Auto-scroll chat\n  useEffect(() => {\n    if (chatEndRef.current) chatEndRef.current.scrollIntoView({ behavior: 'smooth' });\n  }, [messages, loading]);\n\n  // Chart data\n  const getLast7Days = () => {\n    const days = [];\n    for (let i = 6; i >= 0; i--) {\n      const d = new Date();\n      d.setDate(d.getDate() - i);\n      days.push(d.toLocaleDateString());\n    }\n    return days;\n  };\n\n  const chartLabels = getLast7Days();\n  const chartData = {\n    labels: chartLabels,\n    datasets: [\n      {\n        label: 'Resource Uploads',\n        data: chartLabels.map(day => activityLog.filter(a => a.type === 'activity' && a.msg.startsWith('Uploaded resource') && new Date(a.date).toLocaleDateString() === day).length),\n        backgroundColor: '#3182ce',\n      },\n      {\n        label: 'Coding Practice',\n        data: chartLabels.map(day => activityLog.filter(a => a.type === 'activity' && a.msg === 'Clicked coding practice link' && new Date(a.date).toLocaleDateString() === day).length),\n        backgroundColor: '#805ad5',\n      },\n    ],\n  };\n\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: { position: 'top' },\n      tooltip: { enabled: true },\n    },\n    scales: {\n      y: { beginAtZero: true, ticks: { stepSize: 1 } },\n    },\n  };\n\n  return (\n    <div style={getStyle('appContainer')}>\n      {/* Top Navigation Bar */}\n      <nav style={getStyle('navbar')}>\n        <button\n          style={{\n            background: 'none',\n            border: 'none',\n            color: 'white', // Always white since navbar has gradient background\n            marginRight: '20px',\n            cursor: 'pointer',\n            padding: '8px',\n            borderRadius: '4px',\n            transition: 'all 0.2s ease'\n          }}\n          onClick={() => setSidebarOpen(!sidebarOpen)}\n          onMouseEnter={(e) => e.target.style.background = 'rgba(255, 255, 255, 0.1)'}\n          onMouseLeave={(e) => e.target.style.background = 'none'}\n        >\n          {sidebarOpen ? <FiX size={24} /> : <FiMenu size={24} />}\n        </button>\n\n        <div style={{ flex: 1, display: 'flex', alignItems: 'center' }}>\n          <img\n            src={require('./eduai-logo.jpg')}\n            alt=\"EduAI Logo\"\n            style={{ height: '36px', marginRight: '12px' }}\n          />\n          <div>\n            <div\n              data-header-title\n              style={{ fontWeight: 600, fontSize: '18px', color: 'white' }}\n            >\n              EDU NOVA\n            </div>\n            <div\n              data-header-subtitle\n              style={{ fontSize: '12px', opacity: 0.8, color: 'white' }}\n            >\n              AI POWERED LEARNING SYSTEM\n            </div>\n          </div>\n        </div>\n\n        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>\n          {user ? (\n            <>\n              <div style={{\n                width: '40px',\n                height: '40px',\n                borderRadius: '50%',\n                background: 'rgba(255, 255, 255, 0.2)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontWeight: 600,\n                cursor: 'pointer',\n                color: 'white',\n                backdropFilter: 'blur(10px)'\n              }}>\n                {user.email === ADMIN_EMAIL ? <FiShield size={20} color=\"#4caf50\" /> : user.email[0].toUpperCase()}\n              </div>\n              <button\n                style={{\n                  ...getStyle('buttonPrimary'),\n                  background: 'rgba(255, 255, 255, 0.2)',\n                  color: 'white',\n                  border: '1px solid rgba(255, 255, 255, 0.3)',\n                  backdropFilter: 'blur(10px)'\n                }}\n                onClick={handleLogout}\n              >\n                <FiLogOut /> Logout\n              </button>\n            </>\n          ) : (\n            <button\n              style={{\n                ...getStyle('buttonPrimary'),\n                background: 'rgba(255, 255, 255, 0.2)',\n                color: 'white',\n                border: '1px solid rgba(255, 255, 255, 0.3)',\n                backdropFilter: 'blur(10px)'\n              }}\n              onClick={() => { console.log('Login functionality to be implemented'); }}\n            >\n              <FiLogIn /> Login\n            </button>\n          )}\n        </div>\n      </nav>\n\n      {/* Sidebar */}\n      <aside style={getStyle('sidebar')}>\n        <div style={{ padding: '16px' }}>\n          {updatedSidebarItems.map((item, index) => (\n            <div key={index}>\n              <div\n                style={{\n                  ...getStyle('sidebarItem'),\n                  ...(activeTab === item.tab ? getStyle('sidebarItemActive') : {}),\n                  cursor: 'pointer',\n                  transition: 'all 0.3s ease'\n                }}\n                onClick={() => {\n                  setActiveTab(item.tab);\n                  setSidebarOpen(false);\n                }}\n                onMouseEnter={(e) => {\n                  if (activeTab !== item.tab) {\n                    e.target.style.background = 'rgba(0, 0, 0, 0.05)';\n                    e.target.style.transform = 'translateX(4px)';\n                    e.target.style.borderLeft = `3px solid ${globalStyles.currentTheme.primary}`;\n                  }\n                }}\n                onMouseLeave={(e) => {\n                  if (activeTab !== item.tab) {\n                    e.target.style.background = globalStyles.currentTheme.surface;\n                    e.target.style.transform = 'translateX(0)';\n                    e.target.style.borderLeft = '3px solid transparent';\n                  }\n                }}\n              >\n                <div style={{ marginRight: '12px' }}>{item.icon}</div>\n                <span style={{ flex: 1 }}>{item.title}</span>\n                {item.subItems.length > 0 && (\n                  <div onClick={(e) => {\n                    e.stopPropagation();\n                    toggleMenu(item.title);\n                  }}>\n                    {expandedMenus[item.title] ? <FiChevronDown /> : <FiChevronRight />}\n                  </div>\n                )}\n              </div>\n\n              {item.subItems.length > 0 && expandedMenus[item.title] && (\n                <div style={{ marginLeft: '32px' }}>\n                  {item.subItems.map((subItem, subIndex) => (\n                    <div\n                      key={subIndex}\n                      style={{\n                        ...getStyle('sidebarItem'),\n                        padding: '8px 16px 8px 32px',\n                        fontSize: '14px',\n                        opacity: 0.9\n                      }}\n                      onClick={() => {\n                        setActiveTab(item.tab);\n                        setSidebarOpen(false);\n                      }}\n                      onMouseEnter={(e) => {\n                        e.target.style.background = 'rgba(0, 0, 0, 0.03)';\n                        e.target.style.paddingLeft = '36px';\n                        e.target.style.opacity = '1';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.target.style.background = globalStyles.currentTheme.surface;\n                        e.target.style.paddingLeft = '32px';\n                        e.target.style.opacity = '0.9';\n                      }}\n                    >\n                      {subItem.title}\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n          ))}\n        </div>\n      </aside>\n\n      {/* Main Content */}\n      <main style={getStyle('mainContent')}>\n        {/* Overlay when sidebar is open on mobile */}\n        {sidebarOpen && window.innerWidth < 768 && (\n          <div\n            style={{\n              position: 'fixed',\n              top: '64px',\n              left: 0,\n              right: 0,\n              bottom: 0,\n              backgroundColor: 'rgba(0,0,0,0.5)',\n              zIndex: 800\n            }}\n            onClick={() => setSidebarOpen(false)}\n          />\n        )}\n\n        {/* Dashboard Content */}\n        {activeTab === \"dashboard\" && (\n          <div style={{\n            padding: '1rem',\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            minHeight: '100vh',\n            position: 'relative',\n            overflow: 'hidden'\n          }}>\n            {/* Animated Background Elements */}\n            <div style={{\n              position: 'absolute',\n              top: '10%',\n              left: '5%',\n              width: '300px',\n              height: '300px',\n              background: 'linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))',\n              borderRadius: '50%',\n              animation: 'float 6s ease-in-out infinite',\n              zIndex: 0\n            }} />\n            <div style={{\n              position: 'absolute',\n              top: '60%',\n              right: '10%',\n              width: '200px',\n              height: '200px',\n              background: 'linear-gradient(45deg, rgba(255,255,255,0.08), rgba(255,255,255,0.03))',\n              borderRadius: '50%',\n              animation: 'float 8s ease-in-out infinite reverse',\n              zIndex: 0\n            }} />\n            <div style={{\n              position: 'absolute',\n              bottom: '20%',\n              left: '15%',\n              width: '150px',\n              height: '150px',\n              background: 'linear-gradient(45deg, rgba(255,255,255,0.06), rgba(255,255,255,0.02))',\n              borderRadius: '50%',\n              animation: 'float 10s ease-in-out infinite',\n              zIndex: 0\n            }} />\n\n            <div style={{ maxWidth: '1400px', margin: '0 auto', position: 'relative', zIndex: 1 }}>\n\n              {/* Hero Section */}\n              <div style={{\n                background: 'linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.1))',\n                backdropFilter: 'blur(20px)',\n                borderRadius: '25px',\n                padding: '3rem',\n                marginBottom: '2rem',\n                color: 'white',\n                position: 'relative',\n                overflow: 'hidden',\n                border: '1px solid rgba(255,255,255,0.2)',\n                boxShadow: '0 25px 50px rgba(0,0,0,0.2)'\n              }}>\n                {/* Animated particles */}\n                <div style={{\n                  position: 'absolute',\n                  top: '20px',\n                  right: '20px',\n                  fontSize: '2rem',\n                  animation: 'bounce 2s infinite'\n                }}>🚀</div>\n                <div style={{\n                  position: 'absolute',\n                  bottom: '20px',\n                  left: '20px',\n                  fontSize: '1.5rem',\n                  animation: 'bounce 3s infinite'\n                }}>⭐</div>\n                <div style={{\n                  position: 'absolute',\n                  top: '50%',\n                  right: '10%',\n                  fontSize: '1.2rem',\n                  animation: 'bounce 4s infinite'\n                }}>💎</div>\n\n                <div style={{ display: 'flex', alignItems: 'center', gap: '2rem', position: 'relative', zIndex: 1 }}>\n                  <div style={{\n                    width: '120px',\n                    height: '120px',\n                    borderRadius: '50%',\n                    background: 'linear-gradient(135deg, #ff6b6b, #feca57)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    fontSize: '3rem',\n                    fontWeight: 'bold',\n                    border: '4px solid rgba(255, 255, 255, 0.3)',\n                    boxShadow: '0 15px 35px rgba(0,0,0,0.2)',\n                    animation: 'pulse 3s infinite'\n                  }}>\n                    {user ? user.email[0].toUpperCase() : '👤'}\n                  </div>\n                  <div>\n                    <h1 style={{\n                      margin: 0,\n                      fontSize: '3.5rem',\n                      fontWeight: 800,\n                      marginBottom: '1rem',\n                      background: 'linear-gradient(45deg, #fff, #f0f0f0)',\n                      WebkitBackgroundClip: 'text',\n                      WebkitTextFillColor: 'transparent',\n                      textShadow: '0 2px 4px rgba(0,0,0,0.3)'\n                    }}>\n                      Hey {user ? user.email.split('@')[0] : 'Champion'}! 🎯\n                    </h1>\n                    <p style={{\n                      margin: 0,\n                      fontSize: '1.3rem',\n                      opacity: 0.95,\n                      fontWeight: 500\n                    }}>\n                      Time to level up your skills and dominate your goals! 💪✨\n                    </p>\n                    <div style={{\n                      marginTop: '1rem',\n                      display: 'flex',\n                      gap: '1rem'\n                    }}>\n                      <div style={{\n                        background: 'rgba(255,255,255,0.2)',\n                        padding: '0.5rem 1rem',\n                        borderRadius: '20px',\n                        fontSize: '0.9rem',\n                        fontWeight: 600\n                      }}>\n                        🔥 12 Day Streak\n                      </div>\n                      <div style={{\n                        background: 'rgba(255,255,255,0.2)',\n                        padding: '0.5rem 1rem',\n                        borderRadius: '20px',\n                        fontSize: '0.9rem',\n                        fontWeight: 600\n                      }}>\n                        🏆 Level 15\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Gaming-Style Stats Cards */}\n              <div style={{\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n                gap: '1.5rem',\n                marginBottom: '2rem'\n              }}>\n                {[\n                  {\n                    title: 'FIRE STREAK',\n                    value: '12',\n                    unit: 'DAYS',\n                    icon: '🔥',\n                    color: '#ff4757',\n                    bgGradient: 'linear-gradient(135deg, #ff4757, #ff6b7a)',\n                    glowColor: '#ff4757',\n                    description: 'Unstoppable momentum!'\n                  },\n                  {\n                    title: 'SKILL POINTS',\n                    value: '2,847',\n                    unit: 'XP',\n                    icon: '⚡',\n                    color: '#3742fa',\n                    bgGradient: 'linear-gradient(135deg, #3742fa, #5352ed)',\n                    glowColor: '#3742fa',\n                    description: 'Level up achieved!'\n                  },\n                  {\n                    title: 'POWER LEVEL',\n                    value: '47',\n                    unit: 'HOURS',\n                    icon: '💪',\n                    color: '#2ed573',\n                    bgGradient: 'linear-gradient(135deg, #2ed573, #7bed9f)',\n                    glowColor: '#2ed573',\n                    description: 'Training complete!'\n                  },\n                  {\n                    title: 'ACHIEVEMENTS',\n                    value: '15',\n                    unit: 'UNLOCKED',\n                    icon: '🏆',\n                    color: '#ffa502',\n                    bgGradient: 'linear-gradient(135deg, #ffa502, #ffb142)',\n                    glowColor: '#ffa502',\n                    description: 'Champion status!'\n                  }\n                ].map((stat, index) => (\n                  <div key={index} style={{\n                    background: 'linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.1))',\n                    backdropFilter: 'blur(20px)',\n                    borderRadius: '20px',\n                    padding: '2rem',\n                    position: 'relative',\n                    overflow: 'hidden',\n                    border: '1px solid rgba(255,255,255,0.2)',\n                    cursor: 'pointer',\n                    transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',\n                    boxShadow: `0 15px 35px rgba(0,0,0,0.1), 0 0 0 1px ${stat.glowColor}20`\n                  }}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.transform = 'translateY(-10px) scale(1.02)';\n                    e.currentTarget.style.boxShadow = `0 25px 50px rgba(0,0,0,0.2), 0 0 30px ${stat.glowColor}40`;\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.transform = 'translateY(0) scale(1)';\n                    e.currentTarget.style.boxShadow = `0 15px 35px rgba(0,0,0,0.1), 0 0 0 1px ${stat.glowColor}20`;\n                  }}>\n\n                    {/* Animated background pattern */}\n                    <div style={{\n                      position: 'absolute',\n                      top: '-50%',\n                      left: '-50%',\n                      width: '200%',\n                      height: '200%',\n                      background: `conic-gradient(from 0deg, transparent, ${stat.color}20, transparent)`,\n                      animation: 'rotate 20s linear infinite',\n                      opacity: 0.3\n                    }} />\n\n                    {/* Glow effect */}\n                    <div style={{\n                      position: 'absolute',\n                      top: '10px',\n                      right: '10px',\n                      width: '80px',\n                      height: '80px',\n                      background: stat.bgGradient,\n                      borderRadius: '50%',\n                      opacity: 0.2,\n                      filter: 'blur(20px)'\n                    }} />\n\n                    <div style={{ position: 'relative', zIndex: 1 }}>\n                      {/* Header */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        marginBottom: '1.5rem'\n                      }}>\n                        <div style={{\n                          fontSize: '0.8rem',\n                          fontWeight: 800,\n                          color: 'rgba(255,255,255,0.8)',\n                          letterSpacing: '2px'\n                        }}>\n                          {stat.title}\n                        </div>\n                        <div style={{\n                          fontSize: '2rem',\n                          filter: 'drop-shadow(0 0 10px currentColor)'\n                        }}>\n                          {stat.icon}\n                        </div>\n                      </div>\n\n                      {/* Main Value */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'baseline',\n                        gap: '0.5rem',\n                        marginBottom: '1rem'\n                      }}>\n                        <div style={{\n                          fontSize: '3rem',\n                          fontWeight: 900,\n                          color: 'white',\n                          textShadow: `0 0 20px ${stat.color}`,\n                          lineHeight: 1\n                        }}>\n                          {stat.value}\n                        </div>\n                        <div style={{\n                          fontSize: '0.9rem',\n                          fontWeight: 600,\n                          color: stat.color,\n                          textTransform: 'uppercase',\n                          letterSpacing: '1px'\n                        }}>\n                          {stat.unit}\n                        </div>\n                      </div>\n\n                      {/* Description */}\n                      <div style={{\n                        fontSize: '0.9rem',\n                        color: 'rgba(255,255,255,0.9)',\n                        fontWeight: 500,\n                        fontStyle: 'italic'\n                      }}>\n                        {stat.description}\n                      </div>\n\n                      {/* Progress bar */}\n                      <div style={{\n                        marginTop: '1rem',\n                        height: '4px',\n                        background: 'rgba(255,255,255,0.2)',\n                        borderRadius: '2px',\n                        overflow: 'hidden'\n                      }}>\n                        <div style={{\n                          height: '100%',\n                          width: `${Math.min(100, (index + 1) * 25)}%`,\n                          background: stat.bgGradient,\n                          borderRadius: '2px',\n                          animation: 'slideIn 2s ease-out'\n                        }} />\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n\n              {/* Epic Action Center */}\n              <div style={{\n                background: 'linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.1))',\n                backdropFilter: 'blur(20px)',\n                borderRadius: '25px',\n                padding: '2rem',\n                marginBottom: '2rem',\n                border: '1px solid rgba(255,255,255,0.2)',\n                boxShadow: '0 25px 50px rgba(0,0,0,0.2)',\n                position: 'relative',\n                overflow: 'hidden'\n              }}>\n                <div style={{\n                  position: 'absolute',\n                  top: '-100px',\n                  right: '-100px',\n                  width: '300px',\n                  height: '300px',\n                  background: 'conic-gradient(from 0deg, #ff4757, #3742fa, #2ed573, #ffa502, #ff4757)',\n                  borderRadius: '50%',\n                  opacity: 0.1,\n                  animation: 'rotate 30s linear infinite'\n                }} />\n\n                <h2 style={{\n                  margin: '0 0 2rem 0',\n                  fontSize: '2rem',\n                  fontWeight: 800,\n                  color: 'white',\n                  textAlign: 'center',\n                  textShadow: '0 2px 4px rgba(0,0,0,0.3)'\n                }}>\n                  🎮 MISSION CONTROL CENTER 🎮\n                </h2>\n\n                <div style={{\n                  display: 'grid',\n                  gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                  gap: '1.5rem'\n                }}>\n                  {[\n                    {\n                      icon: '🎯',\n                      title: 'BATTLE MODE',\n                      subtitle: 'Take Quiz Challenge',\n                      desc: 'Test your skills in epic battles!',\n                      action: () => setActiveTab('quizzes'),\n                      color: '#ff4757',\n                      bgGradient: 'linear-gradient(135deg, #ff4757, #ff6b7a)'\n                    },\n                    {\n                      icon: '⚔️',\n                      title: 'CODE ARENA',\n                      subtitle: 'DSA Combat Zone',\n                      desc: 'Sharpen your coding weapons!',\n                      action: () => setActiveTab('dsa'),\n                      color: '#3742fa',\n                      bgGradient: 'linear-gradient(135deg, #3742fa, #5352ed)'\n                    },\n                    {\n                      icon: '📜',\n                      title: 'SCROLL REVIEW',\n                      subtitle: 'Resume Enhancement',\n                      desc: 'Upgrade your legendary resume!',\n                      action: () => setActiveTab('resume'),\n                      color: '#2ed573',\n                      bgGradient: 'linear-gradient(135deg, #2ed573, #7bed9f)'\n                    },\n                    {\n                      icon: '�',\n                      title: 'KNOWLEDGE VAULT',\n                      subtitle: 'Study Materials',\n                      desc: 'Access ancient wisdom scrolls!',\n                      action: () => setActiveTab('resources'),\n                      color: '#ffa502',\n                      bgGradient: 'linear-gradient(135deg, #ffa502, #ffb142)'\n                    }\n                  ].map((action, index) => (\n                    <div key={index}\n                      onClick={action.action}\n                      style={{\n                        background: 'linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.05))',\n                        backdropFilter: 'blur(15px)',\n                        borderRadius: '20px',\n                        padding: '1.5rem',\n                        cursor: 'pointer',\n                        transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',\n                        border: `2px solid ${action.color}30`,\n                        position: 'relative',\n                        overflow: 'hidden',\n                        boxShadow: `0 10px 30px rgba(0,0,0,0.1), 0 0 0 1px ${action.color}20`\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.transform = 'translateY(-8px) scale(1.05)';\n                        e.currentTarget.style.boxShadow = `0 20px 40px rgba(0,0,0,0.2), 0 0 30px ${action.color}40`;\n                        e.currentTarget.style.borderColor = action.color + '80';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.transform = 'translateY(0) scale(1)';\n                        e.currentTarget.style.boxShadow = `0 10px 30px rgba(0,0,0,0.1), 0 0 0 1px ${action.color}20`;\n                        e.currentTarget.style.borderColor = action.color + '30';\n                      }}\n                    >\n                      {/* Animated glow */}\n                      <div style={{\n                        position: 'absolute',\n                        top: '50%',\n                        left: '50%',\n                        width: '100px',\n                        height: '100px',\n                        background: action.bgGradient,\n                        borderRadius: '50%',\n                        transform: 'translate(-50%, -50%)',\n                        opacity: 0.1,\n                        filter: 'blur(30px)',\n                        animation: 'pulse 3s ease-in-out infinite'\n                      }} />\n\n                      <div style={{ position: 'relative', zIndex: 1, textAlign: 'center' }}>\n                        <div style={{\n                          fontSize: '3rem',\n                          marginBottom: '1rem',\n                          filter: 'drop-shadow(0 0 10px currentColor)',\n                          animation: 'bounce 2s ease-in-out infinite'\n                        }}>\n                          {action.icon}\n                        </div>\n\n                        <div style={{\n                          fontSize: '1.1rem',\n                          fontWeight: 800,\n                          color: 'white',\n                          marginBottom: '0.5rem',\n                          textShadow: `0 0 10px ${action.color}`,\n                          letterSpacing: '1px'\n                        }}>\n                          {action.title}\n                        </div>\n\n                        <div style={{\n                          fontSize: '0.8rem',\n                          color: action.color,\n                          fontWeight: 600,\n                          marginBottom: '0.75rem',\n                          textTransform: 'uppercase',\n                          letterSpacing: '0.5px'\n                        }}>\n                          {action.subtitle}\n                        </div>\n\n                        <div style={{\n                          fontSize: '0.85rem',\n                          color: 'rgba(255,255,255,0.8)',\n                          fontStyle: 'italic',\n                          lineHeight: 1.4\n                        }}>\n                          {action.desc}\n                        </div>\n\n                        {/* Power level indicator */}\n                        <div style={{\n                          marginTop: '1rem',\n                          height: '3px',\n                          background: 'rgba(255,255,255,0.2)',\n                          borderRadius: '2px',\n                          overflow: 'hidden'\n                        }}>\n                          <div style={{\n                            height: '100%',\n                            width: `${75 + index * 5}%`,\n                            background: action.bgGradient,\n                            borderRadius: '2px',\n                            animation: 'slideIn 1.5s ease-out'\n                          }} />\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              {/* Recent Activity & Resume Management */}\n              <div style={{\n                display: 'grid',\n                gridTemplateColumns: '1fr 1fr',\n                gap: '2rem'\n              }}>\n\n                {/* Recent Activity */}\n                <div style={{\n                  background: globalStyles.currentTheme.surface,\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n                  border: `1px solid ${globalStyles.currentTheme.border}`\n                }}>\n                  <h3 style={{\n                    margin: '0 0 1.5rem 0',\n                    fontSize: '1.3rem',\n                    fontWeight: 600,\n                    color: globalStyles.currentTheme.text\n                  }}>\n                    📈 Recent Activity\n                  </h3>\n\n                  <div style={{\n                    maxHeight: '250px',\n                    overflowY: 'auto'\n                  }}>\n                    {activityLog.slice(0, 5).map((log, index) => (\n                      <div key={index} style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1rem',\n                        padding: '0.75rem',\n                        borderRadius: '8px',\n                        marginBottom: '0.5rem',\n                        background: index % 2 === 0 ? globalStyles.currentTheme.secondary : 'transparent',\n                        transition: 'all 0.3s ease'\n                      }}>\n                        <div style={{\n                          width: '10px',\n                          height: '10px',\n                          borderRadius: '50%',\n                          background: log.type === 'login' ? '#4ECDC4' : '#45B7D1',\n                          flexShrink: 0\n                        }} />\n                        <div style={{ flex: 1 }}>\n                          <div style={{\n                            fontSize: '0.9rem',\n                            fontWeight: 500,\n                            color: globalStyles.currentTheme.text,\n                            marginBottom: '0.2rem'\n                          }}>\n                            {log.msg}\n                          </div>\n                          <div style={{\n                            fontSize: '0.8rem',\n                            color: globalStyles.currentTheme.textLight\n                          }}>\n                            {new Date(log.date).toLocaleString()}\n                          </div>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Resume Management */}\n                <div style={{\n                  background: globalStyles.currentTheme.surface,\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  boxShadow: `0 8px 25px ${globalStyles.currentTheme.shadow}`,\n                  border: `1px solid ${globalStyles.currentTheme.border}`\n                }}>\n                  <h3 style={{\n                    margin: '0 0 1.5rem 0',\n                    fontSize: '1.3rem',\n                    fontWeight: 600,\n                    color: globalStyles.currentTheme.text\n                  }}>\n                    📄 Resume Management\n                  </h3>\n\n                  <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n                    <label style={{\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.75rem',\n                      padding: '1rem',\n                      borderRadius: '12px',\n                      background: globalStyles.currentTheme.secondary,\n                      border: `2px dashed ${globalStyles.currentTheme.border}`,\n                      cursor: resumeUploadLoading ? 'not-allowed' : 'pointer',\n                      transition: 'all 0.3s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      if (!resumeUploadLoading) {\n                        e.currentTarget.style.borderColor = globalStyles.currentTheme.primary;\n                        e.currentTarget.style.background = globalStyles.currentTheme.primary + '10';\n                      }\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.borderColor = globalStyles.currentTheme.border;\n                      e.currentTarget.style.background = globalStyles.currentTheme.secondary;\n                    }}>\n                      <FiUpload size={20} color={globalStyles.currentTheme.primary} />\n                      <div>\n                        <div style={{\n                          fontWeight: 600,\n                          color: globalStyles.currentTheme.text,\n                          marginBottom: '0.2rem'\n                        }}>\n                          {resumeUploadLoading ? 'Uploading...' : 'Upload Resume'}\n                        </div>\n                        <div style={{\n                          fontSize: '0.8rem',\n                          color: globalStyles.currentTheme.textLight\n                        }}>\n                          PDF files only\n                        </div>\n                      </div>\n                      <input\n                        type=\"file\"\n                        accept=\"application/pdf\"\n                        onChange={handleResumeUpload}\n                        disabled={resumeUploadLoading}\n                        style={{ display: 'none' }}\n                      />\n                    </label>\n\n                    {resumeUrl && (\n                      <a\n                        href={resumeUrl}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.75rem',\n                          padding: '1rem',\n                          borderRadius: '12px',\n                          background: '#4ECDC4',\n                          color: 'white',\n                          textDecoration: 'none',\n                          fontWeight: 600,\n                          transition: 'all 0.3s ease'\n                        }}\n                        onMouseEnter={(e) => {\n                          e.currentTarget.style.background = '#3DBDB6';\n                          e.currentTarget.style.transform = 'translateY(-2px)';\n                        }}\n                        onMouseLeave={(e) => {\n                          e.currentTarget.style.background = '#4ECDC4';\n                          e.currentTarget.style.transform = 'translateY(0)';\n                        }}\n                      >\n                        <FiFileText size={20} />\n                        <span>View Resume</span>\n                      </a>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Chat Interface */}\n        {activeTab === \"resume\" && (\n          <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>\n            <div style={getStyle('card')}>\n              <h2 style={{\n                marginTop: 0,\n                color: '#333'\n              }}>Career Assistant</h2>\n              <p style={{\n                opacity: 0.8,\n                marginBottom: '24px',\n                color: '#666'\n              }}>\n                Get personalized resume advice and career guidance\n              </p>\n\n              {/* Chat messages */}\n              <div style={{\n                height: '50vh',\n                overflowY: 'auto',\n                marginBottom: '24px',\n                padding: '16px',\n                backgroundColor: '#f5f5f5',\n                border: '1px solid #e0e0e0',\n                borderRadius: '8px'\n              }}>\n\n                {messages.length === 0 ? (\n                  <div style={{\n                    height: '100%',\n                    display: 'flex',\n                    flexDirection: 'column',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    textAlign: 'center',\n                    opacity: 0.7\n                  }}>\n                    <div style={{ fontSize: '48px', marginBottom: '16px' }}>💬</div>\n                    <h3 style={{\n                      margin: 0,\n                      color: '#333'\n                    }}>Start a conversation</h3>\n                    <p style={{\n                      color: '#666'\n                    }}>Ask about resumes, interviews, or career advice</p>\n                  </div>\n                ) : (\n                  messages.map((msg, idx) => (\n                    <div\n                      key={idx}\n                      style={{\n                        ...(msg.role === 'user' ? getStyle('chatBubbleUser') : getStyle('chatBubbleBot')),\n                        animation: 'fadeIn 0.3s ease'\n                      }}\n                    >\n                      {msg.role === 'bot' ? (\n                        <ReactMarkdown>{msg.content}</ReactMarkdown>\n                      ) : (\n                        msg.content\n                      )}\n                    </div>\n                  ))\n                )}\n                {loading && (\n                  <div style={getStyle('chatBubbleBot')}>\n                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n                      <div style={{\n                        width: '10px',\n                        height: '10px',\n                        borderRadius: '50%',\n                        backgroundColor: '#1976d2',\n                        animation: 'pulse 1.4s infinite ease-in-out'\n                      }} />\n                      <div style={{\n                        width: '10px',\n                        height: '10px',\n                        borderRadius: '50%',\n                        backgroundColor: '#1976d2',\n                        animation: 'pulse 1.4s infinite ease-in-out',\n                        animationDelay: '0.2s'\n                      }} />\n                      <div style={{\n                        width: '10px',\n                        height: '10px',\n                        borderRadius: '50%',\n                        backgroundColor: '#1976d2',\n                        animation: 'pulse 1.4s infinite ease-in-out',\n                        animationDelay: '0.4s'\n                      }} />\n                    </div>\n                  </div>\n                )}\n                <div ref={chatEndRef} />\n              </div>\n\n              {/* Input area */}\n              <form\n                style={{ display: 'flex', gap: '12px' }}\n                onSubmit={e => {\n                  e.preventDefault();\n                  sendMessage();\n                }}\n              >\n                <input\n                  type=\"text\"\n                  placeholder=\"Type your message...\"\n                  style={getStyle('inputField')}\n                  value={input}\n                  onChange={e => setInput(e.target.value)}\n                  disabled={loading}\n                />\n                <button\n                  type=\"submit\"\n                  style={{\n                    ...getStyle('buttonPrimary'),\n                    minWidth: '100px'\n                  }}\n                  disabled={loading || !input.trim()}\n                >\n                  {loading ? 'Sending...' : 'Send'}\n                </button>\n              </form>\n            </div>\n          </div>\n        )}\n\n        {/* Enhanced DSA Company Questions */}\n        {activeTab === \"dsa\" && (\n          <div style={{ padding: '24px' }}>\n            <div style={getStyle('card')}>\n              {/* Header with revert button */}\n              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>\n                <div>\n                  <h2 style={{ marginTop: 0, marginBottom: '8px' }}>🚀 Company Wise DSA Questions</h2>\n                  <p style={{ opacity: 0.8, margin: 0 }}>\n                    Explore DSA questions from top companies with enhanced filtering and favorites\n                  </p>\n                </div>\n                {showRevertButton && (\n                  <button\n                    onClick={revertHeaderChanges}\n                    style={{\n                      ...getStyle('buttonPrimary'),\n                      background: '#ff6b6b',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '8px',\n                      fontSize: '14px',\n                      padding: '8px 16px',\n                      border: 'none',\n                      borderRadius: '8px',\n                      color: 'white',\n                      cursor: 'pointer',\n                      transition: 'all 0.3s ease'\n                    }}\n                    onMouseEnter={(e) => e.target.style.background = '#ff5252'}\n                    onMouseLeave={(e) => e.target.style.background = '#ff6b6b'}\n                  >\n                    <FiRefreshCw size={16} />\n                    Revert Header Color\n                  </button>\n                )}\n              </div>\n\n              {/* Category Tabs */}\n              <div style={{\n                display: 'flex',\n                gap: '8px',\n                marginBottom: '20px',\n                flexWrap: 'wrap',\n                borderBottom: '1px solid #eee',\n                paddingBottom: '16px'\n              }}>\n                {['all', ...Object.keys(companyCategories)].map(category => (\n                  <button\n                    key={category}\n                    onClick={() => setSelectedCategory(category)}\n                    style={{\n                      padding: '8px 16px',\n                      borderRadius: '20px',\n                      border: selectedCategory === category\n                        ? 'none'\n                        : '1px solid #ddd',\n                      background: selectedCategory === category\n                        ? globalStyles.currentTheme.primary\n                        : 'transparent',\n                      color: selectedCategory === category\n                        ? 'white'\n                        : '#666',\n                      cursor: 'pointer',\n                      fontSize: '14px',\n                      fontWeight: selectedCategory === category ? 600 : 400,\n                      transition: 'all 0.3s ease',\n                      textTransform: 'capitalize'\n                    }}\n                    onMouseEnter={(e) => {\n                      if (selectedCategory !== category) {\n                        e.target.style.background = '#f5f5f5';\n                      }\n                    }}\n                    onMouseLeave={(e) => {\n                      if (selectedCategory !== category) {\n                        e.target.style.background = 'transparent';\n                      }\n                    }}\n                  >\n                    {category === 'all' ? '🌟 All' : `${category === 'FAANG' ? '🔥' : category === 'Big Tech' ? '💻' : category === 'Startups' ? '🚀' : category === 'Finance' ? '💰' : category === 'Indian' ? '🇮🇳' : '🏢'} ${category}`}\n                  </button>\n                ))}\n              </div>\n\n              {/* Controls Row */}\n              <div style={{\n                display: 'flex',\n                gap: '16px',\n                marginBottom: '24px',\n                flexWrap: 'wrap',\n                alignItems: 'center'\n              }}>\n                {/* Search box */}\n                <div style={{ position: 'relative', flex: 1, minWidth: '300px' }}>\n                  <div style={{\n                    position: 'absolute',\n                    left: '16px',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    color: '#666'\n                  }}>\n                    <FiSearch size={20} />\n                  </div>\n                  <input\n                    type=\"text\"\n                    placeholder=\"Search companies...\"\n                    style={{\n                      ...getStyle('inputField'),\n                      paddingLeft: '48px',\n                      width: '100%'\n                    }}\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                  />\n                  {searchTerm && (\n                    <button\n                      style={{\n                        position: 'absolute',\n                        right: '16px',\n                        top: '50%',\n                        transform: 'translateY(-50%)',\n                        background: 'none',\n                        border: 'none',\n                        color: '#666',\n                        cursor: 'pointer'\n                      }}\n                      onClick={() => setSearchTerm(\"\")}\n                    >\n                      <FiX size={20} />\n                    </button>\n                  )}\n                </div>\n\n                {/* Sort dropdown */}\n                <select\n                  value={sortBy}\n                  onChange={(e) => setSortBy(e.target.value)}\n                  style={{\n                    ...getStyle('inputField'),\n                    width: 'auto',\n                    minWidth: '150px'\n                  }}\n                >\n                  <option value=\"name\">📝 Sort by Name</option>\n                  <option value=\"favorites\">⭐ Favorites First</option>\n                </select>\n              </div>\n\n              {/* Recent Companies */}\n              {recentCompanies.length > 0 && (\n                <div style={{\n                  marginBottom: '24px',\n                  padding: '16px',\n                  borderRadius: '12px',\n                  background: '#f8f9fa',\n                  border: '1px solid #e9ecef'\n                }}>\n                  <h3 style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '8px',\n                    fontSize: '16px',\n                    marginBottom: '12px',\n                    color: '#333',\n                    margin: '0 0 12px 0'\n                  }}>\n                    <FiClock color=\"#666\" /> Recently Viewed\n                  </h3>\n                  <div style={{\n                    display: 'flex',\n                    gap: '8px',\n                    flexWrap: 'wrap'\n                  }}>\n                    {recentCompanies.map(company => (\n                      <button\n                        key={company}\n                        onClick={() => handleCompanyClick(company)}\n                        style={{\n                          padding: '6px 12px',\n                          borderRadius: '16px',\n                          border: `1px solid ${globalStyles.currentTheme.primary}`,\n                          background: 'transparent',\n                          color: globalStyles.currentTheme.primary,\n                          cursor: 'pointer',\n                          fontSize: '12px',\n                          transition: 'all 0.3s ease'\n                        }}\n                        onMouseEnter={(e) => {\n                          e.target.style.background = globalStyles.currentTheme.primary;\n                          e.target.style.color = 'white';\n                        }}\n                        onMouseLeave={(e) => {\n                          e.target.style.background = 'transparent';\n                          e.target.style.color = globalStyles.currentTheme.primary;\n                        }}\n                      >\n                        {company}\n                      </button>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {/* Companies grid */}\n              <div style={{\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fill, minmax(220px, 1fr))',\n                gap: '16px',\n                marginTop: '24px'\n              }}>\n                {getFilteredCompanies().map((company, index) => (\n                  <div\n                    key={index}\n                    style={{\n                      ...getStyle('companyCard'),\n                      position: 'relative',\n                      transform: favoriteCompanies.includes(company) ? 'scale(1.02)' : 'scale(1)',\n                      border: favoriteCompanies.includes(company)\n                        ? `2px solid ${globalStyles.currentTheme.primary}`\n                        : `1px solid ${globalStyles.currentTheme.border}`,\n                      background: globalStyles.currentTheme.surface,\n                      color: globalStyles.currentTheme.text,\n                      animation: `fadeIn 0.3s ease ${index * 0.1}s both`,\n                      boxShadow: `0 4px 6px ${globalStyles.currentTheme.shadow}`\n                    }}\n                    onClick={() => handleCompanyClick(company)}\n                  >\n                    {/* Favorite button */}\n                    <button\n                      onClick={(e) => toggleFavorite(company, e)}\n                      style={{\n                        position: 'absolute',\n                        top: '8px',\n                        right: '8px',\n                        background: 'none',\n                        border: 'none',\n                        cursor: 'pointer',\n                        color: favoriteCompanies.includes(company) ? '#ff6b6b' : '#ccc',\n                        transition: 'all 0.3s ease',\n                        fontSize: '18px'\n                      }}\n                    >\n                      <FiHeart fill={favoriteCompanies.includes(company) ? 'currentColor' : 'none'} />\n                    </button>\n\n                    {/* Company initial with gradient */}\n                    <div style={{\n                      width: '56px',\n                      height: '56px',\n                      borderRadius: '50%',\n                      background: `linear-gradient(135deg, ${globalStyles.currentTheme.primary}, ${globalStyles.currentTheme.primaryDark})`,\n                      color: 'white',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      fontSize: '24px',\n                      fontWeight: 700,\n                      marginBottom: '12px',\n                      boxShadow: `0 4px 8px ${globalStyles.currentTheme.shadow}`\n                    }}>\n                      {company.charAt(0)}\n                    </div>\n\n                    {/* Company name */}\n                    <div style={{\n                      fontWeight: 600,\n                      textAlign: 'center',\n                      fontSize: '14px',\n                      marginBottom: '8px'\n                    }}>\n                      {company}\n                    </div>\n\n                    {/* Mock stats */}\n                    <div style={{\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      fontSize: '12px',\n                      opacity: 0.7,\n                      marginTop: '8px'\n                    }}>\n                      <span>📊 {Math.floor(Math.random() * 50) + 10} Questions</span>\n                      <span>⭐ {(Math.random() * 2 + 3).toFixed(1)}</span>\n                    </div>\n\n                    {/* Category badge */}\n                    {Object.entries(companyCategories).map(([category, companies]) => {\n                      if (companies.some(c => c.toLowerCase() === company.toLowerCase())) {\n                        return (\n                          <div\n                            key={category}\n                            style={{\n                              position: 'absolute',\n                              top: '8px',\n                              left: '8px',\n                              background: globalStyles.currentTheme.primary,\n                              color: 'white',\n                              padding: '2px 6px',\n                              borderRadius: '8px',\n                              fontSize: '10px',\n                              fontWeight: 600\n                            }}\n                          >\n                            {category}\n                          </div>\n                        );\n                      }\n                      return null;\n                    })}\n                  </div>\n                ))}\n              </div>\n\n              {/* No results message */}\n              {getFilteredCompanies().length === 0 && (\n                <div style={{\n                  textAlign: 'center',\n                  padding: '40px',\n                  opacity: 0.7,\n                  color: '#666'\n                }}>\n                  <div style={{ fontSize: '48px', marginBottom: '16px' }}>🔍</div>\n                  <h3 style={{ color: '#333' }}>No companies found</h3>\n                  <p style={{ color: '#666' }}>Try adjusting your search or category filter</p>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* Quizzes Section */}\n        {activeTab === \"quizzes\" && (\n          <div style={{ padding: '24px' }}>\n            <div style={getStyle('card')}>\n              <h2 style={{ marginTop: 0 }}>Career Quizzes</h2>\n              <p style={{ opacity: 0.8, marginBottom: '24px' }}>\n                Test your knowledge with our career-focused quizzes!\n              </p>\n\n              <div style={{\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',\n                gap: '16px'\n              }}>\n                {quizButtons.map((quiz, index) => (\n                  <div\n                    key={index}\n                    style={getStyle('quizCard')}\n                    onClick={() => openQuizLink(quiz.link)}\n                  >\n                    <div>\n                      <h3 style={{ margin: '0 0 8px 0' }}>{quiz.title}</h3>\n                      <p style={{\n                        margin: 0,\n                        fontSize: '14px',\n                        opacity: 0.8\n                      }}>\n                        {quiz.description}\n                      </p>\n                    </div>\n                    <div style={{ color: '#1976d2' }}>\n                      <FiExternalLink size={20} />\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Other tabs */}\n        {activeTab === \"coding\" && (\n          <div style={{ padding: '24px' }}>\n            <Coding />\n          </div>\n        )}\n        {activeTab === \"resources\" && (\n          <div style={{ padding: '24px' }}>\n            <div style={getStyle('card')}>\n              <h2 style={{\n                marginTop: 0,\n                color: '#333'\n              }}>Resources</h2>\n              <p style={{\n                opacity: 0.8,\n                marginBottom: '24px',\n                color: '#666'\n              }}>\n                Upload and manage your study materials and notes\n              </p>\n\n              <div style={{ marginBottom: '24px' }}>\n                <label style={{\n                  ...getStyle('buttonPrimary'),\n                  background: '#f5f5f5',\n                  color: '#333',\n                  border: '1px solid #ddd',\n                  cursor: resourceUploadLoading ? 'not-allowed' : 'pointer'\n                }}>\n                  <FiUpload />\n                  {resourceUploadLoading ? 'Uploading...' : 'Upload Resource'}\n                  <input\n                    type=\"file\"\n                    accept=\".pdf,.doc,.docx,.txt\"\n                    onChange={handleResourceUpload}\n                    disabled={resourceUploadLoading}\n                    style={{ display: 'none' }}\n                  />\n                </label>\n              </div>\n\n              <div>\n                <h3 style={{\n                  marginBottom: '16px',\n                  color: '#333'\n                }}>Your Resources</h3>\n                {userResources.length === 0 ? (\n                  <p style={{\n                    opacity: 0.7,\n                    color: '#666'\n                  }}>No resources uploaded yet</p>\n                ) : (\n                  <div style={{\n                    backgroundColor: '#f5f5f5',\n                    border: '1px solid #e0e0e0',\n                    borderRadius: '8px',\n                    padding: '16px'\n                  }}>\n                    {userResources.map((file, idx) => {\n                      const { data: urlData } = supabase.storage.from('resources').getPublicUrl(`${user.id}/${file.name}`);\n                      return (\n                        <div key={idx} style={{\n                          padding: '12px',\n                          borderBottom: '1px solid #eee',\n                          display: 'flex',\n                          justifyContent: 'space-between',\n                          alignItems: 'center'\n                        }}>\n                          <span style={{\n                            color: '#333'\n                          }}>{file.name}</span>\n                          <a\n                            href={urlData.publicUrl}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            style={{\n                              color: '#1976d2',\n                              textDecoration: 'none',\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '4px'\n                            }}\n                          >\n                            <FiExternalLink size={16} />\n                            Open\n                          </a>\n                        </div>\n                      );\n                    })}\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n        {activeTab === \"academics\" && <Exams />}\n        {activeTab === \"faq\" && <Faq />}\n        {activeTab === \"admin\" && user?.email === ADMIN_EMAIL && (\n          <div style={{ padding: '24px' }}>\n            <div style={getStyle('card')}>\n              <h2 style={{\n                marginTop: 0,\n                color: '#333'\n              }}>Admin Panel</h2>\n              <div style={{\n                display: 'flex',\n                gap: '16px',\n                marginBottom: '24px'\n              }}>\n                <button\n                  style={{\n                    ...getStyle('buttonPrimary'),\n                    background: adminTab === 'users' ?\n                      globalStyles.currentTheme.primary : 'transparent',\n                    color: adminTab === 'users' ?\n                      'white' : '#333',\n                    border: '1px solid #ddd'\n                  }}\n                  onClick={() => setAdminTab('users')}\n                >\n                  Users\n                </button>\n                <button\n                  style={{\n                    ...getStyle('buttonPrimary'),\n                    background: adminTab === 'resources' ?\n                      globalStyles.currentTheme.primary : 'transparent',\n                    color: adminTab === 'resources' ?\n                      'white' : '#333',\n                    border: '1px solid #ddd'\n                  }}\n                  onClick={() => setAdminTab('resources')}\n                >\n                  Resources\n                </button>\n              </div>\n\n              {adminTab === 'users' && (\n                <div>\n                  <h3 style={{\n                    marginBottom: '16px',\n                    color: '#333'\n                  }}>All Users</h3>\n                  <div style={{\n                    backgroundColor: '#f5f5f5',\n                    border: '1px solid #e0e0e0',\n                    borderRadius: '8px',\n                    padding: '16px'\n                  }}>\n                    {allUsers.map((user, idx) => (\n                      <div key={idx} style={{\n                        padding: '12px',\n                        borderBottom: '1px solid #eee',\n                        color: '#333'\n                      }}>\n                        {user.email}\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {adminTab === 'resources' && (\n                <div>\n                  <h3 style={{\n                    marginBottom: '16px',\n                    color: '#333'\n                  }}>All Resources</h3>\n                  <p style={{\n                    opacity: 0.7,\n                    color: '#666'\n                  }}>Resource management coming soon</p>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </main>\n\n      {/* Notification */}\n      {notification && (\n        <div style={{\n          ...getStyle('notification'),\n          backgroundColor: notification.type === 'error' ? '#f44336' :\n                         notification.type === 'success' ? '#4caf50' : '#2196f3',\n          color: 'white',\n          border: 'none'\n        }}>\n          {notification.msg}\n        </div>\n      )}\n\n      {/* Enhanced Global styles */}\n      <style>{`\n        @keyframes fadeIn {\n          from { opacity: 0; transform: translateY(10px); }\n          to { opacity: 1; transform: translateY(0); }\n        }\n        @keyframes slideIn {\n          from { transform: translateX(100%); }\n          to { transform: translateX(0); }\n        }\n        @keyframes pulse {\n          0%, 100% { opacity: 1; }\n          50% { opacity: 0.5; }\n        }\n        @keyframes bounce {\n          0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }\n          40%, 43% { transform: translate3d(0,-8px,0); }\n          70% { transform: translate3d(0,-4px,0); }\n          90% { transform: translate3d(0,-2px,0); }\n        }\n        @keyframes glow {\n          0%, 100% { box-shadow: 0 0 5px rgba(99, 102, 241, 0.3); }\n          50% { box-shadow: 0 0 20px rgba(99, 102, 241, 0.6); }\n        }\n        @keyframes shimmer {\n          0% { background-position: -200px 0; }\n          100% { background-position: calc(200px + 100%) 0; }\n        }\n\n        /* Enhanced hover effects */\n        .company-card:hover {\n          animation: bounce 0.6s ease;\n        }\n\n        .favorite-btn:hover {\n          animation: pulse 0.5s ease;\n        }\n\n        /* Smooth transitions for all interactive elements */\n        button, input, select {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        button:hover {\n          transform: translateY(-1px);\n        }\n\n        * {\n          box-sizing: border-box;\n        }\n        body {\n          margin: 0;\n          font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n        }\n      `}</style>\n    </div>\n  );\n};\nexport default EduAIChatBot;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,OAAO;AAC1F,SAASC,MAAM,EAAEC,GAAG,QAAQ,oBAAoB;AAChD,SAASC,IAAI,EAAEC,EAAE,QAAQ,kBAAkB;AAC3C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,kBAAkB,QAAQ,eAAe;AAClD,SAASC,MAAM,EAAEC,OAAO,EAAEC,SAAS,EAAEC,YAAY,QAAQ,cAAc;AACvE,SAASC,aAAa,EAAEC,UAAU,EAAEC,qBAAqB,EAAEC,qBAAqB,QAAQ,uBAAuB;AAC/G,SAASC,QAAQ,QAAQ,SAAS;AAClC,SACEC,UAAU,EAAEC,MAAM,EAAEC,YAAY,EAAEC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EACnEC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,aAAa,EACrEC,cAAc,EAAEC,OAAO,EAAEC,OAAO,EAAEC,WAAW,QACxC,gBAAgB;AACvB,SAASC,YAAY,QAAQ,uBAAuB;AACpD,OAAO,WAAW;AAClB,OAAO,kBAAkB;;AAEzB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,OAAO,gBAAG7C,KAAK,CAAC8C,IAAI,CAAAC,EAAA,GAACA,CAAA,KAAM,MAAM,CAAC,OAAO,CAAC,CAAC;AAACC,GAAA,GAA5CH,OAAO;AACb,MAAMI,SAAS,gBAAGjD,KAAK,CAAC8C,IAAI,CAAAI,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,SAAS,CAAC,CAAC;AAACC,GAAA,GAAhDF,SAAS;AACf,MAAMG,UAAU,gBAAGpD,KAAK,CAAC8C,IAAI,CAAAO,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,UAAU,CAAC,CAAC;;AAEvD;AAAAC,GAAA,GAFMF,UAAU;AAGhB,MAAMG,SAAS,gBAAGvD,KAAK,CAAC8C,IAAI,CAAAU,GAAA,GAAC,MAAAA,CAAA,KAAY;EACvC,MAAM;IAAEC;EAAI,CAAC,GAAG,MAAM,MAAM,CAAC,iBAAiB,CAAC;EAC/C,MAAM;IAAEC,KAAK;IAAEC,UAAU;IAAEC,aAAa;IAAEC,WAAW;IAAEC,OAAO;IAAEC;EAAO,CAAC,GAAG,MAAM,MAAM,CAAC,UAAU,CAAC;EACnGL,KAAK,CAACM,QAAQ,CAACL,UAAU,EAAEC,aAAa,EAAEC,WAAW,EAAEC,OAAO,EAAEC,MAAM,CAAC;EACvE,OAAO;IAAEE,OAAO,EAAER;EAAI,CAAC;AACzB,CAAC,CAAC;AAACS,GAAA,GALGX,SAAS;AAOf,MAAMY,YAAY,gBAAGnE,KAAK,CAAC8C,IAAI,CAAAsB,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,gBAAgB,CAAC,CAAC;;AAE/D;AAAAC,GAAA,GAFMF,YAAY;AAGlB,MAAMG,mBAAmB,GAAG1D,YAAY,CAAC2D,GAAG,CAACC,IAAI,IAAI;EACnD,MAAMC,OAAO,GAAG;IACd,QAAQ,eAAE/B,OAAA,CAACnB,UAAU;MAAAmD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxB,KAAK,eAAEnC,OAAA,CAAClB,MAAM;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjB,QAAQ,eAAEnC,OAAA,CAACR,QAAQ;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtB,WAAW,eAAEnC,OAAA,CAACV,WAAW;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5B,SAAS,eAAEnC,OAAA,CAACP,aAAa;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5B,UAAU,eAAEnC,OAAA,CAACT,WAAW;MAAAyC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3B,WAAW,eAAEnC,OAAA,CAACf,MAAM;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvB,KAAK,eAAEnC,OAAA,CAACjB,YAAY;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvB,OAAO,eAAEnC,OAAA,CAACb,QAAQ;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACtB,CAAC;EAED,OAAO;IACL,GAAGL,IAAI;IACPM,IAAI,EAAEL,OAAO,CAACD,IAAI,CAACO,GAAG,CAAC,IAAIN,OAAO,CAACD,IAAI,CAACQ,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC,iBAAIvC,OAAA,CAAChB,OAAO;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC5E,CAAC;AACH,CAAC,CAAC;AAEF,MAAMK,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB;EACA,MAAM;IAAEC,OAAO;IAAEC,WAAW;IAAEC;EAAU,CAAC,GAAGjE,qBAAqB,CAAC,CAAC;EACnE,MAAM;IAAEkE,QAAQ;IAAEC;EAAS,CAAC,GAAGtE,aAAa,CAAC,CAAC;EAC9C,MAAM;IAAEuE,MAAM,EAAEC,WAAW;IAAEC,MAAM,EAAEC,aAAa;IAAEC,KAAK,EAAEC;EAAa,CAAC,GAAG3E,UAAU,CAAC,CAAC;EACxF,MAAM;IAAE4E;EAAc,CAAC,GAAG3E,qBAAqB,CAAC,CAAC;;EAEjD;EACA,MAAM,CAAC4E,KAAK,EAAEC,QAAQ,CAAC,GAAGhG,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiG,QAAQ,EAAEC,WAAW,CAAC,GAAGlG,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmG,MAAM,EAAEC,SAAS,CAAC,GAAGpG,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACqG,OAAO,EAAEC,UAAU,CAAC,GAAGtG,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuG,SAAS,EAAEC,YAAY,CAAC,GAAGxG,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACyG,SAAS,EAAEC,YAAY,CAAC,GAAG1G,QAAQ,CAAC,WAAW,CAAC;EACvD,MAAM,CAAC2G,UAAU,EAAEC,aAAa,CAAC,GAAG5G,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6G,aAAa,EAAEC,gBAAgB,CAAC,GAAG9G,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAAC+G,IAAI,EAAEC,OAAO,CAAC,GAAGhH,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACiH,UAAU,EAAEC,aAAa,CAAC,GAAGlH,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAM,CAACmH,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGpH,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACqH,SAAS,EAAEC,YAAY,CAAC,GAAGtH,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACuH,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGxH,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACyH,aAAa,CAAC,GAAGzH,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM0H,WAAW,GAAG,mBAAmB;EACvC,MAAM,CAACC,QAAQ,CAAC,GAAG3H,QAAQ,CAAC,EAAE,CAAC;EAC/B,MAAM,CAAC4H,QAAQ,EAAEC,WAAW,CAAC,GAAG7H,QAAQ,CAAC,OAAO,CAAC;EACjD,MAAM,CAAC8H,YAAY,EAAEC,eAAe,CAAC,GAAG/H,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACgI,WAAW,EAAEC,cAAc,CAAC,GAAGjI,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAMkI,UAAU,GAAGhI,MAAM,CAAC,IAAI,CAAC;;EAE/B;EACA,MAAM,CAACiI,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpI,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACqI,MAAM,EAAEC,SAAS,CAAC,GAAGtI,QAAQ,CAAC,MAAM,CAAC;EAC5C,MAAM,CAACuI,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxI,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACyI,eAAe,EAAEC,kBAAkB,CAAC,GAAG1I,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC2I,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5I,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAM6I,OAAO,GAAG,yCAAyC;EACzD,MAAMC,YAAY,GAAG,0CAA0C;EAC/D,MAAMC,iBAAiB,GAAG,kNAAkN;EAC5O,MAAMC,QAAQ,GAAGzG,YAAY,CAACuG,YAAY,EAAEC,iBAAiB,CAAC;;EAE9D;EACA,MAAME,iBAAiB,GAAG;IACxB,OAAO,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC;IAC7D,UAAU,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,CAAC;IACpF,UAAU,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC;IACrF,SAAS,EAAE,CAAC,eAAe,EAAE,UAAU,EAAE,gBAAgB,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,CAAC;IAC/F,YAAY,EAAE,CAAC,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,KAAK,CAAC;IACzE,YAAY,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,aAAa,CAAC;IAC7E,QAAQ,EAAE,CAAC,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,OAAO,EAAE,QAAQ,CAAC;IACrE,QAAQ,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ;EACtF,CAAC;;EAED;EACA,MAAMC,SAAS,GAAG,CACf,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EACxE,QAAQ,EAAE,eAAe,EAAE,SAAS,EAAE,YAAY,EAAE,QAAQ,EAAE,KAAK,EACnE,QAAQ,EAAE,kBAAkB,EAAE,SAAS,EAAE,OAAO,EAAE,iBAAiB,EACnE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,cAAc,EAAE,UAAU,EAChE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,EAC9D,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,MAAM,EAAE,aAAa,EAAE,KAAK,EAChE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,QAAQ,EAClE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAC5D,UAAU,EAAE,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAC9D,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,aAAa,EAAE,QAAQ,EAC3D,SAAS,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EACjE,WAAW,EAAE,WAAW,EAAE,eAAe,EAAE,SAAS,EAAE,SAAS,EAC/D,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAC7D,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,SAAS,EAC1D,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,EAChE,OAAO,EAAE,eAAe,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EACnE,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EACzD,sBAAsB,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EACnE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,aAAa,EACtD,iBAAiB,EAAE,cAAc,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAC3D,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EAC/D,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EACnE,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EACzD,gBAAgB,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAC5D,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,EAChE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAClE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,uBAAuB,EAAE,oBAAoB,EACxE,QAAQ,EAAE,OAAO,EAAE,oBAAoB,EAAE,SAAS,EAAE,WAAW,EAC/D,aAAa,EAAE,SAAS,EAAE,cAAc,EAAE,UAAU,EAAE,OAAO,EAC7D,SAAS,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAC9D,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAE,KAAK,EAAE,YAAY,EAChE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,EACnE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,qBAAqB,EAAE,QAAQ,EAChE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,aAAa,EAC5D,cAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAChE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EACxD,eAAe,EAAE,QAAQ,EAAE,SAAS,EAAE,iBAAiB,EAAE,MAAM,EAC/D,IAAI,EAAE,QAAQ,EAAE,cAAc,EAAE,aAAa,EAAE,SAAS,EACxD,aAAa,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,EAAE,OAAO,EAAE,QAAQ,EAChE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAChE,QAAQ,EAAE,SAAS,EAAE,UAAU,CAChC;;EAED;EACA,MAAMC,WAAW,GAAG,CAClB;IACEpE,KAAK,EAAE,gBAAgB;IACvBqE,WAAW,EAAE,+DAA+D;IAC5EC,IAAI,EAAE;EACR,CAAC,EACD;IACEtE,KAAK,EAAE,oBAAoB;IAC3BqE,WAAW,EAAE,uCAAuC;IACpDC,IAAI,EAAE;EACR,CAAC,EACD;IACEtE,KAAK,EAAE,oBAAoB;IAC3BqE,WAAW,EAAE,mCAAmC;IAChDC,IAAI,EAAE;EACR,CAAC,EACD;IACEtE,KAAK,EAAE,0CAA0C;IACjDqE,WAAW,EAAE,+CAA+C;IAC5DC,IAAI,EAAE;EACR,CAAC,EACD;IACEtE,KAAK,EAAE,qBAAqB;IAC5BqE,WAAW,EAAE,kDAAkD;IAC/DC,IAAI,EAAE;EACR,CAAC,EACD;IACEtE,KAAK,EAAE,UAAU;IACjBqE,WAAW,EAAE,qCAAqC;IAClDC,IAAI,EAAE;EACR,CAAC,EACD;IACEtE,KAAK,EAAE,2CAA2C;IAClDqE,WAAW,EAAE,kCAAkC;IAC/CC,IAAI,EAAE;EACR,CAAC,EACA;IACCtE,KAAK,EAAE,sBAAsB;IAC7BqE,WAAW,EAAE,gCAAgC;IAC7CC,IAAI,EAAE;EAER,CAAC,CACF;;EAED;EACA,MAAMC,MAAM,GAAG;IACb,GAAGC,YAAY;IACfC,YAAY,EAAE;MACZ,GAAGD,YAAY,CAACC,YAAY;MAC5BC,eAAe,EAAEF,YAAY,CAACG,YAAY,CAACC,UAAU;MACrDC,KAAK,EAAEL,YAAY,CAACG,YAAY,CAACG;IACnC,CAAC;IACDC,MAAM,EAAE;MACN,GAAGP,YAAY,CAACQ,WAAW;MAC3BC,YAAY,EAAE,aAAaT,YAAY,CAACG,YAAY,CAACO,MAAM;IAC7D,CAAC;IACDC,OAAO,EAAE;MACP,GAAGX,YAAY,CAACY,YAAY;MAC5BV,eAAe,EAAEF,YAAY,CAACG,YAAY,CAACU,OAAO;MAClDC,WAAW,EAAE,aAAad,YAAY,CAACG,YAAY,CAACO,MAAM,EAAE;MAC5DK,SAAS,EAAE7E,WAAW,GAAG,eAAe,GAAG;IAC7C,CAAC;IACD8E,WAAW,EAAE;MACX,GAAGhB,YAAY,CAACiB,cAAc;MAC9BZ,KAAK,EAAEL,YAAY,CAACG,YAAY,CAACG,IAAI;MACrCF,UAAU,EAAEJ,YAAY,CAACG,YAAY,CAACU,OAAO;MAC7CH,MAAM,EAAE,aAAaV,YAAY,CAACG,YAAY,CAACO,MAAM,EAAE;MACvDQ,UAAU,EAAE,eAAe;MAC3B,SAAS,EAAE;QACTd,UAAU,EAAEJ,YAAY,CAACG,YAAY,CAACgB,OAAO;QAC7Cd,KAAK,EAAE;MACT;IACF,CAAC;IACDe,iBAAiB,EAAE;MACjB,GAAGpB,YAAY,CAACqB,oBAAoB;MACpChB,KAAK,EAAE,OAAO;MACdD,UAAU,EAAEJ,YAAY,CAACG,YAAY,CAACgB,OAAO;MAC7CT,MAAM,EAAE,aAAaV,YAAY,CAACG,YAAY,CAACgB,OAAO;IACxD,CAAC;IACDG,WAAW,EAAE;MACX,GAAGtB,YAAY,CAACuB,cAAc;MAC9BC,UAAU,EAAEtF,WAAW,GAAG,OAAO,GAAG,GAAG;MACvCgE,eAAe,EAAEF,YAAY,CAACG,YAAY,CAACC,UAAU;MACrDC,KAAK,EAAEL,YAAY,CAACG,YAAY,CAACG,IAAI;MACrCmB,SAAS,EAAE;IACb,CAAC;IACDC,IAAI,EAAE;MACJ,GAAG1B,YAAY,CAAC2B,OAAO;MACvBzB,eAAe,EAAEF,YAAY,CAACG,YAAY,CAACU,OAAO;MAClDR,KAAK,EAAEL,YAAY,CAACG,YAAY,CAACG,IAAI;MACrCI,MAAM,EAAE,aAAaV,YAAY,CAACG,YAAY,CAACO,MAAM,EAAE;MACvDkB,SAAS,EAAE,aAAa5B,YAAY,CAACG,YAAY,CAAC0B,MAAM;IAC1D,CAAC;IACDC,aAAa,EAAE;MACb,GAAG9B,YAAY,CAAC8B;IAClB,CAAC;IACDC,UAAU,EAAE;MACV,GAAG/B,YAAY,CAAC+B,UAAU;MAC1B7B,eAAe,EAAE,MAAM;MACvBG,KAAK,EAAE,MAAM;MACbK,MAAM,EAAE,gBAAgB;MACxB,SAAS,EAAE;QACTsB,WAAW,EAAEhC,YAAY,CAACG,YAAY,CAACgB,OAAO;QAC9Cc,OAAO,EAAE,MAAM;QACfL,SAAS,EAAE,aAAa5B,YAAY,CAACG,YAAY,CAACgB,OAAO;MAC3D;IACF,CAAC;IACDe,cAAc,EAAE;MACd,GAAGlC,YAAY,CAACkC,cAAc;MAC9BhC,eAAe,EAAEF,YAAY,CAACG,YAAY,CAACgB,OAAO;MAClDd,KAAK,EAAE;IACT,CAAC;IACD8B,aAAa,EAAE;MACb,GAAGnC,YAAY,CAACmC,aAAa;MAC7BjC,eAAe,EAAEF,YAAY,CAACG,YAAY,CAACiC,SAAS;MACpD/B,KAAK,EAAEL,YAAY,CAACG,YAAY,CAACG,IAAI;MACrCI,MAAM,EAAE;IACV,CAAC;IACD2B,WAAW,EAAE;MACX,GAAGrC,YAAY,CAACsC;IAClB,CAAC;IACDC,QAAQ,EAAE;MACR,GAAGvC,YAAY,CAACwC,WAAW;MAC3BtC,eAAe,EAAEF,YAAY,CAACG,YAAY,CAACU;IAC7C,CAAC;IACDtC,YAAY,EAAE;MACZ,GAAGyB,YAAY,CAACzB;IAClB;EACF,CAAC;;EAED;EACA,MAAMkE,QAAQ,GAAGA,CAACC,SAAS,EAAEC,KAAK,GAAG,KAAK,KAAK;IAC7C,MAAMC,SAAS,GAAG7C,MAAM,CAAC2C,SAAS,CAAC;IACnC,IAAI,OAAOE,SAAS,KAAK,UAAU,EAAE,OAAOA,SAAS,CAAC,CAAC;IACvD,IAAID,KAAK,IAAIC,SAAS,CAAC,SAAS,CAAC,EAAE;MACjC,OAAO;QAAE,GAAGA,SAAS;QAAE,GAAGA,SAAS,CAAC,SAAS;MAAE,CAAC;IAClD;IACA,OAAOA,SAAS;EAClB,CAAC;;EAED;EACAlM,SAAS,CAAC,MAAM;IACd,MAAMmM,WAAW,GAAGxL,kBAAkB,CAACJ,IAAI,EAAGuG,IAAI,IAAK;MACrD,IAAIA,IAAI,EAAE;QACRX,SAAS,CAACW,IAAI,CAACsF,GAAG,CAAC;MACrB,CAAC,MAAM;QACLC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxCjG,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC;IACF,OAAO,MAAM8F,WAAW,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;EAENnM,SAAS,CAAC,MAAM;IACd,IAAIkG,MAAM,EAAE;MACV,MAAMqG,gBAAgB,GAAG,MAAAA,CAAA,KAAY;QACnC,MAAMC,OAAO,GAAGlM,GAAG,CAACE,EAAE,EAAE,OAAO,EAAE0F,MAAM,CAAC;QACxC,MAAMuG,OAAO,GAAG,MAAMpM,MAAM,CAACmM,OAAO,CAAC;QAErC,IAAIC,OAAO,CAACC,MAAM,CAAC,CAAC,EAAE;UACpB,MAAMC,QAAQ,GAAGF,OAAO,CAACG,IAAI,CAAC,CAAC;UAC/B;UACAP,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEK,QAAQ,CAACE,EAAE,CAAC;QAC/C,CAAC,MAAM;UACLR,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;QAC9B;QACAjG,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC;MACDkG,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACrG,MAAM,CAAC,CAAC;;EAEZ;EACAlG,SAAS,CAAC,MAAM;IACd8M,KAAK,CAAC,oBAAoB,CAAC,CACxBC,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACpD,IAAI,CAAC,CAAC,CAAC,CACzBmD,IAAI,CAAEH,IAAI,IAAKrG,YAAY,CAACqG,IAAI,CAAC,CAAC,CAClCK,KAAK,CAAEC,GAAG,IAAKb,OAAO,CAACc,KAAK,CAAC,+BAA+B,EAAED,GAAG,CAAC,CAAC;EACxE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAlN,SAAS,CAAC,MAAM;IACd+I,QAAQ,CAACxI,IAAI,CAAC6M,UAAU,CAAC,CAAC,CAACL,IAAI,CAAC,CAAC;MAAEH,IAAI,EAAE;QAAES;MAAQ;IAAE,CAAC,KAAK;MACzDtG,OAAO,CAAC,CAAAsG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEvG,IAAI,KAAI,IAAI,CAAC;IAChC,CAAC,CAAC;IACF,MAAM;MAAE8F,IAAI,EAAEU;IAAS,CAAC,GAAGvE,QAAQ,CAACxI,IAAI,CAACgN,iBAAiB,CAAC,CAACC,MAAM,EAAEH,OAAO,KAAK;MAC9EtG,OAAO,CAAC,CAAAsG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEvG,IAAI,KAAI,IAAI,CAAC;IAChC,CAAC,CAAC;IACF,OAAO,MAAM;MACXwG,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEG,YAAY,CAACtB,WAAW,CAAC,CAAC;IACtC,CAAC;EACH,CAAC,EAAE,CAACpD,QAAQ,CAACxI,IAAI,CAAC,CAAC;;EAEnB;EACA,MAAMmN,kBAAkB,GAAG,MAAOC,CAAC,IAAK;IACtC,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACF,IAAI,IAAI,CAAC9G,IAAI,EAAE;IACpBK,sBAAsB,CAAC,IAAI,CAAC;IAC5B,MAAM4G,QAAQ,GAAG,GAAGjH,IAAI,CAACkH,EAAE,IAAIJ,IAAI,CAACK,IAAI,EAAE;IAC1C,MAAM;MAAEd;IAAM,CAAC,GAAG,MAAMpE,QAAQ,CAACmF,OAAO,CAACC,IAAI,CAAC,SAAS,CAAC,CAACC,MAAM,CAACL,QAAQ,EAAEH,IAAI,EAAE;MAAES,MAAM,EAAE;IAAK,CAAC,CAAC;IACjG,IAAI,CAAClB,KAAK,EAAE;MACV,MAAM;QAAEP,IAAI,EAAE0B;MAAQ,CAAC,GAAGvF,QAAQ,CAACmF,OAAO,CAACC,IAAI,CAAC,SAAS,CAAC,CAACI,YAAY,CAACR,QAAQ,CAAC;MACjF1G,YAAY,CAACiH,OAAO,CAACE,SAAS,CAAC;MAC/BC,gBAAgB,CAAC,+BAA+B,EAAE,SAAS,CAAC;MAC5DC,WAAW,CAAC,mBAAmB,CAAC;IAClC,CAAC,MAAM;MACLD,gBAAgB,CAAC,uBAAuB,EAAE,OAAO,CAAC;IACpD;IACAtH,sBAAsB,CAAC,KAAK,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMwH,oBAAoB,GAAG,MAAOhB,CAAC,IAAK;IACxC,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACF,IAAI,IAAI,CAAC9G,IAAI,EAAE;IACpBS,wBAAwB,CAAC,IAAI,CAAC;IAC9B,MAAMwG,QAAQ,GAAG,GAAGjH,IAAI,CAACkH,EAAE,IAAIJ,IAAI,CAACK,IAAI,EAAE;IAC1C,MAAM;MAAEd;IAAM,CAAC,GAAG,MAAMpE,QAAQ,CAACmF,OAAO,CAACC,IAAI,CAAC,WAAW,CAAC,CAACC,MAAM,CAACL,QAAQ,EAAEH,IAAI,EAAE;MAAES,MAAM,EAAE;IAAK,CAAC,CAAC;IACnG,IAAI,CAAClB,KAAK,EAAE;MACVsB,gBAAgB,CAAC,oBAAoB,EAAE,SAAS,CAAC;MACjDC,WAAW,CAAC,sBAAsBd,IAAI,CAACK,IAAI,EAAE,CAAC;IAChD,CAAC,MAAM;MACLQ,gBAAgB,CAAC,yBAAyB,EAAE,OAAO,CAAC;IACtD;IACAlH,wBAAwB,CAAC,KAAK,CAAC;EACjC,CAAC;;EAED;EACA,MAAMqH,kBAAkB,GAAIC,OAAO,IAAK;IACtC;IACApG,kBAAkB,CAACqG,IAAI,IAAI;MACzB,MAAMC,QAAQ,GAAGD,IAAI,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKJ,OAAO,CAAC;MAChD,OAAO,CAACA,OAAO,EAAE,GAAGE,QAAQ,CAAC,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC;IAEFR,WAAW,CAAC,UAAUG,OAAO,gBAAgB,CAAC;IAE9C,IAAIA,OAAO,CAAC9J,WAAW,CAAC,CAAC,KAAK,WAAW,EAAE;MACzCoK,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,uCAAuC;MAC9D;IACF;IACA,MAAMC,gBAAgB,GAAGT,OAAO,CAACU,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;IACpDJ,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,gBAAgBC,gBAAgB,OAAO;EAChE,CAAC;;EAED;EACA,MAAME,cAAc,GAAGA,CAACX,OAAO,EAAElB,CAAC,KAAK;IACrCA,CAAC,CAAC8B,eAAe,CAAC,CAAC,CAAC,CAAC;IACrBlH,oBAAoB,CAACuG,IAAI,IAAI;MAC3B,IAAIA,IAAI,CAACY,QAAQ,CAACb,OAAO,CAAC,EAAE;QAC1B,OAAOC,IAAI,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKJ,OAAO,CAAC;MACxC,CAAC,MAAM;QACL,OAAO,CAAC,GAAGC,IAAI,EAAED,OAAO,CAAC;MAC3B;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMc,mBAAmB,GAAGA,CAAA,KAAM;IAChChH,mBAAmB,CAAC,KAAK,CAAC;IAC1B8F,gBAAgB,CAAC,8CAA8C,EAAE,SAAS,CAAC;;IAE3E;IACA,MAAMmB,cAAc,GAAGC,QAAQ,CAACC,aAAa,CAAC,qBAAqB,CAAC;IACpE,MAAMC,eAAe,GAAGF,QAAQ,CAACC,aAAa,CAAC,wBAAwB,CAAC;IAExE,IAAIF,cAAc,EAAE;MAClBA,cAAc,CAACI,KAAK,CAACrG,KAAK,GAAG,MAAM;IACrC;IACA,IAAIoG,eAAe,EAAE;MACnBA,eAAe,CAACC,KAAK,CAACrG,KAAK,GAAG,MAAM;IACtC;EACF,CAAC;;EAED;EACA,MAAMsG,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAIlB,QAAQ,GAAG9F,SAAS;;IAExB;IACA,IAAIf,gBAAgB,KAAK,KAAK,EAAE;MAC9B,MAAMgI,iBAAiB,GAAGlH,iBAAiB,CAACd,gBAAgB,CAAC,IAAI,EAAE;MACnE6G,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACH,OAAO,IAChCqB,iBAAiB,CAACC,IAAI,CAACC,UAAU,IAC/BvB,OAAO,CAAC9J,WAAW,CAAC,CAAC,CAAC2K,QAAQ,CAACU,UAAU,CAACrL,WAAW,CAAC,CAAC,CAAC,IACxDqL,UAAU,CAACrL,WAAW,CAAC,CAAC,CAAC2K,QAAQ,CAACb,OAAO,CAAC9J,WAAW,CAAC,CAAC,CACzD,CACF,CAAC;IACH;;IAEA;IACAgK,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACH,OAAO,IAChCA,OAAO,CAAC9J,WAAW,CAAC,CAAC,CAAC2K,QAAQ,CAAChJ,UAAU,CAAC3B,WAAW,CAAC,CAAC,CACzD,CAAC;;IAED;IACA,IAAIqD,MAAM,KAAK,MAAM,EAAE;MACrB2G,QAAQ,CAACsB,IAAI,CAAC,CAAC;IACjB,CAAC,MAAM,IAAIjI,MAAM,KAAK,WAAW,EAAE;MACjC2G,QAAQ,CAACsB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QACtB,MAAMC,IAAI,GAAGlI,iBAAiB,CAACoH,QAAQ,CAACY,CAAC,CAAC;QAC1C,MAAMG,IAAI,GAAGnI,iBAAiB,CAACoH,QAAQ,CAACa,CAAC,CAAC;QAC1C,IAAIC,IAAI,IAAI,CAACC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC5B,IAAI,CAACD,IAAI,IAAIC,IAAI,EAAE,OAAO,CAAC;QAC3B,OAAOH,CAAC,CAACI,aAAa,CAACH,CAAC,CAAC;MAC3B,CAAC,CAAC;IACJ;IAEA,OAAOxB,QAAQ;EACjB,CAAC;;EAED;EACA,MAAM4B,YAAY,GAAIC,GAAG,IAAK;IAC5BzB,MAAM,CAAC0B,IAAI,CAACD,GAAG,EAAE,QAAQ,CAAC;EAC5B,CAAC;;EAED;EACA,MAAME,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAAChL,KAAK,CAACiL,IAAI,CAAC,CAAC,EAAE;IAEnB,MAAMC,WAAW,GAAG;MAAEC,IAAI,EAAE,MAAM;MAAEC,OAAO,EAAEpL;IAAM,CAAC;IACpDG,WAAW,CAAE6I,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEkC,WAAW,CAAC,CAAC;IAC7CjL,QAAQ,CAAC,EAAE,CAAC;IACZM,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MAAA,IAAA8K,oBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACF,MAAMC,MAAM,GAAG,oOAAoOlL,SAAS,iBAAiBR,KAAK,EAAE;MAEpR,MAAMkH,GAAG,GAAG,MAAMvM,KAAK,CAACgR,IAAI,CAC1B,gGAAgG7I,OAAO,EAAE,EACzG;QACE8I,QAAQ,EAAE,CACR;UACEC,KAAK,EAAE,CAAC;YAAE/H,IAAI,EAAE4H;UAAO,CAAC;QAC1B,CAAC;MAEL,CAAC,EACD;QACEI,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CACF,CAAC;MAED,MAAMC,QAAQ,GACZ,EAAAV,oBAAA,GAAAnE,GAAG,CAACJ,IAAI,CAACkF,UAAU,cAAAX,oBAAA,wBAAAC,qBAAA,GAAnBD,oBAAA,CAAsB,CAAC,CAAC,cAAAC,qBAAA,wBAAAC,sBAAA,GAAxBD,qBAAA,CAA0BF,OAAO,cAAAG,sBAAA,wBAAAC,sBAAA,GAAjCD,sBAAA,CAAmCM,KAAK,cAAAL,sBAAA,wBAAAC,sBAAA,GAAxCD,sBAAA,CAA2C,CAAC,CAAC,cAAAC,sBAAA,uBAA7CA,sBAAA,CAA+C3H,IAAI,KACnD,yBAAyB;MAC3B,MAAMmI,UAAU,GAAG;QAAEd,IAAI,EAAE,KAAK;QAAEC,OAAO,EAAEW;MAAS,CAAC;MACrD5L,WAAW,CAAE6I,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEiD,UAAU,CAAC,CAAC;IAC9C,CAAC,CAAC,OAAO5E,KAAK,EAAE;MACdd,OAAO,CAACc,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzClH,WAAW,CAAE6I,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QAAEmC,IAAI,EAAE,KAAK;QAAEC,OAAO,EAAE,WAAW,GAAG/D,KAAK,CAAC6E;MAAQ,CAAC,CACtD,CAAC;IACJ,CAAC,SAAS;MACR3L,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;;EAEA;EACA,MAAM4L,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMlJ,QAAQ,CAACxI,IAAI,CAAC2R,OAAO,CAAC,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMzD,gBAAgB,GAAGA,CAAC0D,GAAG,EAAEC,IAAI,GAAG,MAAM,KAAK;IAC/CtK,eAAe,CAAC;MAAEqK,GAAG;MAAEC;IAAK,CAAC,CAAC;IAC9BC,UAAU,CAAC,MAAMvK,eAAe,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;EAC/C,CAAC;;EAED;EACA,MAAM4G,WAAW,GAAIyD,GAAG,IAAK;IAC3BnK,cAAc,CAACsE,GAAG,IAAI,CACpB;MAAE8F,IAAI,EAAE,UAAU;MAAEE,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAAEL;IAAI,CAAC,EACzD,GAAG7F,GAAG,CAAC4C,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CACpB,CAAC;EACJ,CAAC;;EAED;EACA,MAAMuD,UAAU,GAAIC,IAAI,IAAK;IAC3B7L,gBAAgB,CAACiI,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP,CAAC4D,IAAI,GAAG,CAAC5D,IAAI,CAAC4D,IAAI;IACpB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA1S,SAAS,CAAC,MAAM;IACd,IAAIiI,UAAU,CAAC0K,OAAO,EAAE1K,UAAU,CAAC0K,OAAO,CAACC,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACnF,CAAC,EAAE,CAAC7M,QAAQ,EAAEI,OAAO,CAAC,CAAC;;EAEvB;EACA,MAAM0M,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,IAAI,GAAG,EAAE;IACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC3B,MAAMC,CAAC,GAAG,IAAIV,IAAI,CAAC,CAAC;MACpBU,CAAC,CAACC,OAAO,CAACD,CAAC,CAACE,OAAO,CAAC,CAAC,GAAGH,CAAC,CAAC;MAC1BD,IAAI,CAACK,IAAI,CAACH,CAAC,CAACI,kBAAkB,CAAC,CAAC,CAAC;IACnC;IACA,OAAON,IAAI;EACb,CAAC;EAED,MAAMO,WAAW,GAAGR,YAAY,CAAC,CAAC;EAClC,MAAMS,SAAS,GAAG;IAChBC,MAAM,EAAEF,WAAW;IACnBG,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,kBAAkB;MACzB9G,IAAI,EAAE0G,WAAW,CAACjP,GAAG,CAACsP,GAAG,IAAI5L,WAAW,CAACiH,MAAM,CAACsB,CAAC,IAAIA,CAAC,CAAC8B,IAAI,KAAK,UAAU,IAAI9B,CAAC,CAAC6B,GAAG,CAACyB,UAAU,CAAC,mBAAmB,CAAC,IAAI,IAAIrB,IAAI,CAACjC,CAAC,CAACgC,IAAI,CAAC,CAACe,kBAAkB,CAAC,CAAC,KAAKM,GAAG,CAAC,CAACE,MAAM,CAAC;MAC7KrK,eAAe,EAAE;IACnB,CAAC,EACD;MACEkK,KAAK,EAAE,iBAAiB;MACxB9G,IAAI,EAAE0G,WAAW,CAACjP,GAAG,CAACsP,GAAG,IAAI5L,WAAW,CAACiH,MAAM,CAACsB,CAAC,IAAIA,CAAC,CAAC8B,IAAI,KAAK,UAAU,IAAI9B,CAAC,CAAC6B,GAAG,KAAK,8BAA8B,IAAI,IAAII,IAAI,CAACjC,CAAC,CAACgC,IAAI,CAAC,CAACe,kBAAkB,CAAC,CAAC,KAAKM,GAAG,CAAC,CAACE,MAAM,CAAC;MAChLrK,eAAe,EAAE;IACnB,CAAC;EAEL,CAAC;EAED,MAAMsK,YAAY,GAAG;IACnBC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE;MACPC,MAAM,EAAE;QAAEC,QAAQ,EAAE;MAAM,CAAC;MAC3BC,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAK;IAC3B,CAAC;IACDC,MAAM,EAAE;MACNC,CAAC,EAAE;QAAEC,WAAW,EAAE,IAAI;QAAEC,KAAK,EAAE;UAAEC,QAAQ,EAAE;QAAE;MAAE;IACjD;EACF,CAAC;EAED,oBACEjS,OAAA;IAAKwN,KAAK,EAAEjE,QAAQ,CAAC,cAAc,CAAE;IAAA2I,QAAA,gBAEnClS,OAAA;MAAKwN,KAAK,EAAEjE,QAAQ,CAAC,QAAQ,CAAE;MAAA2I,QAAA,gBAC7BlS,OAAA;QACEwN,KAAK,EAAE;UACLtG,UAAU,EAAE,MAAM;UAClBM,MAAM,EAAE,MAAM;UACdL,KAAK,EAAE,OAAO;UAAE;UAChBgL,WAAW,EAAE,MAAM;UACnBC,MAAM,EAAE,SAAS;UACjBC,OAAO,EAAE,KAAK;UACdC,YAAY,EAAE,KAAK;UACnBtK,UAAU,EAAE;QACd,CAAE;QACFuK,OAAO,EAAEA,CAAA,KAAMC,cAAc,CAAC,CAACxP,WAAW,CAAE;QAC5CyP,YAAY,EAAGtH,CAAC,IAAKA,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACtG,UAAU,GAAG,0BAA2B;QAC5EwL,YAAY,EAAGvH,CAAC,IAAKA,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACtG,UAAU,GAAG,MAAO;QAAAgL,QAAA,EAEvDlP,WAAW,gBAAGhD,OAAA,CAAC2S,GAAG;UAACC,IAAI,EAAE;QAAG;UAAA5Q,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGnC,OAAA,CAAC6S,MAAM;UAACD,IAAI,EAAE;QAAG;UAAA5Q,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,eAETnC,OAAA;QAAKwN,KAAK,EAAE;UAAEsF,IAAI,EAAE,CAAC;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAd,QAAA,gBAC7DlS,OAAA;UACEiT,GAAG,EAAEC,OAAO,CAAC,kBAAkB,CAAE;UACjCC,GAAG,EAAC,YAAY;UAChB3F,KAAK,EAAE;YAAE4F,MAAM,EAAE,MAAM;YAAEjB,WAAW,EAAE;UAAO;QAAE;UAAAnQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACFnC,OAAA;UAAAkS,QAAA,gBACElS,OAAA;YACE,yBAAiB;YACjBwN,KAAK,EAAE;cAAE6F,UAAU,EAAE,GAAG;cAAEC,QAAQ,EAAE,MAAM;cAAEnM,KAAK,EAAE;YAAQ,CAAE;YAAA+K,QAAA,EAC9D;UAED;YAAAlQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNnC,OAAA;YACE,4BAAoB;YACpBwN,KAAK,EAAE;cAAE8F,QAAQ,EAAE,MAAM;cAAEC,OAAO,EAAE,GAAG;cAAEpM,KAAK,EAAE;YAAQ,CAAE;YAAA+K,QAAA,EAC3D;UAED;YAAAlQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnC,OAAA;QAAKwN,KAAK,EAAE;UAAEuF,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEQ,GAAG,EAAE;QAAO,CAAE;QAAAtB,QAAA,EAChE5N,IAAI,gBACHtE,OAAA,CAAAE,SAAA;UAAAgS,QAAA,gBACElS,OAAA;YAAKwN,KAAK,EAAE;cACViG,KAAK,EAAE,MAAM;cACbL,MAAM,EAAE,MAAM;cACdd,YAAY,EAAE,KAAK;cACnBpL,UAAU,EAAE,0BAA0B;cACtC6L,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBU,cAAc,EAAE,QAAQ;cACxBL,UAAU,EAAE,GAAG;cACfjB,MAAM,EAAE,SAAS;cACjBjL,KAAK,EAAE,OAAO;cACdwM,cAAc,EAAE;YAClB,CAAE;YAAAzB,QAAA,EACC5N,IAAI,CAACsP,KAAK,KAAK3O,WAAW,gBAAGjF,OAAA,CAACb,QAAQ;cAACyT,IAAI,EAAE,EAAG;cAACzL,KAAK,EAAC;YAAS;cAAAnF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAGmC,IAAI,CAACsP,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;UAAC;YAAA7R,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/F,CAAC,eACNnC,OAAA;YACEwN,KAAK,EAAE;cACL,GAAGjE,QAAQ,CAAC,eAAe,CAAC;cAC5BrC,UAAU,EAAE,0BAA0B;cACtCC,KAAK,EAAE,OAAO;cACdK,MAAM,EAAE,oCAAoC;cAC5CmM,cAAc,EAAE;YAClB,CAAE;YACFpB,OAAO,EAAE9C,YAAa;YAAAyC,QAAA,gBAEtBlS,OAAA,CAAC8T,QAAQ;cAAA9R,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,WACd;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CAAC,gBAEHnC,OAAA;UACEwN,KAAK,EAAE;YACL,GAAGjE,QAAQ,CAAC,eAAe,CAAC;YAC5BrC,UAAU,EAAE,0BAA0B;YACtCC,KAAK,EAAE,OAAO;YACdK,MAAM,EAAE,oCAAoC;YAC5CmM,cAAc,EAAE;UAClB,CAAE;UACFpB,OAAO,EAAEA,CAAA,KAAM;YAAE1I,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;UAAE,CAAE;UAAAoI,QAAA,gBAEzElS,OAAA,CAAC+T,OAAO;YAAA/R,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnC,OAAA;MAAOwN,KAAK,EAAEjE,QAAQ,CAAC,SAAS,CAAE;MAAA2I,QAAA,eAChClS,OAAA;QAAKwN,KAAK,EAAE;UAAE6E,OAAO,EAAE;QAAO,CAAE;QAAAH,QAAA,EAC7BtQ,mBAAmB,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEkS,KAAK,kBACnChU,OAAA;UAAAkS,QAAA,gBACElS,OAAA;YACEwN,KAAK,EAAE;cACL,GAAGjE,QAAQ,CAAC,aAAa,CAAC;cAC1B,IAAIvF,SAAS,KAAKlC,IAAI,CAACO,GAAG,GAAGkH,QAAQ,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC;cAChE6I,MAAM,EAAE,SAAS;cACjBpK,UAAU,EAAE;YACd,CAAE;YACFuK,OAAO,EAAEA,CAAA,KAAM;cACbtO,YAAY,CAACnC,IAAI,CAACO,GAAG,CAAC;cACtBmQ,cAAc,CAAC,KAAK,CAAC;YACvB,CAAE;YACFC,YAAY,EAAGtH,CAAC,IAAK;cACnB,IAAInH,SAAS,KAAKlC,IAAI,CAACO,GAAG,EAAE;gBAC1B8I,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACtG,UAAU,GAAG,qBAAqB;gBACjDiE,CAAC,CAACE,MAAM,CAACmC,KAAK,CAAC3F,SAAS,GAAG,iBAAiB;gBAC5CsD,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACyG,UAAU,GAAG,aAAanN,YAAY,CAACG,YAAY,CAACgB,OAAO,EAAE;cAC9E;YACF,CAAE;YACFyK,YAAY,EAAGvH,CAAC,IAAK;cACnB,IAAInH,SAAS,KAAKlC,IAAI,CAACO,GAAG,EAAE;gBAC1B8I,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACtG,UAAU,GAAGJ,YAAY,CAACG,YAAY,CAACU,OAAO;gBAC7DwD,CAAC,CAACE,MAAM,CAACmC,KAAK,CAAC3F,SAAS,GAAG,eAAe;gBAC1CsD,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACyG,UAAU,GAAG,uBAAuB;cACrD;YACF,CAAE;YAAA/B,QAAA,gBAEFlS,OAAA;cAAKwN,KAAK,EAAE;gBAAE2E,WAAW,EAAE;cAAO,CAAE;cAAAD,QAAA,EAAEpQ,IAAI,CAACM;YAAI;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtDnC,OAAA;cAAMwN,KAAK,EAAE;gBAAEsF,IAAI,EAAE;cAAE,CAAE;cAAAZ,QAAA,EAAEpQ,IAAI,CAACQ;YAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAC5CL,IAAI,CAACoS,QAAQ,CAAC7C,MAAM,GAAG,CAAC,iBACvBrR,OAAA;cAAKuS,OAAO,EAAGpH,CAAC,IAAK;gBACnBA,CAAC,CAAC8B,eAAe,CAAC,CAAC;gBACnBgD,UAAU,CAACnO,IAAI,CAACQ,KAAK,CAAC;cACxB,CAAE;cAAA4P,QAAA,EACC9N,aAAa,CAACtC,IAAI,CAACQ,KAAK,CAAC,gBAAGtC,OAAA,CAACmU,aAAa;gBAAAnS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGnC,OAAA,CAACoU,cAAc;gBAAApS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAELL,IAAI,CAACoS,QAAQ,CAAC7C,MAAM,GAAG,CAAC,IAAIjN,aAAa,CAACtC,IAAI,CAACQ,KAAK,CAAC,iBACpDtC,OAAA;YAAKwN,KAAK,EAAE;cAAElF,UAAU,EAAE;YAAO,CAAE;YAAA4J,QAAA,EAChCpQ,IAAI,CAACoS,QAAQ,CAACrS,GAAG,CAAC,CAACwS,OAAO,EAAEC,QAAQ,kBACnCtU,OAAA;cAEEwN,KAAK,EAAE;gBACL,GAAGjE,QAAQ,CAAC,aAAa,CAAC;gBAC1B8I,OAAO,EAAE,mBAAmB;gBAC5BiB,QAAQ,EAAE,MAAM;gBAChBC,OAAO,EAAE;cACX,CAAE;cACFhB,OAAO,EAAEA,CAAA,KAAM;gBACbtO,YAAY,CAACnC,IAAI,CAACO,GAAG,CAAC;gBACtBmQ,cAAc,CAAC,KAAK,CAAC;cACvB,CAAE;cACFC,YAAY,EAAGtH,CAAC,IAAK;gBACnBA,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACtG,UAAU,GAAG,qBAAqB;gBACjDiE,CAAC,CAACE,MAAM,CAACmC,KAAK,CAAC+G,WAAW,GAAG,MAAM;gBACnCpJ,CAAC,CAACE,MAAM,CAACmC,KAAK,CAAC+F,OAAO,GAAG,GAAG;cAC9B,CAAE;cACFb,YAAY,EAAGvH,CAAC,IAAK;gBACnBA,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACtG,UAAU,GAAGJ,YAAY,CAACG,YAAY,CAACU,OAAO;gBAC7DwD,CAAC,CAACE,MAAM,CAACmC,KAAK,CAAC+G,WAAW,GAAG,MAAM;gBACnCpJ,CAAC,CAACE,MAAM,CAACmC,KAAK,CAAC+F,OAAO,GAAG,KAAK;cAChC,CAAE;cAAArB,QAAA,EAEDmC,OAAO,CAAC/R;YAAK,GAtBTgS,QAAQ;cAAAtS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuBV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA,GArEO6R,KAAK;UAAAhS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsEV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRnC,OAAA;MAAMwN,KAAK,EAAEjE,QAAQ,CAAC,aAAa,CAAE;MAAA2I,QAAA,GAElClP,WAAW,IAAI2J,MAAM,CAAC6H,UAAU,GAAG,GAAG,iBACrCxU,OAAA;QACEwN,KAAK,EAAE;UACLkE,QAAQ,EAAE,OAAO;UACjB+C,GAAG,EAAE,MAAM;UACXC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACT5N,eAAe,EAAE,iBAAiB;UAClC6N,MAAM,EAAE;QACV,CAAE;QACFtC,OAAO,EAAEA,CAAA,KAAMC,cAAc,CAAC,KAAK;MAAE;QAAAxQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CACF,EAGA6B,SAAS,KAAK,WAAW,iBACxBhE,OAAA;QAAKwN,KAAK,EAAE;UACV6E,OAAO,EAAE,MAAM;UACfnL,UAAU,EAAE,mDAAmD;UAC/DqB,SAAS,EAAE,OAAO;UAClBmJ,QAAQ,EAAE,UAAU;UACpBoD,QAAQ,EAAE;QACZ,CAAE;QAAA5C,QAAA,gBAEAlS,OAAA;UAAKwN,KAAK,EAAE;YACVkE,QAAQ,EAAE,UAAU;YACpB+C,GAAG,EAAE,KAAK;YACVC,IAAI,EAAE,IAAI;YACVjB,KAAK,EAAE,OAAO;YACdL,MAAM,EAAE,OAAO;YACflM,UAAU,EAAE,uEAAuE;YACnFoL,YAAY,EAAE,KAAK;YACnByC,SAAS,EAAE,+BAA+B;YAC1CF,MAAM,EAAE;UACV;QAAE;UAAA7S,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACLnC,OAAA;UAAKwN,KAAK,EAAE;YACVkE,QAAQ,EAAE,UAAU;YACpB+C,GAAG,EAAE,KAAK;YACVE,KAAK,EAAE,KAAK;YACZlB,KAAK,EAAE,OAAO;YACdL,MAAM,EAAE,OAAO;YACflM,UAAU,EAAE,wEAAwE;YACpFoL,YAAY,EAAE,KAAK;YACnByC,SAAS,EAAE,uCAAuC;YAClDF,MAAM,EAAE;UACV;QAAE;UAAA7S,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACLnC,OAAA;UAAKwN,KAAK,EAAE;YACVkE,QAAQ,EAAE,UAAU;YACpBkD,MAAM,EAAE,KAAK;YACbF,IAAI,EAAE,KAAK;YACXjB,KAAK,EAAE,OAAO;YACdL,MAAM,EAAE,OAAO;YACflM,UAAU,EAAE,wEAAwE;YACpFoL,YAAY,EAAE,KAAK;YACnByC,SAAS,EAAE,gCAAgC;YAC3CF,MAAM,EAAE;UACV;QAAE;UAAA7S,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAELnC,OAAA;UAAKwN,KAAK,EAAE;YAAEwH,QAAQ,EAAE,QAAQ;YAAEC,MAAM,EAAE,QAAQ;YAAEvD,QAAQ,EAAE,UAAU;YAAEmD,MAAM,EAAE;UAAE,CAAE;UAAA3C,QAAA,gBAGpFlS,OAAA;YAAKwN,KAAK,EAAE;cACVtG,UAAU,EAAE,wEAAwE;cACpFyM,cAAc,EAAE,YAAY;cAC5BrB,YAAY,EAAE,MAAM;cACpBD,OAAO,EAAE,MAAM;cACf6C,YAAY,EAAE,MAAM;cACpB/N,KAAK,EAAE,OAAO;cACduK,QAAQ,EAAE,UAAU;cACpBoD,QAAQ,EAAE,QAAQ;cAClBtN,MAAM,EAAE,iCAAiC;cACzCkB,SAAS,EAAE;YACb,CAAE;YAAAwJ,QAAA,gBAEAlS,OAAA;cAAKwN,KAAK,EAAE;gBACVkE,QAAQ,EAAE,UAAU;gBACpB+C,GAAG,EAAE,MAAM;gBACXE,KAAK,EAAE,MAAM;gBACbrB,QAAQ,EAAE,MAAM;gBAChByB,SAAS,EAAE;cACb,CAAE;cAAA7C,QAAA,EAAC;YAAE;cAAAlQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACXnC,OAAA;cAAKwN,KAAK,EAAE;gBACVkE,QAAQ,EAAE,UAAU;gBACpBkD,MAAM,EAAE,MAAM;gBACdF,IAAI,EAAE,MAAM;gBACZpB,QAAQ,EAAE,QAAQ;gBAClByB,SAAS,EAAE;cACb,CAAE;cAAA7C,QAAA,EAAC;YAAC;cAAAlQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACVnC,OAAA;cAAKwN,KAAK,EAAE;gBACVkE,QAAQ,EAAE,UAAU;gBACpB+C,GAAG,EAAE,KAAK;gBACVE,KAAK,EAAE,KAAK;gBACZrB,QAAQ,EAAE,QAAQ;gBAClByB,SAAS,EAAE;cACb,CAAE;cAAA7C,QAAA,EAAC;YAAE;cAAAlQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAEXnC,OAAA;cAAKwN,KAAK,EAAE;gBAAEuF,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEQ,GAAG,EAAE,MAAM;gBAAE9B,QAAQ,EAAE,UAAU;gBAAEmD,MAAM,EAAE;cAAE,CAAE;cAAA3C,QAAA,gBAClGlS,OAAA;gBAAKwN,KAAK,EAAE;kBACViG,KAAK,EAAE,OAAO;kBACdL,MAAM,EAAE,OAAO;kBACfd,YAAY,EAAE,KAAK;kBACnBpL,UAAU,EAAE,2CAA2C;kBACvD6L,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBU,cAAc,EAAE,QAAQ;kBACxBJ,QAAQ,EAAE,MAAM;kBAChBD,UAAU,EAAE,MAAM;kBAClB7L,MAAM,EAAE,oCAAoC;kBAC5CkB,SAAS,EAAE,6BAA6B;kBACxCqM,SAAS,EAAE;gBACb,CAAE;gBAAA7C,QAAA,EACC5N,IAAI,GAAGA,IAAI,CAACsP,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG;cAAI;gBAAA7R,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACNnC,OAAA;gBAAAkS,QAAA,gBACElS,OAAA;kBAAIwN,KAAK,EAAE;oBACTyH,MAAM,EAAE,CAAC;oBACT3B,QAAQ,EAAE,QAAQ;oBAClBD,UAAU,EAAE,GAAG;oBACf6B,YAAY,EAAE,MAAM;oBACpBhO,UAAU,EAAE,uCAAuC;oBACnDiO,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE,aAAa;oBAClCC,UAAU,EAAE;kBACd,CAAE;kBAAAnD,QAAA,GAAC,MACG,EAAC5N,IAAI,GAAGA,IAAI,CAACsP,KAAK,CAAC0B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,EAAC,gBACpD;gBAAA;kBAAAtT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLnC,OAAA;kBAAGwN,KAAK,EAAE;oBACRyH,MAAM,EAAE,CAAC;oBACT3B,QAAQ,EAAE,QAAQ;oBAClBC,OAAO,EAAE,IAAI;oBACbF,UAAU,EAAE;kBACd,CAAE;kBAAAnB,QAAA,EAAC;gBAEH;kBAAAlQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJnC,OAAA;kBAAKwN,KAAK,EAAE;oBACV+H,SAAS,EAAE,MAAM;oBACjBxC,OAAO,EAAE,MAAM;oBACfS,GAAG,EAAE;kBACP,CAAE;kBAAAtB,QAAA,gBACAlS,OAAA;oBAAKwN,KAAK,EAAE;sBACVtG,UAAU,EAAE,uBAAuB;sBACnCmL,OAAO,EAAE,aAAa;sBACtBC,YAAY,EAAE,MAAM;sBACpBgB,QAAQ,EAAE,QAAQ;sBAClBD,UAAU,EAAE;oBACd,CAAE;oBAAAnB,QAAA,EAAC;kBAEH;oBAAAlQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNnC,OAAA;oBAAKwN,KAAK,EAAE;sBACVtG,UAAU,EAAE,uBAAuB;sBACnCmL,OAAO,EAAE,aAAa;sBACtBC,YAAY,EAAE,MAAM;sBACpBgB,QAAQ,EAAE,QAAQ;sBAClBD,UAAU,EAAE;oBACd,CAAE;oBAAAnB,QAAA,EAAC;kBAEH;oBAAAlQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNnC,OAAA;YAAKwN,KAAK,EAAE;cACVuF,OAAO,EAAE,MAAM;cACfyC,mBAAmB,EAAE,sCAAsC;cAC3DhC,GAAG,EAAE,QAAQ;cACb0B,YAAY,EAAE;YAChB,CAAE;YAAAhD,QAAA,EACC,CACC;cACE5P,KAAK,EAAE,aAAa;cACpBmT,KAAK,EAAE,IAAI;cACXC,IAAI,EAAE,MAAM;cACZtT,IAAI,EAAE,IAAI;cACV+E,KAAK,EAAE,SAAS;cAChBwO,UAAU,EAAE,2CAA2C;cACvDC,SAAS,EAAE,SAAS;cACpBjP,WAAW,EAAE;YACf,CAAC,EACD;cACErE,KAAK,EAAE,cAAc;cACrBmT,KAAK,EAAE,OAAO;cACdC,IAAI,EAAE,IAAI;cACVtT,IAAI,EAAE,GAAG;cACT+E,KAAK,EAAE,SAAS;cAChBwO,UAAU,EAAE,2CAA2C;cACvDC,SAAS,EAAE,SAAS;cACpBjP,WAAW,EAAE;YACf,CAAC,EACD;cACErE,KAAK,EAAE,aAAa;cACpBmT,KAAK,EAAE,IAAI;cACXC,IAAI,EAAE,OAAO;cACbtT,IAAI,EAAE,IAAI;cACV+E,KAAK,EAAE,SAAS;cAChBwO,UAAU,EAAE,2CAA2C;cACvDC,SAAS,EAAE,SAAS;cACpBjP,WAAW,EAAE;YACf,CAAC,EACD;cACErE,KAAK,EAAE,cAAc;cACrBmT,KAAK,EAAE,IAAI;cACXC,IAAI,EAAE,UAAU;cAChBtT,IAAI,EAAE,IAAI;cACV+E,KAAK,EAAE,SAAS;cAChBwO,UAAU,EAAE,2CAA2C;cACvDC,SAAS,EAAE,SAAS;cACpBjP,WAAW,EAAE;YACf,CAAC,CACF,CAAC9E,GAAG,CAAC,CAACgU,IAAI,EAAE7B,KAAK,kBAChBhU,OAAA;cAAiBwN,KAAK,EAAE;gBACtBtG,UAAU,EAAE,wEAAwE;gBACpFyM,cAAc,EAAE,YAAY;gBAC5BrB,YAAY,EAAE,MAAM;gBACpBD,OAAO,EAAE,MAAM;gBACfX,QAAQ,EAAE,UAAU;gBACpBoD,QAAQ,EAAE,QAAQ;gBAClBtN,MAAM,EAAE,iCAAiC;gBACzC4K,MAAM,EAAE,SAAS;gBACjBpK,UAAU,EAAE,kDAAkD;gBAC9DU,SAAS,EAAE,0CAA0CmN,IAAI,CAACD,SAAS;cACrE,CAAE;cACFnD,YAAY,EAAGtH,CAAC,IAAK;gBACnBA,CAAC,CAAC2K,aAAa,CAACtI,KAAK,CAAC3F,SAAS,GAAG,+BAA+B;gBACjEsD,CAAC,CAAC2K,aAAa,CAACtI,KAAK,CAAC9E,SAAS,GAAG,yCAAyCmN,IAAI,CAACD,SAAS,IAAI;cAC/F,CAAE;cACFlD,YAAY,EAAGvH,CAAC,IAAK;gBACnBA,CAAC,CAAC2K,aAAa,CAACtI,KAAK,CAAC3F,SAAS,GAAG,wBAAwB;gBAC1DsD,CAAC,CAAC2K,aAAa,CAACtI,KAAK,CAAC9E,SAAS,GAAG,0CAA0CmN,IAAI,CAACD,SAAS,IAAI;cAChG,CAAE;cAAA1D,QAAA,gBAGAlS,OAAA;gBAAKwN,KAAK,EAAE;kBACVkE,QAAQ,EAAE,UAAU;kBACpB+C,GAAG,EAAE,MAAM;kBACXC,IAAI,EAAE,MAAM;kBACZjB,KAAK,EAAE,MAAM;kBACbL,MAAM,EAAE,MAAM;kBACdlM,UAAU,EAAE,0CAA0C2O,IAAI,CAAC1O,KAAK,kBAAkB;kBAClF4N,SAAS,EAAE,4BAA4B;kBACvCxB,OAAO,EAAE;gBACX;cAAE;gBAAAvR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAGLnC,OAAA;gBAAKwN,KAAK,EAAE;kBACVkE,QAAQ,EAAE,UAAU;kBACpB+C,GAAG,EAAE,MAAM;kBACXE,KAAK,EAAE,MAAM;kBACblB,KAAK,EAAE,MAAM;kBACbL,MAAM,EAAE,MAAM;kBACdlM,UAAU,EAAE2O,IAAI,CAACF,UAAU;kBAC3BrD,YAAY,EAAE,KAAK;kBACnBiB,OAAO,EAAE,GAAG;kBACZ/G,MAAM,EAAE;gBACV;cAAE;gBAAAxK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAELnC,OAAA;gBAAKwN,KAAK,EAAE;kBAAEkE,QAAQ,EAAE,UAAU;kBAAEmD,MAAM,EAAE;gBAAE,CAAE;gBAAA3C,QAAA,gBAE9ClS,OAAA;kBAAKwN,KAAK,EAAE;oBACVuF,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBU,cAAc,EAAE,eAAe;oBAC/BwB,YAAY,EAAE;kBAChB,CAAE;kBAAAhD,QAAA,gBACAlS,OAAA;oBAAKwN,KAAK,EAAE;sBACV8F,QAAQ,EAAE,QAAQ;sBAClBD,UAAU,EAAE,GAAG;sBACflM,KAAK,EAAE,uBAAuB;sBAC9B4O,aAAa,EAAE;oBACjB,CAAE;oBAAA7D,QAAA,EACC2D,IAAI,CAACvT;kBAAK;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC,eACNnC,OAAA;oBAAKwN,KAAK,EAAE;sBACV8F,QAAQ,EAAE,MAAM;sBAChB9G,MAAM,EAAE;oBACV,CAAE;oBAAA0F,QAAA,EACC2D,IAAI,CAACzT;kBAAI;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNnC,OAAA;kBAAKwN,KAAK,EAAE;oBACVuF,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,UAAU;oBACtBQ,GAAG,EAAE,QAAQ;oBACb0B,YAAY,EAAE;kBAChB,CAAE;kBAAAhD,QAAA,gBACAlS,OAAA;oBAAKwN,KAAK,EAAE;sBACV8F,QAAQ,EAAE,MAAM;sBAChBD,UAAU,EAAE,GAAG;sBACflM,KAAK,EAAE,OAAO;sBACdkO,UAAU,EAAE,YAAYQ,IAAI,CAAC1O,KAAK,EAAE;sBACpC6O,UAAU,EAAE;oBACd,CAAE;oBAAA9D,QAAA,EACC2D,IAAI,CAACJ;kBAAK;oBAAAzT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC,eACNnC,OAAA;oBAAKwN,KAAK,EAAE;sBACV8F,QAAQ,EAAE,QAAQ;sBAClBD,UAAU,EAAE,GAAG;sBACflM,KAAK,EAAE0O,IAAI,CAAC1O,KAAK;sBACjB8O,aAAa,EAAE,WAAW;sBAC1BF,aAAa,EAAE;oBACjB,CAAE;oBAAA7D,QAAA,EACC2D,IAAI,CAACH;kBAAI;oBAAA1T,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNnC,OAAA;kBAAKwN,KAAK,EAAE;oBACV8F,QAAQ,EAAE,QAAQ;oBAClBnM,KAAK,EAAE,uBAAuB;oBAC9BkM,UAAU,EAAE,GAAG;oBACf6C,SAAS,EAAE;kBACb,CAAE;kBAAAhE,QAAA,EACC2D,IAAI,CAAClP;gBAAW;kBAAA3E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,eAGNnC,OAAA;kBAAKwN,KAAK,EAAE;oBACV+H,SAAS,EAAE,MAAM;oBACjBnC,MAAM,EAAE,KAAK;oBACblM,UAAU,EAAE,uBAAuB;oBACnCoL,YAAY,EAAE,KAAK;oBACnBwC,QAAQ,EAAE;kBACZ,CAAE;kBAAA5C,QAAA,eACAlS,OAAA;oBAAKwN,KAAK,EAAE;sBACV4F,MAAM,EAAE,MAAM;sBACdK,KAAK,EAAE,GAAG0C,IAAI,CAACC,GAAG,CAAC,GAAG,EAAE,CAACpC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG;sBAC5C9M,UAAU,EAAE2O,IAAI,CAACF,UAAU;sBAC3BrD,YAAY,EAAE,KAAK;sBACnByC,SAAS,EAAE;oBACb;kBAAE;oBAAA/S,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GA3HE6R,KAAK;cAAAhS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA4HV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNnC,OAAA;YAAKwN,KAAK,EAAE;cACVtG,UAAU,EAAE,wEAAwE;cACpFyM,cAAc,EAAE,YAAY;cAC5BrB,YAAY,EAAE,MAAM;cACpBD,OAAO,EAAE,MAAM;cACf6C,YAAY,EAAE,MAAM;cACpB1N,MAAM,EAAE,iCAAiC;cACzCkB,SAAS,EAAE,6BAA6B;cACxCgJ,QAAQ,EAAE,UAAU;cACpBoD,QAAQ,EAAE;YACZ,CAAE;YAAA5C,QAAA,gBACAlS,OAAA;cAAKwN,KAAK,EAAE;gBACVkE,QAAQ,EAAE,UAAU;gBACpB+C,GAAG,EAAE,QAAQ;gBACbE,KAAK,EAAE,QAAQ;gBACflB,KAAK,EAAE,OAAO;gBACdL,MAAM,EAAE,OAAO;gBACflM,UAAU,EAAE,wEAAwE;gBACpFoL,YAAY,EAAE,KAAK;gBACnBiB,OAAO,EAAE,GAAG;gBACZwB,SAAS,EAAE;cACb;YAAE;cAAA/S,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAELnC,OAAA;cAAIwN,KAAK,EAAE;gBACTyH,MAAM,EAAE,YAAY;gBACpB3B,QAAQ,EAAE,MAAM;gBAChBD,UAAU,EAAE,GAAG;gBACflM,KAAK,EAAE,OAAO;gBACdkP,SAAS,EAAE,QAAQ;gBACnBhB,UAAU,EAAE;cACd,CAAE;cAAAnD,QAAA,EAAC;YAEH;cAAAlQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAELnC,OAAA;cAAKwN,KAAK,EAAE;gBACVuF,OAAO,EAAE,MAAM;gBACfyC,mBAAmB,EAAE,sCAAsC;gBAC3DhC,GAAG,EAAE;cACP,CAAE;cAAAtB,QAAA,EACC,CACC;gBACE9P,IAAI,EAAE,IAAI;gBACVE,KAAK,EAAE,aAAa;gBACpBgU,QAAQ,EAAE,qBAAqB;gBAC/BC,IAAI,EAAE,mCAAmC;gBACzCC,MAAM,EAAEA,CAAA,KAAMvS,YAAY,CAAC,SAAS,CAAC;gBACrCkD,KAAK,EAAE,SAAS;gBAChBwO,UAAU,EAAE;cACd,CAAC,EACD;gBACEvT,IAAI,EAAE,IAAI;gBACVE,KAAK,EAAE,YAAY;gBACnBgU,QAAQ,EAAE,iBAAiB;gBAC3BC,IAAI,EAAE,8BAA8B;gBACpCC,MAAM,EAAEA,CAAA,KAAMvS,YAAY,CAAC,KAAK,CAAC;gBACjCkD,KAAK,EAAE,SAAS;gBAChBwO,UAAU,EAAE;cACd,CAAC,EACD;gBACEvT,IAAI,EAAE,IAAI;gBACVE,KAAK,EAAE,eAAe;gBACtBgU,QAAQ,EAAE,oBAAoB;gBAC9BC,IAAI,EAAE,gCAAgC;gBACtCC,MAAM,EAAEA,CAAA,KAAMvS,YAAY,CAAC,QAAQ,CAAC;gBACpCkD,KAAK,EAAE,SAAS;gBAChBwO,UAAU,EAAE;cACd,CAAC,EACD;gBACEvT,IAAI,EAAE,GAAG;gBACTE,KAAK,EAAE,iBAAiB;gBACxBgU,QAAQ,EAAE,iBAAiB;gBAC3BC,IAAI,EAAE,gCAAgC;gBACtCC,MAAM,EAAEA,CAAA,KAAMvS,YAAY,CAAC,WAAW,CAAC;gBACvCkD,KAAK,EAAE,SAAS;gBAChBwO,UAAU,EAAE;cACd,CAAC,CACF,CAAC9T,GAAG,CAAC,CAAC2U,MAAM,EAAExC,KAAK,kBAClBhU,OAAA;gBACEuS,OAAO,EAAEiE,MAAM,CAACA,MAAO;gBACvBhJ,KAAK,EAAE;kBACLtG,UAAU,EAAE,wEAAwE;kBACpFyM,cAAc,EAAE,YAAY;kBAC5BrB,YAAY,EAAE,MAAM;kBACpBD,OAAO,EAAE,QAAQ;kBACjBD,MAAM,EAAE,SAAS;kBACjBpK,UAAU,EAAE,kDAAkD;kBAC9DR,MAAM,EAAE,aAAagP,MAAM,CAACrP,KAAK,IAAI;kBACrCuK,QAAQ,EAAE,UAAU;kBACpBoD,QAAQ,EAAE,QAAQ;kBAClBpM,SAAS,EAAE,0CAA0C8N,MAAM,CAACrP,KAAK;gBACnE,CAAE;gBACFsL,YAAY,EAAGtH,CAAC,IAAK;kBACnBA,CAAC,CAAC2K,aAAa,CAACtI,KAAK,CAAC3F,SAAS,GAAG,8BAA8B;kBAChEsD,CAAC,CAAC2K,aAAa,CAACtI,KAAK,CAAC9E,SAAS,GAAG,yCAAyC8N,MAAM,CAACrP,KAAK,IAAI;kBAC3FgE,CAAC,CAAC2K,aAAa,CAACtI,KAAK,CAAC1E,WAAW,GAAG0N,MAAM,CAACrP,KAAK,GAAG,IAAI;gBACzD,CAAE;gBACFuL,YAAY,EAAGvH,CAAC,IAAK;kBACnBA,CAAC,CAAC2K,aAAa,CAACtI,KAAK,CAAC3F,SAAS,GAAG,wBAAwB;kBAC1DsD,CAAC,CAAC2K,aAAa,CAACtI,KAAK,CAAC9E,SAAS,GAAG,0CAA0C8N,MAAM,CAACrP,KAAK,IAAI;kBAC5FgE,CAAC,CAAC2K,aAAa,CAACtI,KAAK,CAAC1E,WAAW,GAAG0N,MAAM,CAACrP,KAAK,GAAG,IAAI;gBACzD,CAAE;gBAAA+K,QAAA,gBAGFlS,OAAA;kBAAKwN,KAAK,EAAE;oBACVkE,QAAQ,EAAE,UAAU;oBACpB+C,GAAG,EAAE,KAAK;oBACVC,IAAI,EAAE,KAAK;oBACXjB,KAAK,EAAE,OAAO;oBACdL,MAAM,EAAE,OAAO;oBACflM,UAAU,EAAEsP,MAAM,CAACb,UAAU;oBAC7BrD,YAAY,EAAE,KAAK;oBACnBzK,SAAS,EAAE,uBAAuB;oBAClC0L,OAAO,EAAE,GAAG;oBACZ/G,MAAM,EAAE,YAAY;oBACpBuI,SAAS,EAAE;kBACb;gBAAE;kBAAA/S,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAELnC,OAAA;kBAAKwN,KAAK,EAAE;oBAAEkE,QAAQ,EAAE,UAAU;oBAAEmD,MAAM,EAAE,CAAC;oBAAEwB,SAAS,EAAE;kBAAS,CAAE;kBAAAnE,QAAA,gBACnElS,OAAA;oBAAKwN,KAAK,EAAE;sBACV8F,QAAQ,EAAE,MAAM;sBAChB4B,YAAY,EAAE,MAAM;sBACpB1I,MAAM,EAAE,oCAAoC;sBAC5CuI,SAAS,EAAE;oBACb,CAAE;oBAAA7C,QAAA,EACCsE,MAAM,CAACpU;kBAAI;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eAENnC,OAAA;oBAAKwN,KAAK,EAAE;sBACV8F,QAAQ,EAAE,QAAQ;sBAClBD,UAAU,EAAE,GAAG;sBACflM,KAAK,EAAE,OAAO;sBACd+N,YAAY,EAAE,QAAQ;sBACtBG,UAAU,EAAE,YAAYmB,MAAM,CAACrP,KAAK,EAAE;sBACtC4O,aAAa,EAAE;oBACjB,CAAE;oBAAA7D,QAAA,EACCsE,MAAM,CAAClU;kBAAK;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eAENnC,OAAA;oBAAKwN,KAAK,EAAE;sBACV8F,QAAQ,EAAE,QAAQ;sBAClBnM,KAAK,EAAEqP,MAAM,CAACrP,KAAK;sBACnBkM,UAAU,EAAE,GAAG;sBACf6B,YAAY,EAAE,SAAS;sBACvBe,aAAa,EAAE,WAAW;sBAC1BF,aAAa,EAAE;oBACjB,CAAE;oBAAA7D,QAAA,EACCsE,MAAM,CAACF;kBAAQ;oBAAAtU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC,eAENnC,OAAA;oBAAKwN,KAAK,EAAE;sBACV8F,QAAQ,EAAE,SAAS;sBACnBnM,KAAK,EAAE,uBAAuB;sBAC9B+O,SAAS,EAAE,QAAQ;sBACnBF,UAAU,EAAE;oBACd,CAAE;oBAAA9D,QAAA,EACCsE,MAAM,CAACD;kBAAI;oBAAAvU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eAGNnC,OAAA;oBAAKwN,KAAK,EAAE;sBACV+H,SAAS,EAAE,MAAM;sBACjBnC,MAAM,EAAE,KAAK;sBACblM,UAAU,EAAE,uBAAuB;sBACnCoL,YAAY,EAAE,KAAK;sBACnBwC,QAAQ,EAAE;oBACZ,CAAE;oBAAA5C,QAAA,eACAlS,OAAA;sBAAKwN,KAAK,EAAE;wBACV4F,MAAM,EAAE,MAAM;wBACdK,KAAK,EAAE,GAAG,EAAE,GAAGO,KAAK,GAAG,CAAC,GAAG;wBAC3B9M,UAAU,EAAEsP,MAAM,CAACb,UAAU;wBAC7BrD,YAAY,EAAE,KAAK;wBACnByC,SAAS,EAAE;sBACb;oBAAE;sBAAA/S,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GAjGE6R,KAAK;gBAAAhS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAkGV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNnC,OAAA;YAAKwN,KAAK,EAAE;cACVuF,OAAO,EAAE,MAAM;cACfyC,mBAAmB,EAAE,SAAS;cAC9BhC,GAAG,EAAE;YACP,CAAE;YAAAtB,QAAA,gBAGAlS,OAAA;cAAKwN,KAAK,EAAE;gBACVtG,UAAU,EAAEJ,YAAY,CAACG,YAAY,CAACU,OAAO;gBAC7C2K,YAAY,EAAE,MAAM;gBACpBD,OAAO,EAAE,QAAQ;gBACjB3J,SAAS,EAAE,cAAc5B,YAAY,CAACG,YAAY,CAAC0B,MAAM,EAAE;gBAC3DnB,MAAM,EAAE,aAAaV,YAAY,CAACG,YAAY,CAACO,MAAM;cACvD,CAAE;cAAA0K,QAAA,gBACAlS,OAAA;gBAAIwN,KAAK,EAAE;kBACTyH,MAAM,EAAE,cAAc;kBACtB3B,QAAQ,EAAE,QAAQ;kBAClBD,UAAU,EAAE,GAAG;kBACflM,KAAK,EAAEL,YAAY,CAACG,YAAY,CAACG;gBACnC,CAAE;gBAAA8K,QAAA,EAAC;cAEH;gBAAAlQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAELnC,OAAA;gBAAKwN,KAAK,EAAE;kBACViJ,SAAS,EAAE,OAAO;kBAClBC,SAAS,EAAE;gBACb,CAAE;gBAAAxE,QAAA,EACC3M,WAAW,CAACmH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC7K,GAAG,CAAC,CAACiI,GAAG,EAAEkK,KAAK,kBACtChU,OAAA;kBAAiBwN,KAAK,EAAE;oBACtBuF,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBQ,GAAG,EAAE,MAAM;oBACXnB,OAAO,EAAE,SAAS;oBAClBC,YAAY,EAAE,KAAK;oBACnB4C,YAAY,EAAE,QAAQ;oBACtBhO,UAAU,EAAE8M,KAAK,GAAG,CAAC,KAAK,CAAC,GAAGlN,YAAY,CAACG,YAAY,CAACiC,SAAS,GAAG,aAAa;oBACjFlB,UAAU,EAAE;kBACd,CAAE;kBAAAkK,QAAA,gBACAlS,OAAA;oBAAKwN,KAAK,EAAE;sBACViG,KAAK,EAAE,MAAM;sBACbL,MAAM,EAAE,MAAM;sBACdd,YAAY,EAAE,KAAK;sBACnBpL,UAAU,EAAE4C,GAAG,CAAC8F,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS;sBACxD+G,UAAU,EAAE;oBACd;kBAAE;oBAAA3U,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACLnC,OAAA;oBAAKwN,KAAK,EAAE;sBAAEsF,IAAI,EAAE;oBAAE,CAAE;oBAAAZ,QAAA,gBACtBlS,OAAA;sBAAKwN,KAAK,EAAE;wBACV8F,QAAQ,EAAE,QAAQ;wBAClBD,UAAU,EAAE,GAAG;wBACflM,KAAK,EAAEL,YAAY,CAACG,YAAY,CAACG,IAAI;wBACrC8N,YAAY,EAAE;sBAChB,CAAE;sBAAAhD,QAAA,EACCpI,GAAG,CAAC6F;oBAAG;sBAAA3N,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACNnC,OAAA;sBAAKwN,KAAK,EAAE;wBACV8F,QAAQ,EAAE,QAAQ;wBAClBnM,KAAK,EAAEL,YAAY,CAACG,YAAY,CAAC2P;sBACnC,CAAE;sBAAA1E,QAAA,EACC,IAAInC,IAAI,CAACjG,GAAG,CAACgG,IAAI,CAAC,CAAC+G,cAAc,CAAC;oBAAC;sBAAA7U,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAhCE6R,KAAK;kBAAAhS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAiCV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNnC,OAAA;cAAKwN,KAAK,EAAE;gBACVtG,UAAU,EAAEJ,YAAY,CAACG,YAAY,CAACU,OAAO;gBAC7C2K,YAAY,EAAE,MAAM;gBACpBD,OAAO,EAAE,QAAQ;gBACjB3J,SAAS,EAAE,cAAc5B,YAAY,CAACG,YAAY,CAAC0B,MAAM,EAAE;gBAC3DnB,MAAM,EAAE,aAAaV,YAAY,CAACG,YAAY,CAACO,MAAM;cACvD,CAAE;cAAA0K,QAAA,gBACAlS,OAAA;gBAAIwN,KAAK,EAAE;kBACTyH,MAAM,EAAE,cAAc;kBACtB3B,QAAQ,EAAE,QAAQ;kBAClBD,UAAU,EAAE,GAAG;kBACflM,KAAK,EAAEL,YAAY,CAACG,YAAY,CAACG;gBACnC,CAAE;gBAAA8K,QAAA,EAAC;cAEH;gBAAAlQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAELnC,OAAA;gBAAKwN,KAAK,EAAE;kBAAEuF,OAAO,EAAE,MAAM;kBAAE+D,aAAa,EAAE,QAAQ;kBAAEtD,GAAG,EAAE;gBAAO,CAAE;gBAAAtB,QAAA,gBACpElS,OAAA;kBAAOwN,KAAK,EAAE;oBACZuF,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBQ,GAAG,EAAE,SAAS;oBACdnB,OAAO,EAAE,MAAM;oBACfC,YAAY,EAAE,MAAM;oBACpBpL,UAAU,EAAEJ,YAAY,CAACG,YAAY,CAACiC,SAAS;oBAC/C1B,MAAM,EAAE,cAAcV,YAAY,CAACG,YAAY,CAACO,MAAM,EAAE;oBACxD4K,MAAM,EAAE1N,mBAAmB,GAAG,aAAa,GAAG,SAAS;oBACvDsD,UAAU,EAAE;kBACd,CAAE;kBACFyK,YAAY,EAAGtH,CAAC,IAAK;oBACnB,IAAI,CAACzG,mBAAmB,EAAE;sBACxByG,CAAC,CAAC2K,aAAa,CAACtI,KAAK,CAAC1E,WAAW,GAAGhC,YAAY,CAACG,YAAY,CAACgB,OAAO;sBACrEkD,CAAC,CAAC2K,aAAa,CAACtI,KAAK,CAACtG,UAAU,GAAGJ,YAAY,CAACG,YAAY,CAACgB,OAAO,GAAG,IAAI;oBAC7E;kBACF,CAAE;kBACFyK,YAAY,EAAGvH,CAAC,IAAK;oBACnBA,CAAC,CAAC2K,aAAa,CAACtI,KAAK,CAAC1E,WAAW,GAAGhC,YAAY,CAACG,YAAY,CAACO,MAAM;oBACpE2D,CAAC,CAAC2K,aAAa,CAACtI,KAAK,CAACtG,UAAU,GAAGJ,YAAY,CAACG,YAAY,CAACiC,SAAS;kBACxE,CAAE;kBAAAgJ,QAAA,gBACAlS,OAAA,CAACX,QAAQ;oBAACuT,IAAI,EAAE,EAAG;oBAACzL,KAAK,EAAEL,YAAY,CAACG,YAAY,CAACgB;kBAAQ;oBAAAjG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAChEnC,OAAA;oBAAAkS,QAAA,gBACElS,OAAA;sBAAKwN,KAAK,EAAE;wBACV6F,UAAU,EAAE,GAAG;wBACflM,KAAK,EAAEL,YAAY,CAACG,YAAY,CAACG,IAAI;wBACrC8N,YAAY,EAAE;sBAChB,CAAE;sBAAAhD,QAAA,EACCxN,mBAAmB,GAAG,cAAc,GAAG;oBAAe;sBAAA1C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD,CAAC,eACNnC,OAAA;sBAAKwN,KAAK,EAAE;wBACV8F,QAAQ,EAAE,QAAQ;wBAClBnM,KAAK,EAAEL,YAAY,CAACG,YAAY,CAAC2P;sBACnC,CAAE;sBAAA1E,QAAA,EAAC;oBAEH;sBAAAlQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNnC,OAAA;oBACE4P,IAAI,EAAC,MAAM;oBACXmH,MAAM,EAAC,iBAAiB;oBACxBC,QAAQ,EAAE9L,kBAAmB;oBAC7B+L,QAAQ,EAAEvS,mBAAoB;oBAC9B8I,KAAK,EAAE;sBAAEuF,OAAO,EAAE;oBAAO;kBAAE;oBAAA/Q,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC,EAEPyC,SAAS,iBACR5E,OAAA;kBACE6M,IAAI,EAAEjI,SAAU;kBAChByG,MAAM,EAAC,QAAQ;kBACf6L,GAAG,EAAC,qBAAqB;kBACzB1J,KAAK,EAAE;oBACLuF,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBQ,GAAG,EAAE,SAAS;oBACdnB,OAAO,EAAE,MAAM;oBACfC,YAAY,EAAE,MAAM;oBACpBpL,UAAU,EAAE,SAAS;oBACrBC,KAAK,EAAE,OAAO;oBACdgQ,cAAc,EAAE,MAAM;oBACtB9D,UAAU,EAAE,GAAG;oBACfrL,UAAU,EAAE;kBACd,CAAE;kBACFyK,YAAY,EAAGtH,CAAC,IAAK;oBACnBA,CAAC,CAAC2K,aAAa,CAACtI,KAAK,CAACtG,UAAU,GAAG,SAAS;oBAC5CiE,CAAC,CAAC2K,aAAa,CAACtI,KAAK,CAAC3F,SAAS,GAAG,kBAAkB;kBACtD,CAAE;kBACF6K,YAAY,EAAGvH,CAAC,IAAK;oBACnBA,CAAC,CAAC2K,aAAa,CAACtI,KAAK,CAACtG,UAAU,GAAG,SAAS;oBAC5CiE,CAAC,CAAC2K,aAAa,CAACtI,KAAK,CAAC3F,SAAS,GAAG,eAAe;kBACnD,CAAE;kBAAAqK,QAAA,gBAEFlS,OAAA,CAACnB,UAAU;oBAAC+T,IAAI,EAAE;kBAAG;oBAAA5Q,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACxBnC,OAAA;oBAAAkS,QAAA,EAAM;kBAAW;oBAAAlQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CACJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA6B,SAAS,KAAK,QAAQ,iBACrBhE,OAAA;QAAKwN,KAAK,EAAE;UAAE6E,OAAO,EAAE,MAAM;UAAE2C,QAAQ,EAAE,QAAQ;UAAEC,MAAM,EAAE;QAAS,CAAE;QAAA/C,QAAA,eACpElS,OAAA;UAAKwN,KAAK,EAAEjE,QAAQ,CAAC,MAAM,CAAE;UAAA2I,QAAA,gBAC3BlS,OAAA;YAAIwN,KAAK,EAAE;cACT+H,SAAS,EAAE,CAAC;cACZpO,KAAK,EAAE;YACT,CAAE;YAAA+K,QAAA,EAAC;UAAgB;YAAAlQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBnC,OAAA;YAAGwN,KAAK,EAAE;cACR+F,OAAO,EAAE,GAAG;cACZ2B,YAAY,EAAE,MAAM;cACpB/N,KAAK,EAAE;YACT,CAAE;YAAA+K,QAAA,EAAC;UAEH;YAAAlQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAGJnC,OAAA;YAAKwN,KAAK,EAAE;cACV4F,MAAM,EAAE,MAAM;cACdsD,SAAS,EAAE,MAAM;cACjBxB,YAAY,EAAE,MAAM;cACpB7C,OAAO,EAAE,MAAM;cACfrL,eAAe,EAAE,SAAS;cAC1BQ,MAAM,EAAE,mBAAmB;cAC3B8K,YAAY,EAAE;YAChB,CAAE;YAAAJ,QAAA,GAEC1O,QAAQ,CAAC6N,MAAM,KAAK,CAAC,gBACpBrR,OAAA;cAAKwN,KAAK,EAAE;gBACV4F,MAAM,EAAE,MAAM;gBACdL,OAAO,EAAE,MAAM;gBACf+D,aAAa,EAAE,QAAQ;gBACvB9D,UAAU,EAAE,QAAQ;gBACpBU,cAAc,EAAE,QAAQ;gBACxB2C,SAAS,EAAE,QAAQ;gBACnB9C,OAAO,EAAE;cACX,CAAE;cAAArB,QAAA,gBACAlS,OAAA;gBAAKwN,KAAK,EAAE;kBAAE8F,QAAQ,EAAE,MAAM;kBAAE4B,YAAY,EAAE;gBAAO,CAAE;gBAAAhD,QAAA,EAAC;cAAE;gBAAAlQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChEnC,OAAA;gBAAIwN,KAAK,EAAE;kBACTyH,MAAM,EAAE,CAAC;kBACT9N,KAAK,EAAE;gBACT,CAAE;gBAAA+K,QAAA,EAAC;cAAoB;gBAAAlQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5BnC,OAAA;gBAAGwN,KAAK,EAAE;kBACRrG,KAAK,EAAE;gBACT,CAAE;gBAAA+K,QAAA,EAAC;cAA+C;gBAAAlQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,GAENqB,QAAQ,CAAC3B,GAAG,CAAC,CAAC8N,GAAG,EAAEyH,GAAG,kBACpBpX,OAAA;cAEEwN,KAAK,EAAE;gBACL,IAAImC,GAAG,CAAClB,IAAI,KAAK,MAAM,GAAGlF,QAAQ,CAAC,gBAAgB,CAAC,GAAGA,QAAQ,CAAC,eAAe,CAAC,CAAC;gBACjFwL,SAAS,EAAE;cACb,CAAE;cAAA7C,QAAA,EAEDvC,GAAG,CAAClB,IAAI,KAAK,KAAK,gBACjBzO,OAAA,CAACqX,aAAa;gBAAAnF,QAAA,EAAEvC,GAAG,CAACjB;cAAO;gBAAA1M,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAgB,CAAC,GAE5CwN,GAAG,CAACjB;YACL,GAVI0I,GAAG;cAAApV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAWL,CACN,CACF,EACAyB,OAAO,iBACN5D,OAAA;cAAKwN,KAAK,EAAEjE,QAAQ,CAAC,eAAe,CAAE;cAAA2I,QAAA,eACpClS,OAAA;gBAAKwN,KAAK,EAAE;kBAAEuF,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEQ,GAAG,EAAE;gBAAM,CAAE;gBAAAtB,QAAA,gBAChElS,OAAA;kBAAKwN,KAAK,EAAE;oBACViG,KAAK,EAAE,MAAM;oBACbL,MAAM,EAAE,MAAM;oBACdd,YAAY,EAAE,KAAK;oBACnBtL,eAAe,EAAE,SAAS;oBAC1B+N,SAAS,EAAE;kBACb;gBAAE;kBAAA/S,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACLnC,OAAA;kBAAKwN,KAAK,EAAE;oBACViG,KAAK,EAAE,MAAM;oBACbL,MAAM,EAAE,MAAM;oBACdd,YAAY,EAAE,KAAK;oBACnBtL,eAAe,EAAE,SAAS;oBAC1B+N,SAAS,EAAE,iCAAiC;oBAC5CuC,cAAc,EAAE;kBAClB;gBAAE;kBAAAtV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACLnC,OAAA;kBAAKwN,KAAK,EAAE;oBACViG,KAAK,EAAE,MAAM;oBACbL,MAAM,EAAE,MAAM;oBACdd,YAAY,EAAE,KAAK;oBACnBtL,eAAe,EAAE,SAAS;oBAC1B+N,SAAS,EAAE,iCAAiC;oBAC5CuC,cAAc,EAAE;kBAClB;gBAAE;kBAAAtV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,eACDnC,OAAA;cAAKuX,GAAG,EAAE9R;YAAW;cAAAzD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eAGNnC,OAAA;YACEwN,KAAK,EAAE;cAAEuF,OAAO,EAAE,MAAM;cAAES,GAAG,EAAE;YAAO,CAAE;YACxCgE,QAAQ,EAAErM,CAAC,IAAI;cACbA,CAAC,CAACsM,cAAc,CAAC,CAAC;cAClBnJ,WAAW,CAAC,CAAC;YACf,CAAE;YAAA4D,QAAA,gBAEFlS,OAAA;cACE4P,IAAI,EAAC,MAAM;cACX8H,WAAW,EAAC,sBAAsB;cAClClK,KAAK,EAAEjE,QAAQ,CAAC,YAAY,CAAE;cAC9BkM,KAAK,EAAEnS,KAAM;cACb0T,QAAQ,EAAE7L,CAAC,IAAI5H,QAAQ,CAAC4H,CAAC,CAACE,MAAM,CAACoK,KAAK,CAAE;cACxCwB,QAAQ,EAAErT;YAAQ;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACFnC,OAAA;cACE4P,IAAI,EAAC,QAAQ;cACbpC,KAAK,EAAE;gBACL,GAAGjE,QAAQ,CAAC,eAAe,CAAC;gBAC5BoO,QAAQ,EAAE;cACZ,CAAE;cACFV,QAAQ,EAAErT,OAAO,IAAI,CAACN,KAAK,CAACiL,IAAI,CAAC,CAAE;cAAA2D,QAAA,EAElCtO,OAAO,GAAG,YAAY,GAAG;YAAM;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA6B,SAAS,KAAK,KAAK,iBAClBhE,OAAA;QAAKwN,KAAK,EAAE;UAAE6E,OAAO,EAAE;QAAO,CAAE;QAAAH,QAAA,eAC9BlS,OAAA;UAAKwN,KAAK,EAAEjE,QAAQ,CAAC,MAAM,CAAE;UAAA2I,QAAA,gBAE3BlS,OAAA;YAAKwN,KAAK,EAAE;cAAEuF,OAAO,EAAE,MAAM;cAAEW,cAAc,EAAE,eAAe;cAAEV,UAAU,EAAE,QAAQ;cAAEkC,YAAY,EAAE;YAAO,CAAE;YAAAhD,QAAA,gBAC3GlS,OAAA;cAAAkS,QAAA,gBACElS,OAAA;gBAAIwN,KAAK,EAAE;kBAAE+H,SAAS,EAAE,CAAC;kBAAEL,YAAY,EAAE;gBAAM,CAAE;gBAAAhD,QAAA,EAAC;cAA6B;gBAAAlQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpFnC,OAAA;gBAAGwN,KAAK,EAAE;kBAAE+F,OAAO,EAAE,GAAG;kBAAE0B,MAAM,EAAE;gBAAE,CAAE;gBAAA/C,QAAA,EAAC;cAEvC;gBAAAlQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,EACL+D,gBAAgB,iBACflG,OAAA;cACEuS,OAAO,EAAEpF,mBAAoB;cAC7BK,KAAK,EAAE;gBACL,GAAGjE,QAAQ,CAAC,eAAe,CAAC;gBAC5BrC,UAAU,EAAE,SAAS;gBACrB6L,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBQ,GAAG,EAAE,KAAK;gBACVF,QAAQ,EAAE,MAAM;gBAChBjB,OAAO,EAAE,UAAU;gBACnB7K,MAAM,EAAE,MAAM;gBACd8K,YAAY,EAAE,KAAK;gBACnBnL,KAAK,EAAE,OAAO;gBACdiL,MAAM,EAAE,SAAS;gBACjBpK,UAAU,EAAE;cACd,CAAE;cACFyK,YAAY,EAAGtH,CAAC,IAAKA,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACtG,UAAU,GAAG,SAAU;cAC3DwL,YAAY,EAAGvH,CAAC,IAAKA,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACtG,UAAU,GAAG,SAAU;cAAAgL,QAAA,gBAE3DlS,OAAA,CAACH,WAAW;gBAAC+S,IAAI,EAAE;cAAG;gBAAA5Q,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,uBAE3B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNnC,OAAA;YAAKwN,KAAK,EAAE;cACVuF,OAAO,EAAE,MAAM;cACfS,GAAG,EAAE,KAAK;cACV0B,YAAY,EAAE,MAAM;cACpB0C,QAAQ,EAAE,MAAM;cAChBrQ,YAAY,EAAE,gBAAgB;cAC9BsQ,aAAa,EAAE;YACjB,CAAE;YAAA3F,QAAA,EACC,CAAC,KAAK,EAAE,GAAG4F,MAAM,CAACC,IAAI,CAACvR,iBAAiB,CAAC,CAAC,CAAC3E,GAAG,CAACmW,QAAQ,iBACtDhY,OAAA;cAEEuS,OAAO,EAAEA,CAAA,KAAM5M,mBAAmB,CAACqS,QAAQ,CAAE;cAC7CxK,KAAK,EAAE;gBACL6E,OAAO,EAAE,UAAU;gBACnBC,YAAY,EAAE,MAAM;gBACpB9K,MAAM,EAAE9B,gBAAgB,KAAKsS,QAAQ,GACjC,MAAM,GACN,gBAAgB;gBACpB9Q,UAAU,EAAExB,gBAAgB,KAAKsS,QAAQ,GACrClR,YAAY,CAACG,YAAY,CAACgB,OAAO,GACjC,aAAa;gBACjBd,KAAK,EAAEzB,gBAAgB,KAAKsS,QAAQ,GAChC,OAAO,GACP,MAAM;gBACV5F,MAAM,EAAE,SAAS;gBACjBkB,QAAQ,EAAE,MAAM;gBAChBD,UAAU,EAAE3N,gBAAgB,KAAKsS,QAAQ,GAAG,GAAG,GAAG,GAAG;gBACrDhQ,UAAU,EAAE,eAAe;gBAC3BiO,aAAa,EAAE;cACjB,CAAE;cACFxD,YAAY,EAAGtH,CAAC,IAAK;gBACnB,IAAIzF,gBAAgB,KAAKsS,QAAQ,EAAE;kBACjC7M,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACtG,UAAU,GAAG,SAAS;gBACvC;cACF,CAAE;cACFwL,YAAY,EAAGvH,CAAC,IAAK;gBACnB,IAAIzF,gBAAgB,KAAKsS,QAAQ,EAAE;kBACjC7M,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACtG,UAAU,GAAG,aAAa;gBAC3C;cACF,CAAE;cAAAgL,QAAA,EAED8F,QAAQ,KAAK,KAAK,GAAG,QAAQ,GAAG,GAAGA,QAAQ,KAAK,OAAO,GAAG,IAAI,GAAGA,QAAQ,KAAK,UAAU,GAAG,IAAI,GAAGA,QAAQ,KAAK,UAAU,GAAG,IAAI,GAAGA,QAAQ,KAAK,SAAS,GAAG,IAAI,GAAGA,QAAQ,KAAK,QAAQ,GAAG,MAAM,GAAG,IAAI,IAAIA,QAAQ;YAAE,GA/BlNA,QAAQ;cAAAhW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgCP,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNnC,OAAA;YAAKwN,KAAK,EAAE;cACVuF,OAAO,EAAE,MAAM;cACfS,GAAG,EAAE,MAAM;cACX0B,YAAY,EAAE,MAAM;cACpB0C,QAAQ,EAAE,MAAM;cAChB5E,UAAU,EAAE;YACd,CAAE;YAAAd,QAAA,gBAEAlS,OAAA;cAAKwN,KAAK,EAAE;gBAAEkE,QAAQ,EAAE,UAAU;gBAAEoB,IAAI,EAAE,CAAC;gBAAE6E,QAAQ,EAAE;cAAQ,CAAE;cAAAzF,QAAA,gBAC/DlS,OAAA;gBAAKwN,KAAK,EAAE;kBACVkE,QAAQ,EAAE,UAAU;kBACpBgD,IAAI,EAAE,MAAM;kBACZD,GAAG,EAAE,KAAK;kBACV5M,SAAS,EAAE,kBAAkB;kBAC7BV,KAAK,EAAE;gBACT,CAAE;gBAAA+K,QAAA,eACAlS,OAAA,CAACZ,QAAQ;kBAACwT,IAAI,EAAE;gBAAG;kBAAA5Q,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eACNnC,OAAA;gBACE4P,IAAI,EAAC,MAAM;gBACX8H,WAAW,EAAC,qBAAqB;gBACjClK,KAAK,EAAE;kBACL,GAAGjE,QAAQ,CAAC,YAAY,CAAC;kBACzBgL,WAAW,EAAE,MAAM;kBACnBd,KAAK,EAAE;gBACT,CAAE;gBACFgC,KAAK,EAAEvR,UAAW;gBAClB8S,QAAQ,EAAG7L,CAAC,IAAKhH,aAAa,CAACgH,CAAC,CAACE,MAAM,CAACoK,KAAK;cAAE;gBAAAzT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,EACD+B,UAAU,iBACTlE,OAAA;gBACEwN,KAAK,EAAE;kBACLkE,QAAQ,EAAE,UAAU;kBACpBiD,KAAK,EAAE,MAAM;kBACbF,GAAG,EAAE,KAAK;kBACV5M,SAAS,EAAE,kBAAkB;kBAC7BX,UAAU,EAAE,MAAM;kBAClBM,MAAM,EAAE,MAAM;kBACdL,KAAK,EAAE,MAAM;kBACbiL,MAAM,EAAE;gBACV,CAAE;gBACFG,OAAO,EAAEA,CAAA,KAAMpO,aAAa,CAAC,EAAE,CAAE;gBAAA+N,QAAA,eAEjClS,OAAA,CAAC2S,GAAG;kBAACC,IAAI,EAAE;gBAAG;kBAAA5Q,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNnC,OAAA;cACEyV,KAAK,EAAE7P,MAAO;cACdoR,QAAQ,EAAG7L,CAAC,IAAKtF,SAAS,CAACsF,CAAC,CAACE,MAAM,CAACoK,KAAK,CAAE;cAC3CjI,KAAK,EAAE;gBACL,GAAGjE,QAAQ,CAAC,YAAY,CAAC;gBACzBkK,KAAK,EAAE,MAAM;gBACbkE,QAAQ,EAAE;cACZ,CAAE;cAAAzF,QAAA,gBAEFlS,OAAA;gBAAQyV,KAAK,EAAC,MAAM;gBAAAvD,QAAA,EAAC;cAAe;gBAAAlQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC7CnC,OAAA;gBAAQyV,KAAK,EAAC,WAAW;gBAAAvD,QAAA,EAAC;cAAiB;gBAAAlQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAGL6D,eAAe,CAACqL,MAAM,GAAG,CAAC,iBACzBrR,OAAA;YAAKwN,KAAK,EAAE;cACV0H,YAAY,EAAE,MAAM;cACpB7C,OAAO,EAAE,MAAM;cACfC,YAAY,EAAE,MAAM;cACpBpL,UAAU,EAAE,SAAS;cACrBM,MAAM,EAAE;YACV,CAAE;YAAA0K,QAAA,gBACAlS,OAAA;cAAIwN,KAAK,EAAE;gBACTuF,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBQ,GAAG,EAAE,KAAK;gBACVF,QAAQ,EAAE,MAAM;gBAChB4B,YAAY,EAAE,MAAM;gBACpB/N,KAAK,EAAE,MAAM;gBACb8N,MAAM,EAAE;cACV,CAAE;cAAA/C,QAAA,gBACAlS,OAAA,CAACJ,OAAO;gBAACuH,KAAK,EAAC;cAAM;gBAAAnF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oBAC1B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLnC,OAAA;cAAKwN,KAAK,EAAE;gBACVuF,OAAO,EAAE,MAAM;gBACfS,GAAG,EAAE,KAAK;gBACVoE,QAAQ,EAAE;cACZ,CAAE;cAAA1F,QAAA,EACClM,eAAe,CAACnE,GAAG,CAACwK,OAAO,iBAC1BrM,OAAA;gBAEEuS,OAAO,EAAEA,CAAA,KAAMnG,kBAAkB,CAACC,OAAO,CAAE;gBAC3CmB,KAAK,EAAE;kBACL6E,OAAO,EAAE,UAAU;kBACnBC,YAAY,EAAE,MAAM;kBACpB9K,MAAM,EAAE,aAAaV,YAAY,CAACG,YAAY,CAACgB,OAAO,EAAE;kBACxDf,UAAU,EAAE,aAAa;kBACzBC,KAAK,EAAEL,YAAY,CAACG,YAAY,CAACgB,OAAO;kBACxCmK,MAAM,EAAE,SAAS;kBACjBkB,QAAQ,EAAE,MAAM;kBAChBtL,UAAU,EAAE;gBACd,CAAE;gBACFyK,YAAY,EAAGtH,CAAC,IAAK;kBACnBA,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACtG,UAAU,GAAGJ,YAAY,CAACG,YAAY,CAACgB,OAAO;kBAC7DkD,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACrG,KAAK,GAAG,OAAO;gBAChC,CAAE;gBACFuL,YAAY,EAAGvH,CAAC,IAAK;kBACnBA,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACtG,UAAU,GAAG,aAAa;kBACzCiE,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACrG,KAAK,GAAGL,YAAY,CAACG,YAAY,CAACgB,OAAO;gBAC1D,CAAE;gBAAAiK,QAAA,EAED7F;cAAO,GArBHA,OAAO;gBAAArK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsBN,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDnC,OAAA;YAAKwN,KAAK,EAAE;cACVuF,OAAO,EAAE,MAAM;cACfyC,mBAAmB,EAAE,uCAAuC;cAC5DhC,GAAG,EAAE,MAAM;cACX+B,SAAS,EAAE;YACb,CAAE;YAAArD,QAAA,EACCzE,oBAAoB,CAAC,CAAC,CAAC5L,GAAG,CAAC,CAACwK,OAAO,EAAE2H,KAAK,kBACzChU,OAAA;cAEEwN,KAAK,EAAE;gBACL,GAAGjE,QAAQ,CAAC,aAAa,CAAC;gBAC1BmI,QAAQ,EAAE,UAAU;gBACpB7J,SAAS,EAAE/B,iBAAiB,CAACoH,QAAQ,CAACb,OAAO,CAAC,GAAG,aAAa,GAAG,UAAU;gBAC3E7E,MAAM,EAAE1B,iBAAiB,CAACoH,QAAQ,CAACb,OAAO,CAAC,GACvC,aAAavF,YAAY,CAACG,YAAY,CAACgB,OAAO,EAAE,GAChD,aAAanB,YAAY,CAACG,YAAY,CAACO,MAAM,EAAE;gBACnDN,UAAU,EAAEJ,YAAY,CAACG,YAAY,CAACU,OAAO;gBAC7CR,KAAK,EAAEL,YAAY,CAACG,YAAY,CAACG,IAAI;gBACrC2N,SAAS,EAAE,oBAAoBf,KAAK,GAAG,GAAG,QAAQ;gBAClDtL,SAAS,EAAE,aAAa5B,YAAY,CAACG,YAAY,CAAC0B,MAAM;cAC1D,CAAE;cACF4J,OAAO,EAAEA,CAAA,KAAMnG,kBAAkB,CAACC,OAAO,CAAE;cAAA6F,QAAA,gBAG3ClS,OAAA;gBACEuS,OAAO,EAAGpH,CAAC,IAAK6B,cAAc,CAACX,OAAO,EAAElB,CAAC,CAAE;gBAC3CqC,KAAK,EAAE;kBACLkE,QAAQ,EAAE,UAAU;kBACpB+C,GAAG,EAAE,KAAK;kBACVE,KAAK,EAAE,KAAK;kBACZzN,UAAU,EAAE,MAAM;kBAClBM,MAAM,EAAE,MAAM;kBACd4K,MAAM,EAAE,SAAS;kBACjBjL,KAAK,EAAErB,iBAAiB,CAACoH,QAAQ,CAACb,OAAO,CAAC,GAAG,SAAS,GAAG,MAAM;kBAC/DrE,UAAU,EAAE,eAAe;kBAC3BsL,QAAQ,EAAE;gBACZ,CAAE;gBAAApB,QAAA,eAEFlS,OAAA,CAACL,OAAO;kBAACsY,IAAI,EAAEnS,iBAAiB,CAACoH,QAAQ,CAACb,OAAO,CAAC,GAAG,cAAc,GAAG;gBAAO;kBAAArK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC,eAGTnC,OAAA;gBAAKwN,KAAK,EAAE;kBACViG,KAAK,EAAE,MAAM;kBACbL,MAAM,EAAE,MAAM;kBACdd,YAAY,EAAE,KAAK;kBACnBpL,UAAU,EAAE,2BAA2BJ,YAAY,CAACG,YAAY,CAACgB,OAAO,KAAKnB,YAAY,CAACG,YAAY,CAACiR,WAAW,GAAG;kBACrH/Q,KAAK,EAAE,OAAO;kBACd4L,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBU,cAAc,EAAE,QAAQ;kBACxBJ,QAAQ,EAAE,MAAM;kBAChBD,UAAU,EAAE,GAAG;kBACf6B,YAAY,EAAE,MAAM;kBACpBxM,SAAS,EAAE,aAAa5B,YAAY,CAACG,YAAY,CAAC0B,MAAM;gBAC1D,CAAE;gBAAAuJ,QAAA,EACC7F,OAAO,CAAC8L,MAAM,CAAC,CAAC;cAAC;gBAAAnW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eAGNnC,OAAA;gBAAKwN,KAAK,EAAE;kBACV6F,UAAU,EAAE,GAAG;kBACfgD,SAAS,EAAE,QAAQ;kBACnB/C,QAAQ,EAAE,MAAM;kBAChB4B,YAAY,EAAE;gBAChB,CAAE;gBAAAhD,QAAA,EACC7F;cAAO;gBAAArK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGNnC,OAAA;gBAAKwN,KAAK,EAAE;kBACVuF,OAAO,EAAE,MAAM;kBACfW,cAAc,EAAE,eAAe;kBAC/BJ,QAAQ,EAAE,MAAM;kBAChBC,OAAO,EAAE,GAAG;kBACZgC,SAAS,EAAE;gBACb,CAAE;gBAAArD,QAAA,gBACAlS,OAAA;kBAAAkS,QAAA,GAAM,eAAG,EAACiE,IAAI,CAACiC,KAAK,CAACjC,IAAI,CAACkC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,EAAC,YAAU;gBAAA;kBAAArW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/DnC,OAAA;kBAAAkS,QAAA,GAAM,SAAE,EAAC,CAACiE,IAAI,CAACkC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAtW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,EAGL2V,MAAM,CAACS,OAAO,CAAC/R,iBAAiB,CAAC,CAAC3E,GAAG,CAAC,CAAC,CAACmW,QAAQ,EAAEvR,SAAS,CAAC,KAAK;gBAChE,IAAIA,SAAS,CAACkH,IAAI,CAAClB,CAAC,IAAIA,CAAC,CAAClK,WAAW,CAAC,CAAC,KAAK8J,OAAO,CAAC9J,WAAW,CAAC,CAAC,CAAC,EAAE;kBAClE,oBACEvC,OAAA;oBAEEwN,KAAK,EAAE;sBACLkE,QAAQ,EAAE,UAAU;sBACpB+C,GAAG,EAAE,KAAK;sBACVC,IAAI,EAAE,KAAK;sBACXxN,UAAU,EAAEJ,YAAY,CAACG,YAAY,CAACgB,OAAO;sBAC7Cd,KAAK,EAAE,OAAO;sBACdkL,OAAO,EAAE,SAAS;sBAClBC,YAAY,EAAE,KAAK;sBACnBgB,QAAQ,EAAE,MAAM;sBAChBD,UAAU,EAAE;oBACd,CAAE;oBAAAnB,QAAA,EAED8F;kBAAQ,GAbJA,QAAQ;oBAAAhW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAcV,CAAC;gBAEV;gBACA,OAAO,IAAI;cACb,CAAC,CAAC;YAAA,GAhGG6R,KAAK;cAAAhS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiGP,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EAGLsL,oBAAoB,CAAC,CAAC,CAAC4D,MAAM,KAAK,CAAC,iBAClCrR,OAAA;YAAKwN,KAAK,EAAE;cACV6I,SAAS,EAAE,QAAQ;cACnBhE,OAAO,EAAE,MAAM;cACfkB,OAAO,EAAE,GAAG;cACZpM,KAAK,EAAE;YACT,CAAE;YAAA+K,QAAA,gBACAlS,OAAA;cAAKwN,KAAK,EAAE;gBAAE8F,QAAQ,EAAE,MAAM;gBAAE4B,YAAY,EAAE;cAAO,CAAE;cAAAhD,QAAA,EAAC;YAAE;cAAAlQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChEnC,OAAA;cAAIwN,KAAK,EAAE;gBAAErG,KAAK,EAAE;cAAO,CAAE;cAAA+K,QAAA,EAAC;YAAkB;cAAAlQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrDnC,OAAA;cAAGwN,KAAK,EAAE;gBAAErG,KAAK,EAAE;cAAO,CAAE;cAAA+K,QAAA,EAAC;YAA4C;cAAAlQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA6B,SAAS,KAAK,SAAS,iBACtBhE,OAAA;QAAKwN,KAAK,EAAE;UAAE6E,OAAO,EAAE;QAAO,CAAE;QAAAH,QAAA,eAC9BlS,OAAA;UAAKwN,KAAK,EAAEjE,QAAQ,CAAC,MAAM,CAAE;UAAA2I,QAAA,gBAC3BlS,OAAA;YAAIwN,KAAK,EAAE;cAAE+H,SAAS,EAAE;YAAE,CAAE;YAAArD,QAAA,EAAC;UAAc;YAAAlQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChDnC,OAAA;YAAGwN,KAAK,EAAE;cAAE+F,OAAO,EAAE,GAAG;cAAE2B,YAAY,EAAE;YAAO,CAAE;YAAAhD,QAAA,EAAC;UAElD;YAAAlQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJnC,OAAA;YAAKwN,KAAK,EAAE;cACVuF,OAAO,EAAE,MAAM;cACfyC,mBAAmB,EAAE,uCAAuC;cAC5DhC,GAAG,EAAE;YACP,CAAE;YAAAtB,QAAA,EACCxL,WAAW,CAAC7E,GAAG,CAAC,CAAC2W,IAAI,EAAExE,KAAK,kBAC3BhU,OAAA;cAEEwN,KAAK,EAAEjE,QAAQ,CAAC,UAAU,CAAE;cAC5BgJ,OAAO,EAAEA,CAAA,KAAMpE,YAAY,CAACqK,IAAI,CAAC5R,IAAI,CAAE;cAAAsL,QAAA,gBAEvClS,OAAA;gBAAAkS,QAAA,gBACElS,OAAA;kBAAIwN,KAAK,EAAE;oBAAEyH,MAAM,EAAE;kBAAY,CAAE;kBAAA/C,QAAA,EAAEsG,IAAI,CAAClW;gBAAK;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrDnC,OAAA;kBAAGwN,KAAK,EAAE;oBACRyH,MAAM,EAAE,CAAC;oBACT3B,QAAQ,EAAE,MAAM;oBAChBC,OAAO,EAAE;kBACX,CAAE;kBAAArB,QAAA,EACCsG,IAAI,CAAC7R;gBAAW;kBAAA3E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNnC,OAAA;gBAAKwN,KAAK,EAAE;kBAAErG,KAAK,EAAE;gBAAU,CAAE;gBAAA+K,QAAA,eAC/BlS,OAAA,CAACN,cAAc;kBAACkT,IAAI,EAAE;gBAAG;kBAAA5Q,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA,GAhBD6R,KAAK;cAAAhS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiBP,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA6B,SAAS,KAAK,QAAQ,iBACrBhE,OAAA;QAAKwN,KAAK,EAAE;UAAE6E,OAAO,EAAE;QAAO,CAAE;QAAAH,QAAA,eAC9BlS,OAAA,CAACyY,MAAM;UAAAzW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CACN,EACA6B,SAAS,KAAK,WAAW,iBACxBhE,OAAA;QAAKwN,KAAK,EAAE;UAAE6E,OAAO,EAAE;QAAO,CAAE;QAAAH,QAAA,eAC9BlS,OAAA;UAAKwN,KAAK,EAAEjE,QAAQ,CAAC,MAAM,CAAE;UAAA2I,QAAA,gBAC3BlS,OAAA;YAAIwN,KAAK,EAAE;cACT+H,SAAS,EAAE,CAAC;cACZpO,KAAK,EAAE;YACT,CAAE;YAAA+K,QAAA,EAAC;UAAS;YAAAlQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjBnC,OAAA;YAAGwN,KAAK,EAAE;cACR+F,OAAO,EAAE,GAAG;cACZ2B,YAAY,EAAE,MAAM;cACpB/N,KAAK,EAAE;YACT,CAAE;YAAA+K,QAAA,EAAC;UAEH;YAAAlQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJnC,OAAA;YAAKwN,KAAK,EAAE;cAAE0H,YAAY,EAAE;YAAO,CAAE;YAAAhD,QAAA,eACnClS,OAAA;cAAOwN,KAAK,EAAE;gBACZ,GAAGjE,QAAQ,CAAC,eAAe,CAAC;gBAC5BrC,UAAU,EAAE,SAAS;gBACrBC,KAAK,EAAE,MAAM;gBACbK,MAAM,EAAE,gBAAgB;gBACxB4K,MAAM,EAAEtN,qBAAqB,GAAG,aAAa,GAAG;cAClD,CAAE;cAAAoN,QAAA,gBACAlS,OAAA,CAACX,QAAQ;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACX2C,qBAAqB,GAAG,cAAc,GAAG,iBAAiB,eAC3D9E,OAAA;gBACE4P,IAAI,EAAC,MAAM;gBACXmH,MAAM,EAAC,sBAAsB;gBAC7BC,QAAQ,EAAE7K,oBAAqB;gBAC/B8K,QAAQ,EAAEnS,qBAAsB;gBAChC0I,KAAK,EAAE;kBAAEuF,OAAO,EAAE;gBAAO;cAAE;gBAAA/Q,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENnC,OAAA;YAAAkS,QAAA,gBACElS,OAAA;cAAIwN,KAAK,EAAE;gBACT0H,YAAY,EAAE,MAAM;gBACpB/N,KAAK,EAAE;cACT,CAAE;cAAA+K,QAAA,EAAC;YAAc;cAAAlQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACrB6C,aAAa,CAACqM,MAAM,KAAK,CAAC,gBACzBrR,OAAA;cAAGwN,KAAK,EAAE;gBACR+F,OAAO,EAAE,GAAG;gBACZpM,KAAK,EAAE;cACT,CAAE;cAAA+K,QAAA,EAAC;YAAyB;cAAAlQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,gBAEhCnC,OAAA;cAAKwN,KAAK,EAAE;gBACVxG,eAAe,EAAE,SAAS;gBAC1BQ,MAAM,EAAE,mBAAmB;gBAC3B8K,YAAY,EAAE,KAAK;gBACnBD,OAAO,EAAE;cACX,CAAE;cAAAH,QAAA,EACClN,aAAa,CAACnD,GAAG,CAAC,CAACuJ,IAAI,EAAEgM,GAAG,KAAK;gBAChC,MAAM;kBAAEhN,IAAI,EAAE0B;gBAAQ,CAAC,GAAGvF,QAAQ,CAACmF,OAAO,CAACC,IAAI,CAAC,WAAW,CAAC,CAACI,YAAY,CAAC,GAAGzH,IAAI,CAACkH,EAAE,IAAIJ,IAAI,CAACK,IAAI,EAAE,CAAC;gBACpG,oBACEzL,OAAA;kBAAewN,KAAK,EAAE;oBACpB6E,OAAO,EAAE,MAAM;oBACf9K,YAAY,EAAE,gBAAgB;oBAC9BwL,OAAO,EAAE,MAAM;oBACfW,cAAc,EAAE,eAAe;oBAC/BV,UAAU,EAAE;kBACd,CAAE;kBAAAd,QAAA,gBACAlS,OAAA;oBAAMwN,KAAK,EAAE;sBACXrG,KAAK,EAAE;oBACT,CAAE;oBAAA+K,QAAA,EAAE9G,IAAI,CAACK;kBAAI;oBAAAzJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACrBnC,OAAA;oBACE6M,IAAI,EAAEf,OAAO,CAACE,SAAU;oBACxBX,MAAM,EAAC,QAAQ;oBACf6L,GAAG,EAAC,qBAAqB;oBACzB1J,KAAK,EAAE;sBACLrG,KAAK,EAAE,SAAS;sBAChBgQ,cAAc,EAAE,MAAM;sBACtBpE,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBQ,GAAG,EAAE;oBACP,CAAE;oBAAAtB,QAAA,gBAEFlS,OAAA,CAACN,cAAc;sBAACkT,IAAI,EAAE;oBAAG;sBAAA5Q,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,QAE9B;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA,GAxBIiV,GAAG;kBAAApV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyBR,CAAC;cAEV,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EACA6B,SAAS,KAAK,WAAW,iBAAIhE,OAAA,CAAC0Y,KAAK;QAAA1W,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACtC6B,SAAS,KAAK,KAAK,iBAAIhE,OAAA,CAAC2Y,GAAG;QAAA3W,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAC9B6B,SAAS,KAAK,OAAO,IAAI,CAAAM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsP,KAAK,MAAK3O,WAAW,iBACnDjF,OAAA;QAAKwN,KAAK,EAAE;UAAE6E,OAAO,EAAE;QAAO,CAAE;QAAAH,QAAA,eAC9BlS,OAAA;UAAKwN,KAAK,EAAEjE,QAAQ,CAAC,MAAM,CAAE;UAAA2I,QAAA,gBAC3BlS,OAAA;YAAIwN,KAAK,EAAE;cACT+H,SAAS,EAAE,CAAC;cACZpO,KAAK,EAAE;YACT,CAAE;YAAA+K,QAAA,EAAC;UAAW;YAAAlQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnBnC,OAAA;YAAKwN,KAAK,EAAE;cACVuF,OAAO,EAAE,MAAM;cACfS,GAAG,EAAE,MAAM;cACX0B,YAAY,EAAE;YAChB,CAAE;YAAAhD,QAAA,gBACAlS,OAAA;cACEwN,KAAK,EAAE;gBACL,GAAGjE,QAAQ,CAAC,eAAe,CAAC;gBAC5BrC,UAAU,EAAE/B,QAAQ,KAAK,OAAO,GAC9B2B,YAAY,CAACG,YAAY,CAACgB,OAAO,GAAG,aAAa;gBACnDd,KAAK,EAAEhC,QAAQ,KAAK,OAAO,GACzB,OAAO,GAAG,MAAM;gBAClBqC,MAAM,EAAE;cACV,CAAE;cACF+K,OAAO,EAAEA,CAAA,KAAMnN,WAAW,CAAC,OAAO,CAAE;cAAA8M,QAAA,EACrC;YAED;cAAAlQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnC,OAAA;cACEwN,KAAK,EAAE;gBACL,GAAGjE,QAAQ,CAAC,eAAe,CAAC;gBAC5BrC,UAAU,EAAE/B,QAAQ,KAAK,WAAW,GAClC2B,YAAY,CAACG,YAAY,CAACgB,OAAO,GAAG,aAAa;gBACnDd,KAAK,EAAEhC,QAAQ,KAAK,WAAW,GAC7B,OAAO,GAAG,MAAM;gBAClBqC,MAAM,EAAE;cACV,CAAE;cACF+K,OAAO,EAAEA,CAAA,KAAMnN,WAAW,CAAC,WAAW,CAAE;cAAA8M,QAAA,EACzC;YAED;cAAAlQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAELgD,QAAQ,KAAK,OAAO,iBACnBnF,OAAA;YAAAkS,QAAA,gBACElS,OAAA;cAAIwN,KAAK,EAAE;gBACT0H,YAAY,EAAE,MAAM;gBACpB/N,KAAK,EAAE;cACT,CAAE;cAAA+K,QAAA,EAAC;YAAS;cAAAlQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjBnC,OAAA;cAAKwN,KAAK,EAAE;gBACVxG,eAAe,EAAE,SAAS;gBAC1BQ,MAAM,EAAE,mBAAmB;gBAC3B8K,YAAY,EAAE,KAAK;gBACnBD,OAAO,EAAE;cACX,CAAE;cAAAH,QAAA,EACChN,QAAQ,CAACrD,GAAG,CAAC,CAACyC,IAAI,EAAE8S,GAAG,kBACtBpX,OAAA;gBAAewN,KAAK,EAAE;kBACpB6E,OAAO,EAAE,MAAM;kBACf9K,YAAY,EAAE,gBAAgB;kBAC9BJ,KAAK,EAAE;gBACT,CAAE;gBAAA+K,QAAA,EACC5N,IAAI,CAACsP;cAAK,GALHwD,GAAG;gBAAApV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMR,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEAgD,QAAQ,KAAK,WAAW,iBACvBnF,OAAA;YAAAkS,QAAA,gBACElS,OAAA;cAAIwN,KAAK,EAAE;gBACT0H,YAAY,EAAE,MAAM;gBACpB/N,KAAK,EAAE;cACT,CAAE;cAAA+K,QAAA,EAAC;YAAa;cAAAlQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrBnC,OAAA;cAAGwN,KAAK,EAAE;gBACR+F,OAAO,EAAE,GAAG;gBACZpM,KAAK,EAAE;cACT,CAAE;cAAA+K,QAAA,EAAC;YAA+B;cAAAlQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,EAGNkD,YAAY,iBACXrF,OAAA;MAAKwN,KAAK,EAAE;QACV,GAAGjE,QAAQ,CAAC,cAAc,CAAC;QAC3BvC,eAAe,EAAE3B,YAAY,CAACuK,IAAI,KAAK,OAAO,GAAG,SAAS,GAC3CvK,YAAY,CAACuK,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS;QACtEzI,KAAK,EAAE,OAAO;QACdK,MAAM,EAAE;MACV,CAAE;MAAA0K,QAAA,EACC7M,YAAY,CAACsK;IAAG;MAAA3N,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CACN,eAGDnC,OAAA;MAAAkS,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAlQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACM,EAAA,CA9lEID,YAAY;EAAA,QAE4B7D,qBAAqB,EAClCH,aAAa,EACgCC,UAAU,EAC5DC,qBAAqB;AAAA;AAAAka,GAAA,GAL3CpW,YAAY;AA+lElB,eAAeA,YAAY;AAAC,IAAAnC,EAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAU,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAiX,GAAA;AAAAC,YAAA,CAAAxY,EAAA;AAAAwY,YAAA,CAAAvY,GAAA;AAAAuY,YAAA,CAAArY,GAAA;AAAAqY,YAAA,CAAApY,GAAA;AAAAoY,YAAA,CAAAlY,GAAA;AAAAkY,YAAA,CAAAjY,GAAA;AAAAiY,YAAA,CAAA/X,GAAA;AAAA+X,YAAA,CAAArX,GAAA;AAAAqX,YAAA,CAAAnX,GAAA;AAAAmX,YAAA,CAAAlX,GAAA;AAAAkX,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}