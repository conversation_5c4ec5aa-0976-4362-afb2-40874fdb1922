{"ast": null, "code": "// Optimized component exports\nexport { default as Navbar } from './Navbar';\nexport { default as Sidebar } from './Sidebar';\nexport { default as Dashboard } from './Dashboard';\nexport { default as Notification } from './Notification';\n\n// Lazy-loaded components for better performance\nimport { lazy } from 'react';\nexport const LazyQuizzes = /*#__PURE__*/lazy(_c = () => import('./Quizzes'));\n_c2 = LazyQuizzes;\nexport const LazyDSA = /*#__PURE__*/lazy(_c3 = () => import('./DSA'));\n_c4 = LazyDSA;\nexport const LazyResume = /*#__PURE__*/lazy(_c5 = () => import('./Resume'));\n_c6 = LazyResume;\nexport const LazyResources = /*#__PURE__*/lazy(_c7 = () => import('./Resources'));\n_c8 = LazyResources;\nexport const LazyChat = /*#__PURE__*/lazy(_c9 = () => import('./Chat'));\n_c0 = LazyChat;\nexport const LazyCompanies = /*#__PURE__*/lazy(_c1 = () => import('./Companies'));\n_c10 = LazyCompanies;\nexport const LazyProfile = /*#__PURE__*/lazy(_c11 = () => import('./Profile'));\n_c12 = LazyProfile;\nexport const LazySettings = /*#__PURE__*/lazy(_c13 = () => import('./Settings'));\n_c14 = LazySettings;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14;\n$RefreshReg$(_c, \"LazyQuizzes$lazy\");\n$RefreshReg$(_c2, \"LazyQuizzes\");\n$RefreshReg$(_c3, \"LazyDSA$lazy\");\n$RefreshReg$(_c4, \"LazyDSA\");\n$RefreshReg$(_c5, \"LazyResume$lazy\");\n$RefreshReg$(_c6, \"LazyResume\");\n$RefreshReg$(_c7, \"LazyResources$lazy\");\n$RefreshReg$(_c8, \"LazyResources\");\n$RefreshReg$(_c9, \"LazyChat$lazy\");\n$RefreshReg$(_c0, \"LazyChat\");\n$RefreshReg$(_c1, \"LazyCompanies$lazy\");\n$RefreshReg$(_c10, \"LazyCompanies\");\n$RefreshReg$(_c11, \"LazyProfile$lazy\");\n$RefreshReg$(_c12, \"LazyProfile\");\n$RefreshReg$(_c13, \"LazySettings$lazy\");\n$RefreshReg$(_c14, \"LazySettings\");", "map": {"version": 3, "names": ["default", "<PERSON><PERSON><PERSON>", "Sidebar", "Dashboard", "Notification", "lazy", "LazyQuizzes", "_c", "_c2", "LazyDSA", "_c3", "_c4", "LazyResume", "_c5", "_c6", "LazyResources", "_c7", "_c8", "LazyChat", "_c9", "_c0", "LazyCompanies", "_c1", "_c10", "LazyProfile", "_c11", "_c12", "LazySettings", "_c13", "_c14", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/src/components/index.js"], "sourcesContent": ["// Optimized component exports\nexport { default as Navbar } from './Navbar';\nexport { default as Sidebar } from './Sidebar';\nexport { default as Dashboard } from './Dashboard';\nexport { default as Notification } from './Notification';\n\n// Lazy-loaded components for better performance\nimport { lazy } from 'react';\n\nexport const LazyQuizzes = lazy(() => import('./Quizzes'));\nexport const LazyDSA = lazy(() => import('./DSA'));\nexport const LazyResume = lazy(() => import('./Resume'));\nexport const LazyResources = lazy(() => import('./Resources'));\nexport const LazyChat = lazy(() => import('./Chat'));\nexport const LazyCompanies = lazy(() => import('./Companies'));\nexport const LazyProfile = lazy(() => import('./Profile'));\nexport const LazySettings = lazy(() => import('./Settings'));\n"], "mappings": "AAAA;AACA,SAASA,OAAO,IAAIC,MAAM,QAAQ,UAAU;AAC5C,SAASD,OAAO,IAAIE,OAAO,QAAQ,WAAW;AAC9C,SAASF,OAAO,IAAIG,SAAS,QAAQ,aAAa;AAClD,SAASH,OAAO,IAAII,YAAY,QAAQ,gBAAgB;;AAExD;AACA,SAASC,IAAI,QAAQ,OAAO;AAE5B,OAAO,MAAMC,WAAW,gBAAGD,IAAI,CAAAE,EAAA,GAACA,CAAA,KAAM,MAAM,CAAC,WAAW,CAAC,CAAC;AAACC,GAAA,GAA9CF,WAAW;AACxB,OAAO,MAAMG,OAAO,gBAAGJ,IAAI,CAAAK,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,OAAO,CAAC,CAAC;AAACC,GAAA,GAAtCF,OAAO;AACpB,OAAO,MAAMG,UAAU,gBAAGP,IAAI,CAAAQ,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,UAAU,CAAC,CAAC;AAACC,GAAA,GAA5CF,UAAU;AACvB,OAAO,MAAMG,aAAa,gBAAGV,IAAI,CAAAW,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,aAAa,CAAC,CAAC;AAACC,GAAA,GAAlDF,aAAa;AAC1B,OAAO,MAAMG,QAAQ,gBAAGb,IAAI,CAAAc,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,QAAQ,CAAC,CAAC;AAACC,GAAA,GAAxCF,QAAQ;AACrB,OAAO,MAAMG,aAAa,gBAAGhB,IAAI,CAAAiB,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,aAAa,CAAC,CAAC;AAACC,IAAA,GAAlDF,aAAa;AAC1B,OAAO,MAAMG,WAAW,gBAAGnB,IAAI,CAAAoB,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,WAAW,CAAC,CAAC;AAACC,IAAA,GAA9CF,WAAW;AACxB,OAAO,MAAMG,YAAY,gBAAGtB,IAAI,CAAAuB,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,YAAY,CAAC,CAAC;AAACC,IAAA,GAAhDF,YAAY;AAAA,IAAApB,EAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA;AAAAC,YAAA,CAAAvB,EAAA;AAAAuB,YAAA,CAAAtB,GAAA;AAAAsB,YAAA,CAAApB,GAAA;AAAAoB,YAAA,CAAAnB,GAAA;AAAAmB,YAAA,CAAAjB,GAAA;AAAAiB,YAAA,CAAAhB,GAAA;AAAAgB,YAAA,CAAAd,GAAA;AAAAc,YAAA,CAAAb,GAAA;AAAAa,YAAA,CAAAX,GAAA;AAAAW,YAAA,CAAAV,GAAA;AAAAU,YAAA,CAAAR,GAAA;AAAAQ,YAAA,CAAAP,IAAA;AAAAO,YAAA,CAAAL,IAAA;AAAAK,YAAA,CAAAJ,IAAA;AAAAI,YAAA,CAAAF,IAAA;AAAAE,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}