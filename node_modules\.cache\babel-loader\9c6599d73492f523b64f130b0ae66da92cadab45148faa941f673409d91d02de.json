{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\quiz\\\\aich (4)\\\\aich (3)\\\\aich(6)\\\\aich(5)\\\\src\\\\components\\\\Notification.jsx\",\n  _s = $RefreshSig$();\nimport React, { memo, useEffect } from 'react';\nimport { FiCheckCircle, FiXCircle, FiInfo, FiX } from 'react-icons/fi';\nimport '../components.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Notification = /*#__PURE__*/_s(/*#__PURE__*/memo(_c = _s(({\n  notification,\n  onClose\n}) => {\n  _s();\n  useEffect(() => {\n    if (notification) {\n      const timer = setTimeout(() => {\n        onClose();\n      }, 3000);\n      return () => clearTimeout(timer);\n    }\n  }, [notification, onClose]);\n  if (!notification) return null;\n  const getIcon = () => {\n    switch (notification.type) {\n      case 'success':\n        return /*#__PURE__*/_jsxDEV(FiCheckCircle, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 16\n        }, this);\n      case 'error':\n        return /*#__PURE__*/_jsxDEV(FiXCircle, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FiInfo, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getClassName = () => {\n    switch (notification.type) {\n      case 'success':\n        return 'notification notification-success';\n      case 'error':\n        return 'notification notification-error';\n      default:\n        return 'notification notification-info';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: getClassName(),\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '12px'\n      },\n      children: [getIcon(), /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          flex: 1\n        },\n        children: notification.msg\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onClose,\n        style: {\n          background: 'none',\n          border: 'none',\n          color: 'white',\n          cursor: 'pointer',\n          padding: '4px',\n          borderRadius: '4px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          opacity: 0.8,\n          transition: 'opacity 0.2s ease'\n        },\n        onMouseEnter: e => e.target.style.opacity = '1',\n        onMouseLeave: e => e.target.style.opacity = '0.8',\n        children: /*#__PURE__*/_jsxDEV(FiX, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n}, \"OD7bBpZva5O2jO+Puf00hKivP7c=\")), \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c2 = Notification;\nNotification.displayName = 'Notification';\nexport default Notification;\nvar _c, _c2;\n$RefreshReg$(_c, \"Notification$memo\");\n$RefreshReg$(_c2, \"Notification\");", "map": {"version": 3, "names": ["React", "memo", "useEffect", "FiCheckCircle", "FiXCircle", "FiInfo", "FiX", "jsxDEV", "_jsxDEV", "Notification", "_s", "_c", "notification", "onClose", "timer", "setTimeout", "clearTimeout", "getIcon", "type", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getClassName", "className", "children", "style", "display", "alignItems", "gap", "flex", "msg", "onClick", "background", "border", "color", "cursor", "padding", "borderRadius", "justifyContent", "opacity", "transition", "onMouseEnter", "e", "target", "onMouseLeave", "_c2", "displayName", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/src/components/Notification.jsx"], "sourcesContent": ["import React, { memo, useEffect } from 'react';\nimport { FiCheckCircle, FiXCircle, FiInfo, FiX } from 'react-icons/fi';\nimport '../components.css';\n\nconst Notification = memo(({ notification, onClose }) => {\n  useEffect(() => {\n    if (notification) {\n      const timer = setTimeout(() => {\n        onClose();\n      }, 3000);\n\n      return () => clearTimeout(timer);\n    }\n  }, [notification, onClose]);\n\n  if (!notification) return null;\n\n  const getIcon = () => {\n    switch (notification.type) {\n      case 'success':\n        return <FiCheckCircle size={20} />;\n      case 'error':\n        return <FiXCircle size={20} />;\n      default:\n        return <FiInfo size={20} />;\n    }\n  };\n\n  const getClassName = () => {\n    switch (notification.type) {\n      case 'success':\n        return 'notification notification-success';\n      case 'error':\n        return 'notification notification-error';\n      default:\n        return 'notification notification-info';\n    }\n  };\n\n  return (\n    <div className={getClassName()}>\n      <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n        {getIcon()}\n        <span style={{ flex: 1 }}>{notification.msg}</span>\n        <button\n          onClick={onClose}\n          style={{\n            background: 'none',\n            border: 'none',\n            color: 'white',\n            cursor: 'pointer',\n            padding: '4px',\n            borderRadius: '4px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            opacity: 0.8,\n            transition: 'opacity 0.2s ease'\n          }}\n          onMouseEnter={(e) => e.target.style.opacity = '1'}\n          onMouseLeave={(e) => e.target.style.opacity = '0.8'}\n        >\n          <FiX size={16} />\n        </button>\n      </div>\n    </div>\n  );\n});\n\nNotification.displayName = 'Notification';\n\nexport default Notification;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,IAAI,EAAEC,SAAS,QAAQ,OAAO;AAC9C,SAASC,aAAa,EAAEC,SAAS,EAAEC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AACtE,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,YAAY,gBAAAC,EAAA,cAAGT,IAAI,CAAAU,EAAA,GAAAD,EAAA,CAAC,CAAC;EAAEE,YAAY;EAAEC;AAAQ,CAAC,KAAK;EAAAH,EAAA;EACvDR,SAAS,CAAC,MAAM;IACd,IAAIU,YAAY,EAAE;MAChB,MAAME,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BF,OAAO,CAAC,CAAC;MACX,CAAC,EAAE,IAAI,CAAC;MAER,OAAO,MAAMG,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAACF,YAAY,EAAEC,OAAO,CAAC,CAAC;EAE3B,IAAI,CAACD,YAAY,EAAE,OAAO,IAAI;EAE9B,MAAMK,OAAO,GAAGA,CAAA,KAAM;IACpB,QAAQL,YAAY,CAACM,IAAI;MACvB,KAAK,SAAS;QACZ,oBAAOV,OAAA,CAACL,aAAa;UAACgB,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpC,KAAK,OAAO;QACV,oBAAOf,OAAA,CAACJ,SAAS;UAACe,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAChC;QACE,oBAAOf,OAAA,CAACH,MAAM;UAACc,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC/B;EACF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,QAAQZ,YAAY,CAACM,IAAI;MACvB,KAAK,SAAS;QACZ,OAAO,mCAAmC;MAC5C,KAAK,OAAO;QACV,OAAO,iCAAiC;MAC1C;QACE,OAAO,gCAAgC;IAC3C;EACF,CAAC;EAED,oBACEV,OAAA;IAAKiB,SAAS,EAAED,YAAY,CAAC,CAAE;IAAAE,QAAA,eAC7BlB,OAAA;MAAKmB,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAO,CAAE;MAAAJ,QAAA,GAChET,OAAO,CAAC,CAAC,eACVT,OAAA;QAAMmB,KAAK,EAAE;UAAEI,IAAI,EAAE;QAAE,CAAE;QAAAL,QAAA,EAAEd,YAAY,CAACoB;MAAG;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACnDf,OAAA;QACEyB,OAAO,EAAEpB,OAAQ;QACjBc,KAAK,EAAE;UACLO,UAAU,EAAE,MAAM;UAClBC,MAAM,EAAE,MAAM;UACdC,KAAK,EAAE,OAAO;UACdC,MAAM,EAAE,SAAS;UACjBC,OAAO,EAAE,KAAK;UACdC,YAAY,EAAE,KAAK;UACnBX,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBW,cAAc,EAAE,QAAQ;UACxBC,OAAO,EAAE,GAAG;UACZC,UAAU,EAAE;QACd,CAAE;QACFC,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAAClB,KAAK,CAACc,OAAO,GAAG,GAAI;QAClDK,YAAY,EAAGF,CAAC,IAAKA,CAAC,CAACC,MAAM,CAAClB,KAAK,CAACc,OAAO,GAAG,KAAM;QAAAf,QAAA,eAEpDlB,OAAA,CAACF,GAAG;UAACa,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC,kCAAC;AAACwB,GAAA,GA/DGtC,YAAY;AAiElBA,YAAY,CAACuC,WAAW,GAAG,cAAc;AAEzC,eAAevC,YAAY;AAAC,IAAAE,EAAA,EAAAoC,GAAA;AAAAE,YAAA,CAAAtC,EAAA;AAAAsC,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}