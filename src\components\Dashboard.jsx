import React, { memo, useMemo } from 'react';
import { useOptimizedAnimation } from '../hooks/useResponsive';
import '../components.css';

const StatCard = memo(({ stat, index, shouldAnimate }) => (
  <div 
    className={`stat-card ${shouldAnimate ? 'hover-lift' : ''}`}
    style={{
      background: 'linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.1))',
      backdropFilter: 'blur(20px)',
      border: '1px solid rgba(255,255,255,0.2)',
      boxShadow: `0 15px 35px rgba(0,0,0,0.1), 0 0 0 1px ${stat.glowColor}20`
    }}
  >
    {/* Animated background pattern */}
    {shouldAnimate && (
      <div style={{
        position: 'absolute',
        top: '-50%',
        left: '-50%',
        width: '200%',
        height: '200%',
        background: `conic-gradient(from 0deg, transparent, ${stat.color}20, transparent)`,
        animation: 'rotate 20s linear infinite',
        opacity: 0.3
      }} />
    )}

    {/* Glow effect */}
    <div style={{
      position: 'absolute',
      top: '10px',
      right: '10px',
      width: '80px',
      height: '80px',
      background: stat.bgGradient,
      borderRadius: '50%',
      opacity: 0.2,
      filter: 'blur(20px)'
    }} />

    <div style={{ position: 'relative', zIndex: 1 }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: '24px'
      }}>
        <div style={{
          fontSize: '12px',
          fontWeight: 800,
          color: 'rgba(255,255,255,0.8)',
          letterSpacing: '2px'
        }}>
          {stat.title}
        </div>
        <div style={{
          fontSize: '32px',
          filter: 'drop-shadow(0 0 10px currentColor)'
        }}>
          {stat.icon}
        </div>
      </div>

      {/* Main Value */}
      <div style={{
        display: 'flex',
        alignItems: 'baseline',
        gap: '8px',
        marginBottom: '16px'
      }}>
        <div style={{
          fontSize: '48px',
          fontWeight: 900,
          color: 'white',
          textShadow: `0 0 20px ${stat.color}`,
          lineHeight: 1
        }}>
          {stat.value}
        </div>
        <div style={{
          fontSize: '14px',
          fontWeight: 600,
          color: stat.color,
          textTransform: 'uppercase',
          letterSpacing: '1px'
        }}>
          {stat.unit}
        </div>
      </div>

      {/* Description */}
      <div style={{
        fontSize: '14px',
        color: 'rgba(255,255,255,0.9)',
        fontWeight: 500,
        fontStyle: 'italic'
      }}>
        {stat.description}
      </div>

      {/* Progress bar */}
      <div style={{
        marginTop: '16px',
        height: '4px',
        background: 'rgba(255,255,255,0.2)',
        borderRadius: '2px',
        overflow: 'hidden'
      }}>
        <div style={{
          height: '100%',
          width: `${Math.min(100, (index + 1) * 25)}%`,
          background: stat.bgGradient,
          borderRadius: '2px',
          animation: shouldAnimate ? 'slideIn 2s ease-out' : 'none'
        }} />
      </div>
    </div>
  </div>
));

const ActionCard = memo(({ action, shouldAnimate }) => (
  <div
    className={`action-card ${shouldAnimate ? 'hover-lift' : ''}`}
    onClick={action.action}
    style={{
      background: 'linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.05))',
      backdropFilter: 'blur(15px)',
      border: `2px solid ${action.color}30`,
      boxShadow: `0 10px 30px rgba(0,0,0,0.1), 0 0 0 1px ${action.color}20`
    }}
  >
    {/* Animated glow */}
    {shouldAnimate && (
      <div style={{
        position: 'absolute',
        top: '50%',
        left: '50%',
        width: '100px',
        height: '100px',
        background: action.bgGradient,
        borderRadius: '50%',
        transform: 'translate(-50%, -50%)',
        opacity: 0.1,
        filter: 'blur(30px)',
        animation: 'pulse 3s ease-in-out infinite'
      }} />
    )}

    <div style={{ position: 'relative', zIndex: 1, textAlign: 'center' }}>
      <div style={{
        fontSize: '48px',
        marginBottom: '16px',
        filter: 'drop-shadow(0 0 10px currentColor)',
        animation: shouldAnimate ? 'bounce 2s ease-in-out infinite' : 'none'
      }}>
        {action.icon}
      </div>

      <div style={{
        fontSize: '18px',
        fontWeight: 800,
        color: 'white',
        marginBottom: '8px',
        textShadow: `0 0 10px ${action.color}`,
        letterSpacing: '1px'
      }}>
        {action.title}
      </div>

      <div style={{
        fontSize: '12px',
        color: action.color,
        fontWeight: 600,
        marginBottom: '12px',
        textTransform: 'uppercase',
        letterSpacing: '0.5px'
      }}>
        {action.subtitle}
      </div>

      <div style={{
        fontSize: '14px',
        color: 'rgba(255,255,255,0.8)',
        fontStyle: 'italic',
        lineHeight: 1.4
      }}>
        {action.desc}
      </div>

      {/* Power level indicator */}
      <div style={{
        marginTop: '16px',
        height: '3px',
        background: 'rgba(255,255,255,0.2)',
        borderRadius: '2px',
        overflow: 'hidden'
      }}>
        <div style={{
          height: '100%',
          width: '85%',
          background: action.bgGradient,
          borderRadius: '2px',
          animation: shouldAnimate ? 'slideIn 1.5s ease-out' : 'none'
        }} />
      </div>
    </div>
  </div>
));

const Dashboard = memo(({ user, onTabChange }) => {
  const { shouldAnimate } = useOptimizedAnimation();

  const statsData = useMemo(() => [
    {
      title: 'FIRE STREAK',
      value: '12',
      unit: 'DAYS',
      icon: '🔥',
      color: '#ff4757',
      bgGradient: 'linear-gradient(135deg, #ff4757, #ff6b7a)',
      glowColor: '#ff4757',
      description: 'Unstoppable momentum!'
    },
    {
      title: 'SKILL POINTS',
      value: '2,847',
      unit: 'XP',
      icon: '⚡',
      color: '#3742fa',
      bgGradient: 'linear-gradient(135deg, #3742fa, #5352ed)',
      glowColor: '#3742fa',
      description: 'Level up achieved!'
    },
    {
      title: 'POWER LEVEL',
      value: '47',
      unit: 'HOURS',
      icon: '💪',
      color: '#2ed573',
      bgGradient: 'linear-gradient(135deg, #2ed573, #7bed9f)',
      glowColor: '#2ed573',
      description: 'Training complete!'
    },
    {
      title: 'ACHIEVEMENTS',
      value: '15',
      unit: 'UNLOCKED',
      icon: '🏆',
      color: '#ffa502',
      bgGradient: 'linear-gradient(135deg, #ffa502, #ffb142)',
      glowColor: '#ffa502',
      description: 'Champion status!'
    }
  ], []);

  const actionsData = useMemo(() => [
    {
      icon: '🎯',
      title: 'BATTLE MODE',
      subtitle: 'Take Quiz Challenge',
      desc: 'Test your skills in epic battles!',
      action: () => onTabChange('quizzes'),
      color: '#ff4757',
      bgGradient: 'linear-gradient(135deg, #ff4757, #ff6b7a)'
    },
    {
      icon: '⚔️',
      title: 'CODE ARENA',
      subtitle: 'DSA Combat Zone',
      desc: 'Sharpen your coding weapons!',
      action: () => onTabChange('dsa'),
      color: '#3742fa',
      bgGradient: 'linear-gradient(135deg, #3742fa, #5352ed)'
    },
    {
      icon: '📜',
      title: 'SCROLL REVIEW',
      subtitle: 'Resume Enhancement',
      desc: 'Upgrade your legendary resume!',
      action: () => onTabChange('resume'),
      color: '#2ed573',
      bgGradient: 'linear-gradient(135deg, #2ed573, #7bed9f)'
    },
    {
      icon: '📚',
      title: 'KNOWLEDGE VAULT',
      subtitle: 'Study Materials',
      desc: 'Access ancient wisdom scrolls!',
      action: () => onTabChange('resources'),
      color: '#ffa502',
      bgGradient: 'linear-gradient(135deg, #ffa502, #ffb142)'
    }
  ], [onTabChange]);

  return (
    <div className="dashboard-container">
      {/* Animated Background Elements */}
      {shouldAnimate && (
        <>
          <div style={{
            position: 'absolute',
            top: '10%',
            left: '5%',
            width: '300px',
            height: '300px',
            background: 'linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))',
            borderRadius: '50%',
            animation: 'float 6s ease-in-out infinite',
            zIndex: 0
          }} />
          <div style={{
            position: 'absolute',
            top: '60%',
            right: '10%',
            width: '200px',
            height: '200px',
            background: 'linear-gradient(45deg, rgba(255,255,255,0.08), rgba(255,255,255,0.03))',
            borderRadius: '50%',
            animation: 'float 8s ease-in-out infinite reverse',
            zIndex: 0
          }} />
        </>
      )}

      <div style={{ maxWidth: '1400px', margin: '0 auto', position: 'relative', zIndex: 1 }}>
        {/* Hero Section */}
        <div className="dashboard-hero" style={{
          background: 'linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.1))',
          border: '1px solid rgba(255,255,255,0.2)',
          boxShadow: '0 25px 50px rgba(0,0,0,0.2)'
        }}>
          {/* Animated particles */}
          {shouldAnimate && (
            <>
              <div style={{
                position: 'absolute',
                top: '20px',
                right: '20px',
                fontSize: '32px',
                animation: 'bounce 2s infinite'
              }}>🚀</div>
              <div style={{
                position: 'absolute',
                bottom: '20px',
                left: '20px',
                fontSize: '24px',
                animation: 'bounce 3s infinite'
              }}>⭐</div>
            </>
          )}

          <div style={{ display: 'flex', alignItems: 'center', gap: '32px', position: 'relative', zIndex: 1 }}>
            <div style={{
              width: '120px',
              height: '120px',
              borderRadius: '50%',
              background: 'linear-gradient(135deg, #ff6b6b, #feca57)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '48px',
              fontWeight: 'bold',
              border: '4px solid rgba(255, 255, 255, 0.3)',
              boxShadow: '0 15px 35px rgba(0,0,0,0.2)',
              animation: shouldAnimate ? 'pulse 3s infinite' : 'none'
            }}>
              {user ? user.email[0].toUpperCase() : '👤'}
            </div>
            <div>
              <h1 style={{
                margin: 0,
                fontSize: '56px',
                fontWeight: 800,
                marginBottom: '16px',
                background: 'linear-gradient(45deg, #fff, #f0f0f0)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                textShadow: '0 2px 4px rgba(0,0,0,0.3)'
              }}>
                Hey {user ? user.email.split('@')[0] : 'Champion'}! 🎯
              </h1>
              <p style={{
                margin: 0,
                fontSize: '20px',
                opacity: 0.95,
                fontWeight: 500,
                color: 'white'
              }}>
                Time to level up your skills and dominate your goals! 💪✨
              </p>
              <div style={{
                marginTop: '16px',
                display: 'flex',
                gap: '16px'
              }}>
                <div style={{
                  background: 'rgba(255,255,255,0.2)',
                  padding: '8px 16px',
                  borderRadius: '20px',
                  fontSize: '14px',
                  fontWeight: 600,
                  color: 'white'
                }}>
                  🔥 12 Day Streak
                </div>
                <div style={{
                  background: 'rgba(255,255,255,0.2)',
                  padding: '8px 16px',
                  borderRadius: '20px',
                  fontSize: '14px',
                  fontWeight: 600,
                  color: 'white'
                }}>
                  🏆 Level 15
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Gaming-Style Stats Cards */}
        <div className="stats-grid">
          {statsData.map((stat, index) => (
            <StatCard 
              key={stat.title} 
              stat={stat} 
              index={index} 
              shouldAnimate={shouldAnimate}
            />
          ))}
        </div>

        {/* Epic Action Center */}
        <div style={{
          background: 'linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.1))',
          backdropFilter: 'blur(20px)',
          borderRadius: '25px',
          padding: '32px',
          marginBottom: '32px',
          border: '1px solid rgba(255,255,255,0.2)',
          boxShadow: '0 25px 50px rgba(0,0,0,0.2)',
          position: 'relative',
          overflow: 'hidden'
        }}>
          {shouldAnimate && (
            <div style={{
              position: 'absolute',
              top: '-100px',
              right: '-100px',
              width: '300px',
              height: '300px',
              background: 'conic-gradient(from 0deg, #ff4757, #3742fa, #2ed573, #ffa502, #ff4757)',
              borderRadius: '50%',
              opacity: 0.1,
              animation: 'rotate 30s linear infinite'
            }} />
          )}

          <h2 style={{
            margin: '0 0 32px 0',
            fontSize: '32px',
            fontWeight: 800,
            color: 'white',
            textAlign: 'center',
            textShadow: '0 2px 4px rgba(0,0,0,0.3)'
          }}>
            🎮 MISSION CONTROL CENTER 🎮
          </h2>

          <div className="action-grid">
            {actionsData.map((action, index) => (
              <ActionCard 
                key={action.title} 
                action={action} 
                shouldAnimate={shouldAnimate}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
});

Dashboard.displayName = 'Dashboard';

export default Dashboard;
