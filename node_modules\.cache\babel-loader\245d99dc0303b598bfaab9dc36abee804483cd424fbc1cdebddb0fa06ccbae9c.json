{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\quiz\\\\aich (4)\\\\aich (3)\\\\aich(6)\\\\aich(5)\\\\src\\\\components\\\\Dashboard.jsx\",\n  _s = $RefreshSig$();\nimport React, { memo, useMemo } from 'react';\nimport { useOptimizedAnimation } from '../hooks/useResponsive';\nimport '../components.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst StatCard = /*#__PURE__*/memo(_c = ({\n  stat,\n  index,\n  shouldAnimate\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: `stat-card ${shouldAnimate ? 'hover-lift' : ''}`,\n  style: {\n    background: 'linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.1))',\n    backdropFilter: 'blur(20px)',\n    border: '1px solid rgba(255,255,255,0.2)',\n    boxShadow: `0 15px 35px rgba(0,0,0,0.1), 0 0 0 1px ${stat.glowColor}20`\n  },\n  children: [shouldAnimate && /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'absolute',\n      top: '-50%',\n      left: '-50%',\n      width: '200%',\n      height: '200%',\n      background: `conic-gradient(from 0deg, transparent, ${stat.color}20, transparent)`,\n      animation: 'rotate 20s linear infinite',\n      opacity: 0.3\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 7\n  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'absolute',\n      top: '10px',\n      right: '10px',\n      width: '80px',\n      height: '80px',\n      background: stat.bgGradient,\n      borderRadius: '50%',\n      opacity: 0.2,\n      filter: 'blur(20px)'\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'relative',\n      zIndex: 1\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '12px',\n          fontWeight: 800,\n          color: 'rgba(255,255,255,0.8)',\n          letterSpacing: '2px'\n        },\n        children: stat.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '32px',\n          filter: 'drop-shadow(0 0 10px currentColor)'\n        },\n        children: stat.icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'baseline',\n        gap: '8px',\n        marginBottom: '16px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '48px',\n          fontWeight: 900,\n          color: 'white',\n          textShadow: `0 0 20px ${stat.color}`,\n          lineHeight: 1\n        },\n        children: stat.value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '14px',\n          fontWeight: 600,\n          color: stat.color,\n          textTransform: 'uppercase',\n          letterSpacing: '1px'\n        },\n        children: stat.unit\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: '14px',\n        color: 'rgba(255,255,255,0.9)',\n        fontWeight: 500,\n        fontStyle: 'italic'\n      },\n      children: stat.description\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '16px',\n        height: '4px',\n        background: 'rgba(255,255,255,0.2)',\n        borderRadius: '2px',\n        overflow: 'hidden'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          height: '100%',\n          width: `${Math.min(100, (index + 1) * 25)}%`,\n          background: stat.bgGradient,\n          borderRadius: '2px',\n          animation: shouldAnimate ? 'slideIn 2s ease-out' : 'none'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 6,\n  columnNumber: 3\n}, this));\n_c2 = StatCard;\nconst ActionCard = /*#__PURE__*/memo(_c3 = ({\n  action,\n  shouldAnimate\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: `action-card ${shouldAnimate ? 'hover-lift' : ''}`,\n  onClick: action.action,\n  style: {\n    background: 'linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.05))',\n    backdropFilter: 'blur(15px)',\n    border: `2px solid ${action.color}30`,\n    boxShadow: `0 10px 30px rgba(0,0,0,0.1), 0 0 0 1px ${action.color}20`\n  },\n  children: [shouldAnimate && /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'absolute',\n      top: '50%',\n      left: '50%',\n      width: '100px',\n      height: '100px',\n      background: action.bgGradient,\n      borderRadius: '50%',\n      transform: 'translate(-50%, -50%)',\n      opacity: 0.1,\n      filter: 'blur(30px)',\n      animation: 'pulse 3s ease-in-out infinite'\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 136,\n    columnNumber: 7\n  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'relative',\n      zIndex: 1,\n      textAlign: 'center'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: '48px',\n        marginBottom: '16px',\n        filter: 'drop-shadow(0 0 10px currentColor)',\n        animation: shouldAnimate ? 'bounce 2s ease-in-out infinite' : 'none'\n      },\n      children: action.icon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: '18px',\n        fontWeight: 800,\n        color: 'white',\n        marginBottom: '8px',\n        textShadow: `0 0 10px ${action.color}`,\n        letterSpacing: '1px'\n      },\n      children: action.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: '12px',\n        color: action.color,\n        fontWeight: 600,\n        marginBottom: '12px',\n        textTransform: 'uppercase',\n        letterSpacing: '0.5px'\n      },\n      children: action.subtitle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: '14px',\n        color: 'rgba(255,255,255,0.8)',\n        fontStyle: 'italic',\n        lineHeight: 1.4\n      },\n      children: action.desc\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '16px',\n        height: '3px',\n        background: 'rgba(255,255,255,0.2)',\n        borderRadius: '2px',\n        overflow: 'hidden'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          height: '100%',\n          width: '85%',\n          background: action.bgGradient,\n          borderRadius: '2px',\n          animation: shouldAnimate ? 'slideIn 1.5s ease-out' : 'none'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 151,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 124,\n  columnNumber: 3\n}, this));\n_c4 = ActionCard;\nconst Dashboard = /*#__PURE__*/_s(/*#__PURE__*/memo(_c5 = _s(({\n  user,\n  onTabChange\n}) => {\n  _s();\n  const {\n    shouldAnimate\n  } = useOptimizedAnimation();\n  const statsData = useMemo(() => [{\n    title: 'FIRE STREAK',\n    value: '12',\n    unit: 'DAYS',\n    icon: '🔥',\n    color: '#ff4757',\n    bgGradient: 'linear-gradient(135deg, #ff4757, #ff6b7a)',\n    glowColor: '#ff4757',\n    description: 'Unstoppable momentum!'\n  }, {\n    title: 'SKILL POINTS',\n    value: '2,847',\n    unit: 'XP',\n    icon: '⚡',\n    color: '#3742fa',\n    bgGradient: 'linear-gradient(135deg, #3742fa, #5352ed)',\n    glowColor: '#3742fa',\n    description: 'Level up achieved!'\n  }, {\n    title: 'POWER LEVEL',\n    value: '47',\n    unit: 'HOURS',\n    icon: '💪',\n    color: '#2ed573',\n    bgGradient: 'linear-gradient(135deg, #2ed573, #7bed9f)',\n    glowColor: '#2ed573',\n    description: 'Training complete!'\n  }, {\n    title: 'ACHIEVEMENTS',\n    value: '15',\n    unit: 'UNLOCKED',\n    icon: '🏆',\n    color: '#ffa502',\n    bgGradient: 'linear-gradient(135deg, #ffa502, #ffb142)',\n    glowColor: '#ffa502',\n    description: 'Champion status!'\n  }], []);\n  const actionsData = useMemo(() => [{\n    icon: '🎯',\n    title: 'BATTLE MODE',\n    subtitle: 'Take Quiz Challenge',\n    desc: 'Test your skills in epic battles!',\n    action: () => onTabChange('quizzes'),\n    color: '#ff4757',\n    bgGradient: 'linear-gradient(135deg, #ff4757, #ff6b7a)'\n  }, {\n    icon: '⚔️',\n    title: 'CODE ARENA',\n    subtitle: 'DSA Combat Zone',\n    desc: 'Sharpen your coding weapons!',\n    action: () => onTabChange('dsa'),\n    color: '#3742fa',\n    bgGradient: 'linear-gradient(135deg, #3742fa, #5352ed)'\n  }, {\n    icon: '📜',\n    title: 'SCROLL REVIEW',\n    subtitle: 'Resume Enhancement',\n    desc: 'Upgrade your legendary resume!',\n    action: () => onTabChange('resume'),\n    color: '#2ed573',\n    bgGradient: 'linear-gradient(135deg, #2ed573, #7bed9f)'\n  }, {\n    icon: '📚',\n    title: 'KNOWLEDGE VAULT',\n    subtitle: 'Study Materials',\n    desc: 'Access ancient wisdom scrolls!',\n    action: () => onTabChange('resources'),\n    color: '#ffa502',\n    bgGradient: 'linear-gradient(135deg, #ffa502, #ffb142)'\n  }], [onTabChange]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard-container\",\n    children: [shouldAnimate && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: '10%',\n          left: '5%',\n          width: '300px',\n          height: '300px',\n          background: 'linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))',\n          borderRadius: '50%',\n          animation: 'float 6s ease-in-out infinite',\n          zIndex: 0\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: '60%',\n          right: '10%',\n          width: '200px',\n          height: '200px',\n          background: 'linear-gradient(45deg, rgba(255,255,255,0.08), rgba(255,255,255,0.03))',\n          borderRadius: '50%',\n          animation: 'float 8s ease-in-out infinite reverse',\n          zIndex: 0\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        maxWidth: '1400px',\n        margin: '0 auto',\n        position: 'relative',\n        zIndex: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-hero\",\n        style: {\n          background: 'linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.1))',\n          border: '1px solid rgba(255,255,255,0.2)',\n          boxShadow: '0 25px 50px rgba(0,0,0,0.2)'\n        },\n        children: [shouldAnimate && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: '20px',\n              right: '20px',\n              fontSize: '32px',\n              animation: 'bounce 2s infinite'\n            },\n            children: \"\\uD83D\\uDE80\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              bottom: '20px',\n              left: '20px',\n              fontSize: '24px',\n              animation: 'bounce 3s infinite'\n            },\n            children: \"\\u2B50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '32px',\n            position: 'relative',\n            zIndex: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '120px',\n              height: '120px',\n              borderRadius: '50%',\n              background: 'linear-gradient(135deg, #ff6b6b, #feca57)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              fontSize: '48px',\n              fontWeight: 'bold',\n              border: '4px solid rgba(255, 255, 255, 0.3)',\n              boxShadow: '0 15px 35px rgba(0,0,0,0.2)',\n              animation: shouldAnimate ? 'pulse 3s infinite' : 'none'\n            },\n            children: user ? user.email[0].toUpperCase() : '👤'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              style: {\n                margin: 0,\n                fontSize: '56px',\n                fontWeight: 800,\n                marginBottom: '16px',\n                background: 'linear-gradient(45deg, #fff, #f0f0f0)',\n                WebkitBackgroundClip: 'text',\n                WebkitTextFillColor: 'transparent',\n                textShadow: '0 2px 4px rgba(0,0,0,0.3)'\n              },\n              children: [\"Hey \", user ? user.email.split('@')[0] : 'Champion', \"! \\uD83C\\uDFAF\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: 0,\n                fontSize: '20px',\n                opacity: 0.95,\n                fontWeight: 500,\n                color: 'white'\n              },\n              children: \"Time to level up your skills and dominate your goals! \\uD83D\\uDCAA\\u2728\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '16px',\n                display: 'flex',\n                gap: '16px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: 'rgba(255,255,255,0.2)',\n                  padding: '8px 16px',\n                  borderRadius: '20px',\n                  fontSize: '14px',\n                  fontWeight: 600,\n                  color: 'white'\n                },\n                children: \"\\uD83D\\uDD25 12 Day Streak\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: 'rgba(255,255,255,0.2)',\n                  padding: '8px 16px',\n                  borderRadius: '20px',\n                  fontSize: '14px',\n                  fontWeight: 600,\n                  color: 'white'\n                },\n                children: \"\\uD83C\\uDFC6 Level 15\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stats-grid\",\n        children: statsData.map((stat, index) => /*#__PURE__*/_jsxDEV(StatCard, {\n          stat: stat,\n          index: index,\n          shouldAnimate: shouldAnimate\n        }, stat.title, false, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 424,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.1))',\n          backdropFilter: 'blur(20px)',\n          borderRadius: '25px',\n          padding: '32px',\n          marginBottom: '32px',\n          border: '1px solid rgba(255,255,255,0.2)',\n          boxShadow: '0 25px 50px rgba(0,0,0,0.2)',\n          position: 'relative',\n          overflow: 'hidden'\n        },\n        children: [shouldAnimate && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: '-100px',\n            right: '-100px',\n            width: '300px',\n            height: '300px',\n            background: 'conic-gradient(from 0deg, #ff4757, #3742fa, #2ed573, #ffa502, #ff4757)',\n            borderRadius: '50%',\n            opacity: 0.1,\n            animation: 'rotate 30s linear infinite'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            margin: '0 0 32px 0',\n            fontSize: '32px',\n            fontWeight: 800,\n            color: 'white',\n            textAlign: 'center',\n            textShadow: '0 2px 4px rgba(0,0,0,0.3)'\n          },\n          children: \"\\uD83C\\uDFAE MISSION CONTROL CENTER \\uD83C\\uDFAE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"action-grid\",\n          children: actionsData.map((action, index) => /*#__PURE__*/_jsxDEV(ActionCard, {\n            action: action,\n            shouldAnimate: shouldAnimate\n          }, action.title, false, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 436,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 327,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 298,\n    columnNumber: 5\n  }, this);\n}, \"frwTCzHYjQ4/pTN/zrUcQmdEmbk=\", false, function () {\n  return [useOptimizedAnimation];\n})), \"frwTCzHYjQ4/pTN/zrUcQmdEmbk=\", false, function () {\n  return [useOptimizedAnimation];\n});\n_c6 = Dashboard;\nDashboard.displayName = 'Dashboard';\nexport default Dashboard;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"StatCard$memo\");\n$RefreshReg$(_c2, \"StatCard\");\n$RefreshReg$(_c3, \"ActionCard$memo\");\n$RefreshReg$(_c4, \"ActionCard\");\n$RefreshReg$(_c5, \"Dashboard$memo\");\n$RefreshReg$(_c6, \"Dashboard\");", "map": {"version": 3, "names": ["React", "memo", "useMemo", "useOptimizedAnimation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "StatCard", "_c", "stat", "index", "shouldAnimate", "className", "style", "background", "<PERSON><PERSON>ilter", "border", "boxShadow", "glowColor", "children", "position", "top", "left", "width", "height", "color", "animation", "opacity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "right", "bgGradient", "borderRadius", "filter", "zIndex", "display", "alignItems", "justifyContent", "marginBottom", "fontSize", "fontWeight", "letterSpacing", "title", "icon", "gap", "textShadow", "lineHeight", "value", "textTransform", "unit", "fontStyle", "description", "marginTop", "overflow", "Math", "min", "_c2", "ActionCard", "_c3", "action", "onClick", "transform", "textAlign", "subtitle", "desc", "_c4", "Dashboard", "_s", "_c5", "user", "onTabChange", "statsData", "actionsData", "max<PERSON><PERSON><PERSON>", "margin", "bottom", "email", "toUpperCase", "WebkitBackgroundClip", "WebkitTextFillColor", "split", "padding", "map", "_c6", "displayName", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(6)/aich(5)/src/components/Dashboard.jsx"], "sourcesContent": ["import React, { memo, useMemo } from 'react';\nimport { useOptimizedAnimation } from '../hooks/useResponsive';\nimport '../components.css';\n\nconst StatCard = memo(({ stat, index, shouldAnimate }) => (\n  <div \n    className={`stat-card ${shouldAnimate ? 'hover-lift' : ''}`}\n    style={{\n      background: 'linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.1))',\n      backdropFilter: 'blur(20px)',\n      border: '1px solid rgba(255,255,255,0.2)',\n      boxShadow: `0 15px 35px rgba(0,0,0,0.1), 0 0 0 1px ${stat.glowColor}20`\n    }}\n  >\n    {/* Animated background pattern */}\n    {shouldAnimate && (\n      <div style={{\n        position: 'absolute',\n        top: '-50%',\n        left: '-50%',\n        width: '200%',\n        height: '200%',\n        background: `conic-gradient(from 0deg, transparent, ${stat.color}20, transparent)`,\n        animation: 'rotate 20s linear infinite',\n        opacity: 0.3\n      }} />\n    )}\n\n    {/* Glow effect */}\n    <div style={{\n      position: 'absolute',\n      top: '10px',\n      right: '10px',\n      width: '80px',\n      height: '80px',\n      background: stat.bgGradient,\n      borderRadius: '50%',\n      opacity: 0.2,\n      filter: 'blur(20px)'\n    }} />\n\n    <div style={{ position: 'relative', zIndex: 1 }}>\n      {/* Header */}\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        marginBottom: '24px'\n      }}>\n        <div style={{\n          fontSize: '12px',\n          fontWeight: 800,\n          color: 'rgba(255,255,255,0.8)',\n          letterSpacing: '2px'\n        }}>\n          {stat.title}\n        </div>\n        <div style={{\n          fontSize: '32px',\n          filter: 'drop-shadow(0 0 10px currentColor)'\n        }}>\n          {stat.icon}\n        </div>\n      </div>\n\n      {/* Main Value */}\n      <div style={{\n        display: 'flex',\n        alignItems: 'baseline',\n        gap: '8px',\n        marginBottom: '16px'\n      }}>\n        <div style={{\n          fontSize: '48px',\n          fontWeight: 900,\n          color: 'white',\n          textShadow: `0 0 20px ${stat.color}`,\n          lineHeight: 1\n        }}>\n          {stat.value}\n        </div>\n        <div style={{\n          fontSize: '14px',\n          fontWeight: 600,\n          color: stat.color,\n          textTransform: 'uppercase',\n          letterSpacing: '1px'\n        }}>\n          {stat.unit}\n        </div>\n      </div>\n\n      {/* Description */}\n      <div style={{\n        fontSize: '14px',\n        color: 'rgba(255,255,255,0.9)',\n        fontWeight: 500,\n        fontStyle: 'italic'\n      }}>\n        {stat.description}\n      </div>\n\n      {/* Progress bar */}\n      <div style={{\n        marginTop: '16px',\n        height: '4px',\n        background: 'rgba(255,255,255,0.2)',\n        borderRadius: '2px',\n        overflow: 'hidden'\n      }}>\n        <div style={{\n          height: '100%',\n          width: `${Math.min(100, (index + 1) * 25)}%`,\n          background: stat.bgGradient,\n          borderRadius: '2px',\n          animation: shouldAnimate ? 'slideIn 2s ease-out' : 'none'\n        }} />\n      </div>\n    </div>\n  </div>\n));\n\nconst ActionCard = memo(({ action, shouldAnimate }) => (\n  <div\n    className={`action-card ${shouldAnimate ? 'hover-lift' : ''}`}\n    onClick={action.action}\n    style={{\n      background: 'linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.05))',\n      backdropFilter: 'blur(15px)',\n      border: `2px solid ${action.color}30`,\n      boxShadow: `0 10px 30px rgba(0,0,0,0.1), 0 0 0 1px ${action.color}20`\n    }}\n  >\n    {/* Animated glow */}\n    {shouldAnimate && (\n      <div style={{\n        position: 'absolute',\n        top: '50%',\n        left: '50%',\n        width: '100px',\n        height: '100px',\n        background: action.bgGradient,\n        borderRadius: '50%',\n        transform: 'translate(-50%, -50%)',\n        opacity: 0.1,\n        filter: 'blur(30px)',\n        animation: 'pulse 3s ease-in-out infinite'\n      }} />\n    )}\n\n    <div style={{ position: 'relative', zIndex: 1, textAlign: 'center' }}>\n      <div style={{\n        fontSize: '48px',\n        marginBottom: '16px',\n        filter: 'drop-shadow(0 0 10px currentColor)',\n        animation: shouldAnimate ? 'bounce 2s ease-in-out infinite' : 'none'\n      }}>\n        {action.icon}\n      </div>\n\n      <div style={{\n        fontSize: '18px',\n        fontWeight: 800,\n        color: 'white',\n        marginBottom: '8px',\n        textShadow: `0 0 10px ${action.color}`,\n        letterSpacing: '1px'\n      }}>\n        {action.title}\n      </div>\n\n      <div style={{\n        fontSize: '12px',\n        color: action.color,\n        fontWeight: 600,\n        marginBottom: '12px',\n        textTransform: 'uppercase',\n        letterSpacing: '0.5px'\n      }}>\n        {action.subtitle}\n      </div>\n\n      <div style={{\n        fontSize: '14px',\n        color: 'rgba(255,255,255,0.8)',\n        fontStyle: 'italic',\n        lineHeight: 1.4\n      }}>\n        {action.desc}\n      </div>\n\n      {/* Power level indicator */}\n      <div style={{\n        marginTop: '16px',\n        height: '3px',\n        background: 'rgba(255,255,255,0.2)',\n        borderRadius: '2px',\n        overflow: 'hidden'\n      }}>\n        <div style={{\n          height: '100%',\n          width: '85%',\n          background: action.bgGradient,\n          borderRadius: '2px',\n          animation: shouldAnimate ? 'slideIn 1.5s ease-out' : 'none'\n        }} />\n      </div>\n    </div>\n  </div>\n));\n\nconst Dashboard = memo(({ user, onTabChange }) => {\n  const { shouldAnimate } = useOptimizedAnimation();\n\n  const statsData = useMemo(() => [\n    {\n      title: 'FIRE STREAK',\n      value: '12',\n      unit: 'DAYS',\n      icon: '🔥',\n      color: '#ff4757',\n      bgGradient: 'linear-gradient(135deg, #ff4757, #ff6b7a)',\n      glowColor: '#ff4757',\n      description: 'Unstoppable momentum!'\n    },\n    {\n      title: 'SKILL POINTS',\n      value: '2,847',\n      unit: 'XP',\n      icon: '⚡',\n      color: '#3742fa',\n      bgGradient: 'linear-gradient(135deg, #3742fa, #5352ed)',\n      glowColor: '#3742fa',\n      description: 'Level up achieved!'\n    },\n    {\n      title: 'POWER LEVEL',\n      value: '47',\n      unit: 'HOURS',\n      icon: '💪',\n      color: '#2ed573',\n      bgGradient: 'linear-gradient(135deg, #2ed573, #7bed9f)',\n      glowColor: '#2ed573',\n      description: 'Training complete!'\n    },\n    {\n      title: 'ACHIEVEMENTS',\n      value: '15',\n      unit: 'UNLOCKED',\n      icon: '🏆',\n      color: '#ffa502',\n      bgGradient: 'linear-gradient(135deg, #ffa502, #ffb142)',\n      glowColor: '#ffa502',\n      description: 'Champion status!'\n    }\n  ], []);\n\n  const actionsData = useMemo(() => [\n    {\n      icon: '🎯',\n      title: 'BATTLE MODE',\n      subtitle: 'Take Quiz Challenge',\n      desc: 'Test your skills in epic battles!',\n      action: () => onTabChange('quizzes'),\n      color: '#ff4757',\n      bgGradient: 'linear-gradient(135deg, #ff4757, #ff6b7a)'\n    },\n    {\n      icon: '⚔️',\n      title: 'CODE ARENA',\n      subtitle: 'DSA Combat Zone',\n      desc: 'Sharpen your coding weapons!',\n      action: () => onTabChange('dsa'),\n      color: '#3742fa',\n      bgGradient: 'linear-gradient(135deg, #3742fa, #5352ed)'\n    },\n    {\n      icon: '📜',\n      title: 'SCROLL REVIEW',\n      subtitle: 'Resume Enhancement',\n      desc: 'Upgrade your legendary resume!',\n      action: () => onTabChange('resume'),\n      color: '#2ed573',\n      bgGradient: 'linear-gradient(135deg, #2ed573, #7bed9f)'\n    },\n    {\n      icon: '📚',\n      title: 'KNOWLEDGE VAULT',\n      subtitle: 'Study Materials',\n      desc: 'Access ancient wisdom scrolls!',\n      action: () => onTabChange('resources'),\n      color: '#ffa502',\n      bgGradient: 'linear-gradient(135deg, #ffa502, #ffb142)'\n    }\n  ], [onTabChange]);\n\n  return (\n    <div className=\"dashboard-container\">\n      {/* Animated Background Elements */}\n      {shouldAnimate && (\n        <>\n          <div style={{\n            position: 'absolute',\n            top: '10%',\n            left: '5%',\n            width: '300px',\n            height: '300px',\n            background: 'linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))',\n            borderRadius: '50%',\n            animation: 'float 6s ease-in-out infinite',\n            zIndex: 0\n          }} />\n          <div style={{\n            position: 'absolute',\n            top: '60%',\n            right: '10%',\n            width: '200px',\n            height: '200px',\n            background: 'linear-gradient(45deg, rgba(255,255,255,0.08), rgba(255,255,255,0.03))',\n            borderRadius: '50%',\n            animation: 'float 8s ease-in-out infinite reverse',\n            zIndex: 0\n          }} />\n        </>\n      )}\n\n      <div style={{ maxWidth: '1400px', margin: '0 auto', position: 'relative', zIndex: 1 }}>\n        {/* Hero Section */}\n        <div className=\"dashboard-hero\" style={{\n          background: 'linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.1))',\n          border: '1px solid rgba(255,255,255,0.2)',\n          boxShadow: '0 25px 50px rgba(0,0,0,0.2)'\n        }}>\n          {/* Animated particles */}\n          {shouldAnimate && (\n            <>\n              <div style={{\n                position: 'absolute',\n                top: '20px',\n                right: '20px',\n                fontSize: '32px',\n                animation: 'bounce 2s infinite'\n              }}>🚀</div>\n              <div style={{\n                position: 'absolute',\n                bottom: '20px',\n                left: '20px',\n                fontSize: '24px',\n                animation: 'bounce 3s infinite'\n              }}>⭐</div>\n            </>\n          )}\n\n          <div style={{ display: 'flex', alignItems: 'center', gap: '32px', position: 'relative', zIndex: 1 }}>\n            <div style={{\n              width: '120px',\n              height: '120px',\n              borderRadius: '50%',\n              background: 'linear-gradient(135deg, #ff6b6b, #feca57)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              fontSize: '48px',\n              fontWeight: 'bold',\n              border: '4px solid rgba(255, 255, 255, 0.3)',\n              boxShadow: '0 15px 35px rgba(0,0,0,0.2)',\n              animation: shouldAnimate ? 'pulse 3s infinite' : 'none'\n            }}>\n              {user ? user.email[0].toUpperCase() : '👤'}\n            </div>\n            <div>\n              <h1 style={{\n                margin: 0,\n                fontSize: '56px',\n                fontWeight: 800,\n                marginBottom: '16px',\n                background: 'linear-gradient(45deg, #fff, #f0f0f0)',\n                WebkitBackgroundClip: 'text',\n                WebkitTextFillColor: 'transparent',\n                textShadow: '0 2px 4px rgba(0,0,0,0.3)'\n              }}>\n                Hey {user ? user.email.split('@')[0] : 'Champion'}! 🎯\n              </h1>\n              <p style={{\n                margin: 0,\n                fontSize: '20px',\n                opacity: 0.95,\n                fontWeight: 500,\n                color: 'white'\n              }}>\n                Time to level up your skills and dominate your goals! 💪✨\n              </p>\n              <div style={{\n                marginTop: '16px',\n                display: 'flex',\n                gap: '16px'\n              }}>\n                <div style={{\n                  background: 'rgba(255,255,255,0.2)',\n                  padding: '8px 16px',\n                  borderRadius: '20px',\n                  fontSize: '14px',\n                  fontWeight: 600,\n                  color: 'white'\n                }}>\n                  🔥 12 Day Streak\n                </div>\n                <div style={{\n                  background: 'rgba(255,255,255,0.2)',\n                  padding: '8px 16px',\n                  borderRadius: '20px',\n                  fontSize: '14px',\n                  fontWeight: 600,\n                  color: 'white'\n                }}>\n                  🏆 Level 15\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Gaming-Style Stats Cards */}\n        <div className=\"stats-grid\">\n          {statsData.map((stat, index) => (\n            <StatCard \n              key={stat.title} \n              stat={stat} \n              index={index} \n              shouldAnimate={shouldAnimate}\n            />\n          ))}\n        </div>\n\n        {/* Epic Action Center */}\n        <div style={{\n          background: 'linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.1))',\n          backdropFilter: 'blur(20px)',\n          borderRadius: '25px',\n          padding: '32px',\n          marginBottom: '32px',\n          border: '1px solid rgba(255,255,255,0.2)',\n          boxShadow: '0 25px 50px rgba(0,0,0,0.2)',\n          position: 'relative',\n          overflow: 'hidden'\n        }}>\n          {shouldAnimate && (\n            <div style={{\n              position: 'absolute',\n              top: '-100px',\n              right: '-100px',\n              width: '300px',\n              height: '300px',\n              background: 'conic-gradient(from 0deg, #ff4757, #3742fa, #2ed573, #ffa502, #ff4757)',\n              borderRadius: '50%',\n              opacity: 0.1,\n              animation: 'rotate 30s linear infinite'\n            }} />\n          )}\n\n          <h2 style={{\n            margin: '0 0 32px 0',\n            fontSize: '32px',\n            fontWeight: 800,\n            color: 'white',\n            textAlign: 'center',\n            textShadow: '0 2px 4px rgba(0,0,0,0.3)'\n          }}>\n            🎮 MISSION CONTROL CENTER 🎮\n          </h2>\n\n          <div className=\"action-grid\">\n            {actionsData.map((action, index) => (\n              <ActionCard \n                key={action.title} \n                action={action} \n                shouldAnimate={shouldAnimate}\n              />\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n});\n\nDashboard.displayName = 'Dashboard';\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,IAAI,EAAEC,OAAO,QAAQ,OAAO;AAC5C,SAASC,qBAAqB,QAAQ,wBAAwB;AAC9D,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3B,MAAMC,QAAQ,gBAAGP,IAAI,CAAAQ,EAAA,GAACA,CAAC;EAAEC,IAAI;EAAEC,KAAK;EAAEC;AAAc,CAAC,kBACnDP,OAAA;EACEQ,SAAS,EAAE,aAAaD,aAAa,GAAG,YAAY,GAAG,EAAE,EAAG;EAC5DE,KAAK,EAAE;IACLC,UAAU,EAAE,wEAAwE;IACpFC,cAAc,EAAE,YAAY;IAC5BC,MAAM,EAAE,iCAAiC;IACzCC,SAAS,EAAE,0CAA0CR,IAAI,CAACS,SAAS;EACrE,CAAE;EAAAC,QAAA,GAGDR,aAAa,iBACZP,OAAA;IAAKS,KAAK,EAAE;MACVO,QAAQ,EAAE,UAAU;MACpBC,GAAG,EAAE,MAAM;MACXC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdV,UAAU,EAAE,0CAA0CL,IAAI,CAACgB,KAAK,kBAAkB;MAClFC,SAAS,EAAE,4BAA4B;MACvCC,OAAO,EAAE;IACX;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CACL,eAGD3B,OAAA;IAAKS,KAAK,EAAE;MACVO,QAAQ,EAAE,UAAU;MACpBC,GAAG,EAAE,MAAM;MACXW,KAAK,EAAE,MAAM;MACbT,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdV,UAAU,EAAEL,IAAI,CAACwB,UAAU;MAC3BC,YAAY,EAAE,KAAK;MACnBP,OAAO,EAAE,GAAG;MACZQ,MAAM,EAAE;IACV;EAAE;IAAAP,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAEL3B,OAAA;IAAKS,KAAK,EAAE;MAAEO,QAAQ,EAAE,UAAU;MAAEgB,MAAM,EAAE;IAAE,CAAE;IAAAjB,QAAA,gBAE9Cf,OAAA;MAAKS,KAAK,EAAE;QACVwB,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,eAAe;QAC/BC,YAAY,EAAE;MAChB,CAAE;MAAArB,QAAA,gBACAf,OAAA;QAAKS,KAAK,EAAE;UACV4B,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,GAAG;UACfjB,KAAK,EAAE,uBAAuB;UAC9BkB,aAAa,EAAE;QACjB,CAAE;QAAAxB,QAAA,EACCV,IAAI,CAACmC;MAAK;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eACN3B,OAAA;QAAKS,KAAK,EAAE;UACV4B,QAAQ,EAAE,MAAM;UAChBN,MAAM,EAAE;QACV,CAAE;QAAAhB,QAAA,EACCV,IAAI,CAACoC;MAAI;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3B,OAAA;MAAKS,KAAK,EAAE;QACVwB,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,UAAU;QACtBQ,GAAG,EAAE,KAAK;QACVN,YAAY,EAAE;MAChB,CAAE;MAAArB,QAAA,gBACAf,OAAA;QAAKS,KAAK,EAAE;UACV4B,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,GAAG;UACfjB,KAAK,EAAE,OAAO;UACdsB,UAAU,EAAE,YAAYtC,IAAI,CAACgB,KAAK,EAAE;UACpCuB,UAAU,EAAE;QACd,CAAE;QAAA7B,QAAA,EACCV,IAAI,CAACwC;MAAK;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eACN3B,OAAA;QAAKS,KAAK,EAAE;UACV4B,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,GAAG;UACfjB,KAAK,EAAEhB,IAAI,CAACgB,KAAK;UACjByB,aAAa,EAAE,WAAW;UAC1BP,aAAa,EAAE;QACjB,CAAE;QAAAxB,QAAA,EACCV,IAAI,CAAC0C;MAAI;QAAAvB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3B,OAAA;MAAKS,KAAK,EAAE;QACV4B,QAAQ,EAAE,MAAM;QAChBhB,KAAK,EAAE,uBAAuB;QAC9BiB,UAAU,EAAE,GAAG;QACfU,SAAS,EAAE;MACb,CAAE;MAAAjC,QAAA,EACCV,IAAI,CAAC4C;IAAW;MAAAzB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC,eAGN3B,OAAA;MAAKS,KAAK,EAAE;QACVyC,SAAS,EAAE,MAAM;QACjB9B,MAAM,EAAE,KAAK;QACbV,UAAU,EAAE,uBAAuB;QACnCoB,YAAY,EAAE,KAAK;QACnBqB,QAAQ,EAAE;MACZ,CAAE;MAAApC,QAAA,eACAf,OAAA;QAAKS,KAAK,EAAE;UACVW,MAAM,EAAE,MAAM;UACdD,KAAK,EAAE,GAAGiC,IAAI,CAACC,GAAG,CAAC,GAAG,EAAE,CAAC/C,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG;UAC5CI,UAAU,EAAEL,IAAI,CAACwB,UAAU;UAC3BC,YAAY,EAAE,KAAK;UACnBR,SAAS,EAAEf,aAAa,GAAG,qBAAqB,GAAG;QACrD;MAAE;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN,CAAC;AAAC2B,GAAA,GApHGnD,QAAQ;AAsHd,MAAMoD,UAAU,gBAAG3D,IAAI,CAAA4D,GAAA,GAACA,CAAC;EAAEC,MAAM;EAAElD;AAAc,CAAC,kBAChDP,OAAA;EACEQ,SAAS,EAAE,eAAeD,aAAa,GAAG,YAAY,GAAG,EAAE,EAAG;EAC9DmD,OAAO,EAAED,MAAM,CAACA,MAAO;EACvBhD,KAAK,EAAE;IACLC,UAAU,EAAE,wEAAwE;IACpFC,cAAc,EAAE,YAAY;IAC5BC,MAAM,EAAE,aAAa6C,MAAM,CAACpC,KAAK,IAAI;IACrCR,SAAS,EAAE,0CAA0C4C,MAAM,CAACpC,KAAK;EACnE,CAAE;EAAAN,QAAA,GAGDR,aAAa,iBACZP,OAAA;IAAKS,KAAK,EAAE;MACVO,QAAQ,EAAE,UAAU;MACpBC,GAAG,EAAE,KAAK;MACVC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,OAAO;MACdC,MAAM,EAAE,OAAO;MACfV,UAAU,EAAE+C,MAAM,CAAC5B,UAAU;MAC7BC,YAAY,EAAE,KAAK;MACnB6B,SAAS,EAAE,uBAAuB;MAClCpC,OAAO,EAAE,GAAG;MACZQ,MAAM,EAAE,YAAY;MACpBT,SAAS,EAAE;IACb;EAAE;IAAAE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CACL,eAED3B,OAAA;IAAKS,KAAK,EAAE;MAAEO,QAAQ,EAAE,UAAU;MAAEgB,MAAM,EAAE,CAAC;MAAE4B,SAAS,EAAE;IAAS,CAAE;IAAA7C,QAAA,gBACnEf,OAAA;MAAKS,KAAK,EAAE;QACV4B,QAAQ,EAAE,MAAM;QAChBD,YAAY,EAAE,MAAM;QACpBL,MAAM,EAAE,oCAAoC;QAC5CT,SAAS,EAAEf,aAAa,GAAG,gCAAgC,GAAG;MAChE,CAAE;MAAAQ,QAAA,EACC0C,MAAM,CAAChB;IAAI;MAAAjB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eAEN3B,OAAA;MAAKS,KAAK,EAAE;QACV4B,QAAQ,EAAE,MAAM;QAChBC,UAAU,EAAE,GAAG;QACfjB,KAAK,EAAE,OAAO;QACde,YAAY,EAAE,KAAK;QACnBO,UAAU,EAAE,YAAYc,MAAM,CAACpC,KAAK,EAAE;QACtCkB,aAAa,EAAE;MACjB,CAAE;MAAAxB,QAAA,EACC0C,MAAM,CAACjB;IAAK;MAAAhB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAEN3B,OAAA;MAAKS,KAAK,EAAE;QACV4B,QAAQ,EAAE,MAAM;QAChBhB,KAAK,EAAEoC,MAAM,CAACpC,KAAK;QACnBiB,UAAU,EAAE,GAAG;QACfF,YAAY,EAAE,MAAM;QACpBU,aAAa,EAAE,WAAW;QAC1BP,aAAa,EAAE;MACjB,CAAE;MAAAxB,QAAA,EACC0C,MAAM,CAACI;IAAQ;MAAArC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC,eAEN3B,OAAA;MAAKS,KAAK,EAAE;QACV4B,QAAQ,EAAE,MAAM;QAChBhB,KAAK,EAAE,uBAAuB;QAC9B2B,SAAS,EAAE,QAAQ;QACnBJ,UAAU,EAAE;MACd,CAAE;MAAA7B,QAAA,EACC0C,MAAM,CAACK;IAAI;MAAAtC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eAGN3B,OAAA;MAAKS,KAAK,EAAE;QACVyC,SAAS,EAAE,MAAM;QACjB9B,MAAM,EAAE,KAAK;QACbV,UAAU,EAAE,uBAAuB;QACnCoB,YAAY,EAAE,KAAK;QACnBqB,QAAQ,EAAE;MACZ,CAAE;MAAApC,QAAA,eACAf,OAAA;QAAKS,KAAK,EAAE;UACVW,MAAM,EAAE,MAAM;UACdD,KAAK,EAAE,KAAK;UACZT,UAAU,EAAE+C,MAAM,CAAC5B,UAAU;UAC7BC,YAAY,EAAE,KAAK;UACnBR,SAAS,EAAEf,aAAa,GAAG,uBAAuB,GAAG;QACvD;MAAE;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN,CAAC;AAACoC,GAAA,GAvFGR,UAAU;AAyFhB,MAAMS,SAAS,gBAAAC,EAAA,cAAGrE,IAAI,CAAAsE,GAAA,GAAAD,EAAA,CAAC,CAAC;EAAEE,IAAI;EAAEC;AAAY,CAAC,KAAK;EAAAH,EAAA;EAChD,MAAM;IAAE1D;EAAc,CAAC,GAAGT,qBAAqB,CAAC,CAAC;EAEjD,MAAMuE,SAAS,GAAGxE,OAAO,CAAC,MAAM,CAC9B;IACE2C,KAAK,EAAE,aAAa;IACpBK,KAAK,EAAE,IAAI;IACXE,IAAI,EAAE,MAAM;IACZN,IAAI,EAAE,IAAI;IACVpB,KAAK,EAAE,SAAS;IAChBQ,UAAU,EAAE,2CAA2C;IACvDf,SAAS,EAAE,SAAS;IACpBmC,WAAW,EAAE;EACf,CAAC,EACD;IACET,KAAK,EAAE,cAAc;IACrBK,KAAK,EAAE,OAAO;IACdE,IAAI,EAAE,IAAI;IACVN,IAAI,EAAE,GAAG;IACTpB,KAAK,EAAE,SAAS;IAChBQ,UAAU,EAAE,2CAA2C;IACvDf,SAAS,EAAE,SAAS;IACpBmC,WAAW,EAAE;EACf,CAAC,EACD;IACET,KAAK,EAAE,aAAa;IACpBK,KAAK,EAAE,IAAI;IACXE,IAAI,EAAE,OAAO;IACbN,IAAI,EAAE,IAAI;IACVpB,KAAK,EAAE,SAAS;IAChBQ,UAAU,EAAE,2CAA2C;IACvDf,SAAS,EAAE,SAAS;IACpBmC,WAAW,EAAE;EACf,CAAC,EACD;IACET,KAAK,EAAE,cAAc;IACrBK,KAAK,EAAE,IAAI;IACXE,IAAI,EAAE,UAAU;IAChBN,IAAI,EAAE,IAAI;IACVpB,KAAK,EAAE,SAAS;IAChBQ,UAAU,EAAE,2CAA2C;IACvDf,SAAS,EAAE,SAAS;IACpBmC,WAAW,EAAE;EACf,CAAC,CACF,EAAE,EAAE,CAAC;EAEN,MAAMqB,WAAW,GAAGzE,OAAO,CAAC,MAAM,CAChC;IACE4C,IAAI,EAAE,IAAI;IACVD,KAAK,EAAE,aAAa;IACpBqB,QAAQ,EAAE,qBAAqB;IAC/BC,IAAI,EAAE,mCAAmC;IACzCL,MAAM,EAAEA,CAAA,KAAMW,WAAW,CAAC,SAAS,CAAC;IACpC/C,KAAK,EAAE,SAAS;IAChBQ,UAAU,EAAE;EACd,CAAC,EACD;IACEY,IAAI,EAAE,IAAI;IACVD,KAAK,EAAE,YAAY;IACnBqB,QAAQ,EAAE,iBAAiB;IAC3BC,IAAI,EAAE,8BAA8B;IACpCL,MAAM,EAAEA,CAAA,KAAMW,WAAW,CAAC,KAAK,CAAC;IAChC/C,KAAK,EAAE,SAAS;IAChBQ,UAAU,EAAE;EACd,CAAC,EACD;IACEY,IAAI,EAAE,IAAI;IACVD,KAAK,EAAE,eAAe;IACtBqB,QAAQ,EAAE,oBAAoB;IAC9BC,IAAI,EAAE,gCAAgC;IACtCL,MAAM,EAAEA,CAAA,KAAMW,WAAW,CAAC,QAAQ,CAAC;IACnC/C,KAAK,EAAE,SAAS;IAChBQ,UAAU,EAAE;EACd,CAAC,EACD;IACEY,IAAI,EAAE,IAAI;IACVD,KAAK,EAAE,iBAAiB;IACxBqB,QAAQ,EAAE,iBAAiB;IAC3BC,IAAI,EAAE,gCAAgC;IACtCL,MAAM,EAAEA,CAAA,KAAMW,WAAW,CAAC,WAAW,CAAC;IACtC/C,KAAK,EAAE,SAAS;IAChBQ,UAAU,EAAE;EACd,CAAC,CACF,EAAE,CAACuC,WAAW,CAAC,CAAC;EAEjB,oBACEpE,OAAA;IAAKQ,SAAS,EAAC,qBAAqB;IAAAO,QAAA,GAEjCR,aAAa,iBACZP,OAAA,CAAAE,SAAA;MAAAa,QAAA,gBACEf,OAAA;QAAKS,KAAK,EAAE;UACVO,QAAQ,EAAE,UAAU;UACpBC,GAAG,EAAE,KAAK;UACVC,IAAI,EAAE,IAAI;UACVC,KAAK,EAAE,OAAO;UACdC,MAAM,EAAE,OAAO;UACfV,UAAU,EAAE,uEAAuE;UACnFoB,YAAY,EAAE,KAAK;UACnBR,SAAS,EAAE,+BAA+B;UAC1CU,MAAM,EAAE;QACV;MAAE;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACL3B,OAAA;QAAKS,KAAK,EAAE;UACVO,QAAQ,EAAE,UAAU;UACpBC,GAAG,EAAE,KAAK;UACVW,KAAK,EAAE,KAAK;UACZT,KAAK,EAAE,OAAO;UACdC,MAAM,EAAE,OAAO;UACfV,UAAU,EAAE,wEAAwE;UACpFoB,YAAY,EAAE,KAAK;UACnBR,SAAS,EAAE,uCAAuC;UAClDU,MAAM,EAAE;QACV;MAAE;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA,eACL,CACH,eAED3B,OAAA;MAAKS,KAAK,EAAE;QAAE8D,QAAQ,EAAE,QAAQ;QAAEC,MAAM,EAAE,QAAQ;QAAExD,QAAQ,EAAE,UAAU;QAAEgB,MAAM,EAAE;MAAE,CAAE;MAAAjB,QAAA,gBAEpFf,OAAA;QAAKQ,SAAS,EAAC,gBAAgB;QAACC,KAAK,EAAE;UACrCC,UAAU,EAAE,wEAAwE;UACpFE,MAAM,EAAE,iCAAiC;UACzCC,SAAS,EAAE;QACb,CAAE;QAAAE,QAAA,GAECR,aAAa,iBACZP,OAAA,CAAAE,SAAA;UAAAa,QAAA,gBACEf,OAAA;YAAKS,KAAK,EAAE;cACVO,QAAQ,EAAE,UAAU;cACpBC,GAAG,EAAE,MAAM;cACXW,KAAK,EAAE,MAAM;cACbS,QAAQ,EAAE,MAAM;cAChBf,SAAS,EAAE;YACb,CAAE;YAAAP,QAAA,EAAC;UAAE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACX3B,OAAA;YAAKS,KAAK,EAAE;cACVO,QAAQ,EAAE,UAAU;cACpByD,MAAM,EAAE,MAAM;cACdvD,IAAI,EAAE,MAAM;cACZmB,QAAQ,EAAE,MAAM;cAChBf,SAAS,EAAE;YACb,CAAE;YAAAP,QAAA,EAAC;UAAC;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA,eACV,CACH,eAED3B,OAAA;UAAKS,KAAK,EAAE;YAAEwB,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEQ,GAAG,EAAE,MAAM;YAAE1B,QAAQ,EAAE,UAAU;YAAEgB,MAAM,EAAE;UAAE,CAAE;UAAAjB,QAAA,gBAClGf,OAAA;YAAKS,KAAK,EAAE;cACVU,KAAK,EAAE,OAAO;cACdC,MAAM,EAAE,OAAO;cACfU,YAAY,EAAE,KAAK;cACnBpB,UAAU,EAAE,2CAA2C;cACvDuB,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBE,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE,MAAM;cAClB1B,MAAM,EAAE,oCAAoC;cAC5CC,SAAS,EAAE,6BAA6B;cACxCS,SAAS,EAAEf,aAAa,GAAG,mBAAmB,GAAG;YACnD,CAAE;YAAAQ,QAAA,EACCoD,IAAI,GAAGA,IAAI,CAACO,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG;UAAI;YAAAnD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACN3B,OAAA;YAAAe,QAAA,gBACEf,OAAA;cAAIS,KAAK,EAAE;gBACT+D,MAAM,EAAE,CAAC;gBACTnC,QAAQ,EAAE,MAAM;gBAChBC,UAAU,EAAE,GAAG;gBACfF,YAAY,EAAE,MAAM;gBACpB1B,UAAU,EAAE,uCAAuC;gBACnDkE,oBAAoB,EAAE,MAAM;gBAC5BC,mBAAmB,EAAE,aAAa;gBAClClC,UAAU,EAAE;cACd,CAAE;cAAA5B,QAAA,GAAC,MACG,EAACoD,IAAI,GAAGA,IAAI,CAACO,KAAK,CAACI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,EAAC,gBACpD;YAAA;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL3B,OAAA;cAAGS,KAAK,EAAE;gBACR+D,MAAM,EAAE,CAAC;gBACTnC,QAAQ,EAAE,MAAM;gBAChBd,OAAO,EAAE,IAAI;gBACbe,UAAU,EAAE,GAAG;gBACfjB,KAAK,EAAE;cACT,CAAE;cAAAN,QAAA,EAAC;YAEH;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ3B,OAAA;cAAKS,KAAK,EAAE;gBACVyC,SAAS,EAAE,MAAM;gBACjBjB,OAAO,EAAE,MAAM;gBACfS,GAAG,EAAE;cACP,CAAE;cAAA3B,QAAA,gBACAf,OAAA;gBAAKS,KAAK,EAAE;kBACVC,UAAU,EAAE,uBAAuB;kBACnCqE,OAAO,EAAE,UAAU;kBACnBjD,YAAY,EAAE,MAAM;kBACpBO,QAAQ,EAAE,MAAM;kBAChBC,UAAU,EAAE,GAAG;kBACfjB,KAAK,EAAE;gBACT,CAAE;gBAAAN,QAAA,EAAC;cAEH;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN3B,OAAA;gBAAKS,KAAK,EAAE;kBACVC,UAAU,EAAE,uBAAuB;kBACnCqE,OAAO,EAAE,UAAU;kBACnBjD,YAAY,EAAE,MAAM;kBACpBO,QAAQ,EAAE,MAAM;kBAChBC,UAAU,EAAE,GAAG;kBACfjB,KAAK,EAAE;gBACT,CAAE;gBAAAN,QAAA,EAAC;cAEH;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN3B,OAAA;QAAKQ,SAAS,EAAC,YAAY;QAAAO,QAAA,EACxBsD,SAAS,CAACW,GAAG,CAAC,CAAC3E,IAAI,EAAEC,KAAK,kBACzBN,OAAA,CAACG,QAAQ;UAEPE,IAAI,EAAEA,IAAK;UACXC,KAAK,EAAEA,KAAM;UACbC,aAAa,EAAEA;QAAc,GAHxBF,IAAI,CAACmC,KAAK;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIhB,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN3B,OAAA;QAAKS,KAAK,EAAE;UACVC,UAAU,EAAE,wEAAwE;UACpFC,cAAc,EAAE,YAAY;UAC5BmB,YAAY,EAAE,MAAM;UACpBiD,OAAO,EAAE,MAAM;UACf3C,YAAY,EAAE,MAAM;UACpBxB,MAAM,EAAE,iCAAiC;UACzCC,SAAS,EAAE,6BAA6B;UACxCG,QAAQ,EAAE,UAAU;UACpBmC,QAAQ,EAAE;QACZ,CAAE;QAAApC,QAAA,GACCR,aAAa,iBACZP,OAAA;UAAKS,KAAK,EAAE;YACVO,QAAQ,EAAE,UAAU;YACpBC,GAAG,EAAE,QAAQ;YACbW,KAAK,EAAE,QAAQ;YACfT,KAAK,EAAE,OAAO;YACdC,MAAM,EAAE,OAAO;YACfV,UAAU,EAAE,wEAAwE;YACpFoB,YAAY,EAAE,KAAK;YACnBP,OAAO,EAAE,GAAG;YACZD,SAAS,EAAE;UACb;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CACL,eAED3B,OAAA;UAAIS,KAAK,EAAE;YACT+D,MAAM,EAAE,YAAY;YACpBnC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,GAAG;YACfjB,KAAK,EAAE,OAAO;YACduC,SAAS,EAAE,QAAQ;YACnBjB,UAAU,EAAE;UACd,CAAE;UAAA5B,QAAA,EAAC;QAEH;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEL3B,OAAA;UAAKQ,SAAS,EAAC,aAAa;UAAAO,QAAA,EACzBuD,WAAW,CAACU,GAAG,CAAC,CAACvB,MAAM,EAAEnD,KAAK,kBAC7BN,OAAA,CAACuD,UAAU;YAETE,MAAM,EAAEA,MAAO;YACflD,aAAa,EAAEA;UAAc,GAFxBkD,MAAM,CAACjB,KAAK;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGlB,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;EAAA,QAhR2B7B,qBAAqB;AAAA,EAgRhD,CAAC;EAAA,QAhR0BA,qBAAqB;AAAA,EAgR/C;AAACmF,GAAA,GAjRGjB,SAAS;AAmRfA,SAAS,CAACkB,WAAW,GAAG,WAAW;AAEnC,eAAelB,SAAS;AAAC,IAAA5D,EAAA,EAAAkD,GAAA,EAAAE,GAAA,EAAAO,GAAA,EAAAG,GAAA,EAAAe,GAAA;AAAAE,YAAA,CAAA/E,EAAA;AAAA+E,YAAA,CAAA7B,GAAA;AAAA6B,YAAA,CAAA3B,GAAA;AAAA2B,YAAA,CAAApB,GAAA;AAAAoB,YAAA,CAAAjB,GAAA;AAAAiB,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}